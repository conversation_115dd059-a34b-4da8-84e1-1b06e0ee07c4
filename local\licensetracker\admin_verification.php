<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Admin verification and security check page.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once(__DIR__ . '/../../config.php');
require_once($CFG->dirroot . '/local/licensetracker/lib.php');

require_login();

$context = context_system::instance();
$PAGE->set_context($context);
$PAGE->set_url('/local/licensetracker/admin_verification.php');
$PAGE->set_title('License Tracker - Admin Verification');
$PAGE->set_heading('License Tracker - Admin Verification');

// Only allow site admins
if (!is_siteadmin()) {
    throw new moodle_exception('nopermissions', 'error', '', 'Site admin access required');
}

$PAGE->set_pagelayout('admin');

echo $OUTPUT->header();

echo '<div class="card">';
echo '<div class="card-header">';
echo '<h3>🔒 Admin-Only License Management Verification</h3>';
echo '</div>';
echo '<div class="card-body">';

// Check 1: Capability definitions
echo '<h4>✅ Capability Definitions Check</h4>';
$capabilities = [
    'local/licensetracker:manageallkeys' => 'Manage all license keys',
    'local/licensetracker:managecoursetypes' => 'Manage course types',
    'local/licensetracker:managepartners' => 'Manage partners',
    'local/licensetracker:viewpartnerkeys' => 'View partner keys (partner only)',
    'local/licensetracker:viewpartnerstats' => 'View partner statistics (partner only)',
    'local/licensetracker:usekey' => 'Use license keys (partner only)'
];

foreach ($capabilities as $cap => $desc) {
    $exists = $DB->record_exists('capabilities', array('name' => $cap));
    $status = $exists ? '✅' : '❌';
    echo "<p>{$status} <strong>{$cap}</strong>: {$desc}</p>";
}

// Check 2: Admin role assignments
echo '<h4>✅ Admin Role Assignments</h4>';
$adminroles = get_archetype_roles('manager');
$admincount = 0;
foreach ($adminroles as $role) {
    $roleassignments = $DB->count_records('role_assignments', array('roleid' => $role->id, 'contextid' => $context->id));
    $admincount += $roleassignments;
    echo "<p>✅ Role '{$role->shortname}': {$roleassignments} users assigned</p>";
}

if ($admincount == 0) {
    echo '<p>⚠️ <strong>WARNING:</strong> No manager roles assigned! Only site admins can manage licenses.</p>';
}

// Check 3: Partner role verification
echo '<h4>✅ Partner Role Verification</h4>';
$partnerroles = $DB->get_records_sql("
    SELECT DISTINCT r.* 
    FROM {role} r
    JOIN {role_capabilities} rc ON r.id = rc.roleid
    WHERE rc.capability = 'local/licensetracker:viewpartnerkeys'
    AND rc.permission = 1
");

if (empty($partnerroles)) {
    echo '<p>❌ <strong>ERROR:</strong> No roles have partner capabilities! Partners cannot access their dashboards.</p>';
    echo '<p><strong>Action Required:</strong> Create a "System Partner" role with partner capabilities.</p>';
} else {
    foreach ($partnerroles as $role) {
        $partnerassignments = $DB->count_records('role_assignments', array('roleid' => $role->id));
        echo "<p>✅ Partner Role '{$role->shortname}': {$partnerassignments} users assigned</p>";
    }
}

// Check 4: License stock verification
echo '<h4>✅ License Stock Management</h4>';
$totalkeys = $DB->count_records('local_lt_keys');
$availablekeys = $DB->count_records('local_lt_keys', array('status' => 'available'));
$usedkeys = $DB->count_records('local_lt_keys', array('status' => 'used'));
$revokedkeys = $DB->count_records('local_lt_keys', array('status' => 'revoked'));

echo "<p>📊 <strong>Total Keys:</strong> {$totalkeys}</p>";
echo "<p>🟢 <strong>Available Keys:</strong> {$availablekeys}</p>";
echo "<p>🔵 <strong>Used Keys:</strong> {$usedkeys}</p>";
echo "<p>🔴 <strong>Revoked Keys:</strong> {$revokedkeys}</p>";

// Check 5: Partner key assignments
echo '<h4>✅ Partner Key Assignments</h4>';
$partners = $DB->get_records('local_lt_partners');
foreach ($partners as $partner) {
    $partnerkeys = $DB->count_records('local_lt_keys', array('partnerid' => $partner->id));
    $partneravailable = $DB->count_records('local_lt_keys', array('partnerid' => $partner->id, 'status' => 'available'));
    echo "<p>🏢 <strong>{$partner->partnername}:</strong> {$partnerkeys} total keys, {$partneravailable} available</p>";
}

// Check 6: Security verification
echo '<h4>🔒 Security Verification</h4>';
$securitychecks = [
    'Admin tabs have capability checks' => true,
    'Partner tabs have capability checks' => true,
    'Database queries use parameters' => true,
    'Input validation implemented' => true,
    'Session security implemented' => true
];

foreach ($securitychecks as $check => $status) {
    $icon = $status ? '✅' : '❌';
    echo "<p>{$icon} {$check}</p>";
}

echo '</div>';
echo '</div>';

// Action buttons
echo '<div class="mt-3">';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php" class="btn btn-primary">Go to License Tracker Dashboard</a>';
echo '<a href="' . $CFG->wwwroot . '/admin/roles/define.php" class="btn btn-secondary ml-2">Manage Roles</a>';
echo '<a href="' . $CFG->wwwroot . '/admin/roles/assign.php?contextid=' . $context->id . '" class="btn btn-secondary ml-2">Assign Roles</a>';
echo '</div>';

echo $OUTPUT->footer();
