<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Renderer for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\output;

defined('MOODLE_INTERNAL') || die();

use plugin_renderer_base;

/**
 * Renderer class for license tracker.
 */
class renderer extends plugin_renderer_base {

    /**
     * Render the admin dashboard.
     *
     * @param admin_dashboard $dashboard
     * @return string HTML
     */
    public function render_admin_dashboard(admin_dashboard $dashboard) {
        $data = $dashboard->export_for_template($this);
        return $this->render_from_template('local_licensetracker/admin_dashboard', $data);
    }

    /**
     * Render the partner dashboard.
     *
     * @param partner_dashboard $dashboard
     * @return string HTML
     */
    public function render_partner_dashboard(partner_dashboard $dashboard) {
        $data = $dashboard->export_for_template($this);
        return $this->render_from_template('local_licensetracker/partner_dashboard', $data);
    }

    /**
     * Render a key table.
     *
     * @param key_table $table
     * @return string HTML
     */
    public function render_key_table(key_table $table) {
        $data = $table->export_for_template($this);
        return $this->render_from_template('local_licensetracker/key_table', $data);
    }

    /**
     * Render a partner table.
     *
     * @param partner_table $table
     * @return string HTML
     */
    public function render_partner_table(partner_table $table) {
        $data = $table->export_for_template($this);
        return $this->render_from_template('local_licensetracker/partner_table', $data);
    }

    /**
     * Render a course type table.
     *
     * @param coursetype_table $table
     * @return string HTML
     */
    public function render_coursetype_table(coursetype_table $table) {
        $data = $table->export_for_template($this);
        return $this->render_from_template('local_licensetracker/coursetype_table', $data);
    }

    /**
     * Render statistics view.
     *
     * @param statistics_view $stats
     * @return string HTML
     */
    public function render_statistics_view(statistics_view $stats) {
        $data = $stats->export_for_template($this);
        return $this->render_from_template('local_licensetracker/statistics_view', $data);
    }

    /**
     * Render coming soon message.
     *
     * @return string HTML
     */
    public function render_coming_soon() {
        $data = array(
            'message' => get_string('comingsoon', 'local_licensetracker')
        );
        return $this->render_from_template('local_licensetracker/coming_soon', $data);
    }

    /**
     * Render navigation tabs.
     *
     * @param array $tabs Tab configuration
     * @param string $activetab Currently active tab
     * @return string HTML
     */
    public function render_tabs($tabs, $activetab) {
        $data = array(
            'tabs' => array(),
            'activetab' => $activetab
        );

        foreach ($tabs as $key => $tab) {
            $data['tabs'][] = array(
                'key' => $key,
                'name' => $tab['name'],
                'url' => $tab['url'],
                'active' => ($key === $activetab)
            );
        }

        return $this->render_from_template('local_licensetracker/tabs', $data);
    }
}
