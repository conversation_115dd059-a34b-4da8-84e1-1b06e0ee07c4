<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Adhoc task for importing license keys in bulk.
 *
 * @package    tool_licensetrackeradmin
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace tool_licensetrackeradmin\task;

defined('MOODLE_INTERNAL') || die();

/**
 * Adhoc task for importing license keys in bulk.
 */
class import_keys_task extends \core\task\adhoc_task {

    /**
     * Get a descriptive name for this task.
     *
     * @return string
     */
    public function get_name() {
        return get_string('bulkimportkeys', 'tool_licensetrackeradmin');
    }

    /**
     * Execute the task.
     */
    public function execute() {
        global $DB, $CFG;

        $data = $this->get_custom_data();
        
        if (!isset($data->filepath) || !isset($data->options)) {
            mtrace('Invalid task data provided');
            return;
        }

        $filepath = $data->filepath;
        $options = $data->options;
        $userid = isset($data->userid) ? $data->userid : null;

        mtrace("Starting bulk import of keys from: {$filepath}");

        if (!file_exists($filepath)) {
            mtrace("File not found: {$filepath}");
            return;
        }

        $success = 0;
        $errors = 0;
        $now = time();

        // Open CSV file
        if (($handle = fopen($filepath, "r")) !== FALSE) {
            $rownum = 0;
            
            while (($row = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $rownum++;
                
                // Skip header row if requested
                if ($rownum == 1 && !empty($options->skipfirstrow)) {
                    continue;
                }

                try {
                    // Expected columns: keystring, coursetypename, partnername, validfrom, expireson
                    if (count($row) < 3) {
                        mtrace("Row {$rownum}: Insufficient columns");
                        $errors++;
                        continue;
                    }

                    $keystring = trim($row[0]);
                    $coursetypename = trim($row[1]);
                    $partnername = trim($row[2]);
                    $validfrom = isset($row[3]) && !empty(trim($row[3])) ? strtotime(trim($row[3])) : null;
                    $expireson = isset($row[4]) && !empty(trim($row[4])) ? strtotime(trim($row[4])) : null;

                    // Validate required fields
                    if (empty($keystring) || empty($coursetypename) || empty($partnername)) {
                        mtrace("Row {$rownum}: Missing required fields");
                        $errors++;
                        continue;
                    }

                    // Check if key already exists
                    if ($DB->record_exists('local_lt_keys', array('keystring' => $keystring))) {
                        if (empty($options->updateexisting)) {
                            mtrace("Row {$rownum}: Key already exists: {$keystring}");
                            $errors++;
                            continue;
                        }
                    }

                    // Get course type ID
                    $coursetype = $DB->get_record('local_lt_coursetypes', array('name' => $coursetypename));
                    if (!$coursetype) {
                        mtrace("Row {$rownum}: Course type not found: {$coursetypename}");
                        $errors++;
                        continue;
                    }

                    // Get partner ID
                    $partner = $DB->get_record('local_lt_partners', array('partnername' => $partnername));
                    if (!$partner) {
                        mtrace("Row {$rownum}: Partner not found: {$partnername}");
                        $errors++;
                        continue;
                    }

                    // Create or update key record
                    $keydata = new \stdClass();
                    $keydata->keystring = $keystring;
                    $keydata->coursetypeid = $coursetype->id;
                    $keydata->partnerid = $partner->id;
                    $keydata->status = 'available';
                    $keydata->timemodified = $now;
                    
                    if ($validfrom) {
                        $keydata->validfrom = $validfrom;
                    }
                    
                    if ($expireson) {
                        $keydata->expireson = $expireson;
                    }

                    $existingkey = $DB->get_record('local_lt_keys', array('keystring' => $keystring));
                    if ($existingkey && !empty($options->updateexisting)) {
                        $keydata->id = $existingkey->id;
                        $DB->update_record('local_lt_keys', $keydata);
                    } else {
                        $keydata->timecreated = $now;
                        $DB->insert_record('local_lt_keys', $keydata);
                    }

                    $success++;

                    // Progress update every 100 rows
                    if ($success % 100 == 0) {
                        mtrace("Processed {$success} keys successfully");
                    }

                } catch (\Exception $e) {
                    mtrace("Row {$rownum}: Error - " . $e->getMessage());
                    $errors++;
                }
            }
            
            fclose($handle);
        }

        // Clean up temporary file
        unlink($filepath);

        mtrace("Import completed. Success: {$success}, Errors: {$errors}");

        // Send notification to user if userid provided
        if ($userid) {
            $this->send_completion_notification($userid, $success, $errors);
        }
    }

    /**
     * Send completion notification to user.
     *
     * @param int $userid User ID
     * @param int $success Number of successful imports
     * @param int $errors Number of errors
     */
    private function send_completion_notification($userid, $success, $errors) {
        global $DB;

        try {
            $user = $DB->get_record('user', array('id' => $userid));
            if (!$user) {
                return;
            }

            $subject = get_string('importcomplete', 'tool_licensetrackeradmin', 
                                array('success' => $success, 'errors' => $errors));
            $message = "Bulk key import completed.\n\n";
            $message .= "Successful imports: {$success}\n";
            $message .= "Errors: {$errors}\n";

            $messagedata = new \core\message\message();
            $messagedata->component = 'tool_licensetrackeradmin';
            $messagedata->name = 'bulkimport';
            $messagedata->userfrom = \core_user::get_noreply_user();
            $messagedata->userto = $user;
            $messagedata->subject = $subject;
            $messagedata->fullmessage = $message;
            $messagedata->fullmessageformat = FORMAT_PLAIN;
            $messagedata->fullmessagehtml = nl2br($message);
            $messagedata->smallmessage = $subject;

            message_send($messagedata);

        } catch (\Exception $e) {
            mtrace("Error sending notification: " . $e->getMessage());
        }
    }
}
