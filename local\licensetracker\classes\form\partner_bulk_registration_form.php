<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Partner bulk registration form for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\form;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/formslib.php');

/**
 * Form for partners to bulk register students using CSV upload.
 */
class partner_bulk_registration_form extends \moodleform {

    /**
     * Define the form.
     */
    public function definition() {
        $mform = $this->_form;

        // CSV file upload
        $mform->addElement('header', 'csvupload', get_string('csvupload', 'local_licensetracker'));
        
        $mform->addElement('filepicker', 'csvfile', get_string('csvfile', 'local_licensetracker'), null,
                          array('accepted_types' => array('.csv')));
        $mform->addRule('csvfile', null, 'required', null, 'client');
        $mform->addHelpButton('csvfile', 'csvfile', 'local_licensetracker');

        // Processing options
        $mform->addElement('header', 'options', get_string('options', 'local_licensetracker'));
        
        $mform->addElement('advcheckbox', 'skipfirsterror', get_string('skipfirsterror', 'local_licensetracker'),
                          get_string('skipfirsterror_desc', 'local_licensetracker'));
        $mform->setDefault('skipfirsterror', 1);
        
        $mform->addElement('text', 'maxprocessing', get_string('maxprocessing', 'local_licensetracker'));
        $mform->setType('maxprocessing', PARAM_INT);
        $mform->setDefault('maxprocessing', 100);
        $mform->addRule('maxprocessing', null, 'numeric', null, 'client');
        $mform->addHelpButton('maxprocessing', 'maxprocessing', 'local_licensetracker');

        // Action buttons
        $this->add_action_buttons(true, get_string('processbulkregistration', 'local_licensetracker'));
    }

    /**
     * Validate the form data.
     *
     * @param array $data Form data
     * @param array $files Form files
     * @return array Validation errors
     */
    public function validation($data, $files) {
        $errors = parent::validation($data, $files);

        // Validate max processing limit
        if (!empty($data['maxprocessing'])) {
            if ($data['maxprocessing'] < 1 || $data['maxprocessing'] > 1000) {
                $errors['maxprocessing'] = get_string('error:invalidmaxprocessing', 'local_licensetracker');
            }
        }

        return $errors;
    }
}
