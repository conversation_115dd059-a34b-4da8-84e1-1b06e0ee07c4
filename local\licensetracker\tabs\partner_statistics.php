<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Partner statistics tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

// CRITICAL: Verify partner permissions
$context = context_system::instance();
require_capability('local/licensetracker:viewpartnerstats', $context);

// Ensure partner can only see their own stats
if (!$partner) {
    throw new moodle_exception('nopermissions', 'error', '', 'Partner access required');
}

// Get partner statistics
$stats = local_licensetracker_get_partner_stats($partner->id);

// Get course type breakdown with license prefixes
$sql = "SELECT ct.name, ct.licenseprefix,
               COUNT(*) as total,
               SUM(CASE WHEN k.status = 'available' THEN 1 ELSE 0 END) as available,
               SUM(CASE WHEN k.status = 'used' THEN 1 ELSE 0 END) as used,
               SUM(CASE WHEN k.status = 'revoked' THEN 1 ELSE 0 END) as revoked
        FROM {local_lt_keys} k
        JOIN {local_lt_coursetypes} ct ON k.coursetypeid = ct.id
        WHERE k.partnerid = ?
        GROUP BY ct.id, ct.name, ct.licenseprefix
        ORDER BY ct.name";

$coursetypestats = $DB->get_records_sql($sql, array($partner->id));

$output = '';

// Overall statistics
$output .= '<div class="row mb-4">';
$output .= '<div class="col-md-3">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('totalkeys', 'local_licensetracker') . '</h5>';
$output .= '<h2 class="text-primary">' . $stats->total . '</h2>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

$output .= '<div class="col-md-3">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('availablekeys', 'local_licensetracker') . '</h5>';
$output .= '<h2 class="text-success">' . $stats->available . '</h2>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

$output .= '<div class="col-md-3">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('usedkeys', 'local_licensetracker') . '</h5>';
$output .= '<h2 class="text-info">' . $stats->used . '</h2>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

$output .= '<div class="col-md-3">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('revokedkeys', 'local_licensetracker') . '</h5>';
$output .= '<h2 class="text-danger">' . $stats->revoked . '</h2>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

// Course type breakdown
if (!empty($coursetypestats)) {
    $output .= '<div class="card">';
    $output .= '<div class="card-header">';
    $output .= '<h4>' . get_string('coursetypebreakdown', 'local_licensetracker') . '</h4>';
    $output .= '</div>';
    $output .= '<div class="card-body">';
    $output .= '<div class="table-responsive">';
    $output .= '<table class="table table-striped">';
    $output .= '<thead>';
    $output .= '<tr>';
    $output .= '<th>' . get_string('coursetype', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('licenseprefix', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('totalkeys', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('availablekeys', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('usedkeys', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('revokedkeys', 'local_licensetracker') . '</th>';
    $output .= '</tr>';
    $output .= '</thead>';
    $output .= '<tbody>';

    foreach ($coursetypestats as $ctstat) {
        $output .= '<tr>';
        $output .= '<td>' . $ctstat->name . '</td>';
        $output .= '<td><span class="badge badge-primary">' . $ctstat->licenseprefix . '</span></td>';
        $output .= '<td>' . $ctstat->total . '</td>';
        $output .= '<td><span class="badge badge-success">' . $ctstat->available . '</span></td>';
        $output .= '<td><span class="badge badge-info">' . $ctstat->used . '</span></td>';
        $output .= '<td><span class="badge badge-danger">' . $ctstat->revoked . '</span></td>';
        $output .= '</tr>';
    }
    
    $output .= '</tbody>';
    $output .= '</table>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
}

return $output;
