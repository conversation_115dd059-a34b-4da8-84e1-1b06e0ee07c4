<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Course type form for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\form;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/formslib.php');

/**
 * Form for adding/editing course types.
 */
class coursetype_form extends \moodleform {

    /**
     * Define the form.
     */
    public function definition() {
        global $DB;

        $mform = $this->_form;

        // Hidden ID field for editing
        $mform->addElement('hidden', 'id');
        $mform->setType('id', PARAM_INT);

        // Course type name
        $mform->addElement('text', 'name', get_string('coursetypename', 'local_licensetracker'));
        $mform->setType('name', PARAM_TEXT);
        $mform->addRule('name', null, 'required', null, 'client');
        $mform->addRule('name', null, 'maxlength', 255, 'client');

        // Description
        $mform->addElement('textarea', 'description', get_string('description', 'local_licensetracker'));
        $mform->setType('description', PARAM_TEXT);

        // Default price
        $mform->addElement('text', 'defaultprice', get_string('defaultprice', 'local_licensetracker'));
        $mform->setType('defaultprice', PARAM_FLOAT);
        $mform->addRule('defaultprice', null, 'required', null, 'client');
        $mform->addRule('defaultprice', null, 'numeric', null, 'client');
        $mform->setDefault('defaultprice', 0.00);

        // License prefix
        $mform->addElement('text', 'licenseprefix', get_string('licenseprefix', 'local_licensetracker'));
        $mform->setType('licenseprefix', PARAM_ALPHANUMEXT);
        $mform->addRule('licenseprefix', null, 'required', null, 'client');
        $mform->addRule('licenseprefix', null, 'maxlength', 10, 'client');
        $mform->addHelpButton('licenseprefix', 'licenseprefix', 'local_licensetracker');

        // Moodle course selection
        $courses = $DB->get_records_menu('course', array('visible' => 1), 'fullname', 'id, fullname');
        unset($courses[SITEID]); // Remove site course
        $courses = array('' => get_string('selectacourse', 'core')) + $courses;
        $mform->addElement('select', 'moodlecourseid', get_string('moodlecourse', 'local_licensetracker'), $courses);
        $mform->addRule('moodlecourseid', null, 'required', null, 'client');

        // Enrollment method
        $enrolmethods = array(
            '' => get_string('choose'),
            'self' => get_string('pluginname', 'enrol_self'),
            'manual' => get_string('pluginname', 'enrol_manual')
        );
        $mform->addElement('select', 'enrolmethod', get_string('enrolmethod', 'local_licensetracker'), $enrolmethods);
        $mform->addRule('enrolmethod', null, 'required', null, 'client');

        // Default role
        $roles = $DB->get_records_menu('role', null, 'sortorder', 'id, shortname');
        $roles = array('' => get_string('chooserole', 'core')) + $roles;
        $mform->addElement('select', 'defaultroleid', get_string('defaultrole', 'local_licensetracker'), $roles);
        $mform->addRule('defaultroleid', null, 'required', null, 'client');

        // Action buttons
        $this->add_action_buttons(true, get_string('save', 'local_licensetracker'));
    }

    /**
     * Validate the form data.
     *
     * @param array $data Form data
     * @param array $files Form files
     * @return array Validation errors
     */
    public function validation($data, $files) {
        global $DB;
        
        $errors = parent::validation($data, $files);

        // Check for duplicate course type name
        if (!empty($data['name'])) {
            $conditions = array('name' => $data['name']);
            if (!empty($data['id'])) {
                $sql = "SELECT id FROM {local_lt_coursetypes} WHERE name = ? AND id != ?";
                $params = array($data['name'], $data['id']);
                $exists = $DB->get_record_sql($sql, $params);
            } else {
                $exists = $DB->record_exists('local_lt_coursetypes', $conditions);
            }

            if ($exists) {
                $errors['name'] = get_string('error:coursetypeexists', 'local_licensetracker');
            }
        }

        // Check for duplicate license prefix
        if (!empty($data['licenseprefix'])) {
            $prefix = strtoupper($data['licenseprefix']);
            $conditions = array('licenseprefix' => $prefix);
            if (!empty($data['id'])) {
                $sql = "SELECT id FROM {local_lt_coursetypes} WHERE licenseprefix = ? AND id != ?";
                $params = array($prefix, $data['id']);
                $exists = $DB->get_record_sql($sql, $params);
            } else {
                $exists = $DB->record_exists('local_lt_coursetypes', $conditions);
            }

            if ($exists) {
                $errors['licenseprefix'] = get_string('error:licenseprefixexists', 'local_licensetracker');
            }

            // Validate prefix format (only alphanumeric)
            if (!preg_match('/^[A-Z0-9]+$/', $prefix)) {
                $errors['licenseprefix'] = get_string('error:invalidlicenseprefix', 'local_licensetracker');
            }
        }

        // Validate price
        if (isset($data['defaultprice']) && $data['defaultprice'] < 0) {
            $errors['defaultprice'] = get_string('error:negativeprice', 'local_licensetracker');
        }

        return $errors;
    }
}
