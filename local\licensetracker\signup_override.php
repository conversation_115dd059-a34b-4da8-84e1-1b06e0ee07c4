<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Custom signup page with license key validation.
 * This file should replace or be called from the main signup page.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once(__DIR__ . '/../../config.php');
require_once($CFG->dirroot . '/login/signup_form.php');
require_once($CFG->dirroot . '/local/licensetracker/lib.php');

// Check if self registration is enabled
if (empty($CFG->registerauth)) {
    throw new \moodle_exception('notlocalisederrormessage', 'error', '', 'Sorry, you may not use this page.');
}

$authplugin = get_auth_plugin('licensetracker');
if (!$authplugin->can_signup()) {
    throw new \moodle_exception('notlocalisederrormessage', 'error', '', 'Sorry, you may not use this page.');
}

// Set up the page
$PAGE->set_url('/local/licensetracker/signup_override.php');
$PAGE->set_context(context_system::instance());
$PAGE->set_title(get_string('createuseraccount'));
$PAGE->set_heading(get_string('createuseraccount'));

// Create the signup form
$mform = new login_signup_form();

if ($mform->is_cancelled()) {
    redirect(get_login_url());
} else if ($user = $mform->get_data()) {
    
    // Additional license key validation
    $config = get_config('auth_licensetracker');
    $requireskey = false;
    
    if (!empty($config->enabled)) {
        if (!empty($config->requirekeyforall)) {
            $requireskey = true;
        } else if (!empty($config->localcountries)) {
            $localcountries = array_map('trim', explode(',', $config->localcountries));
            if (!in_array($user->country, $localcountries)) {
                $requireskey = true;
            }
        } else {
            $requireskey = true;
        }
    }
    
    if ($requireskey) {
        if (empty($user->licensekey)) {
            $mform->set_data($user);
            echo $OUTPUT->header();
            echo $OUTPUT->notification(get_string('licensekey_required', 'auth_licensetracker'), 'error');
            $mform->display();
            echo $OUTPUT->footer();
            exit;
        }
        
        // Validate the license key
        $key = local_licensetracker_validate_key($user->licensekey);
        if (!$key) {
            $mform->set_data($user);
            echo $OUTPUT->header();
            echo $OUTPUT->notification(get_string('invalidkey', 'auth_licensetracker'), 'error');
            $mform->display();
            echo $OUTPUT->footer();
            exit;
        }
        
        // Store validated key in session
        $SESSION->licensetracker_validated_key = $key->id;
        $user->auth = 'licensetracker'; // Set auth method for tracking
    }
    
    // Create the user
    $user->confirmed = 1;
    $user->lang = current_language();
    $user->firstaccess = time();
    $user->timecreated = time();
    $user->mnethostid = $CFG->mnet_localhost_id;
    
    $user->id = user_create_user($user, false, false);
    
    if (!$user->id) {
        throw new \moodle_exception('cannotcreateuser');
    }
    
    // Trigger user created event (this will call our observer)
    \core\event\user_created::create_from_userid($user->id)->trigger();
    
    // Complete login
    complete_user_login($user);
    
    // Redirect to course or dashboard
    if (!empty($SESSION->wantsurl)) {
        $urltogo = $SESSION->wantsurl;
        unset($SESSION->wantsurl);
    } else {
        $urltogo = $CFG->wwwroot.'/';
    }
    
    redirect($urltogo, get_string('registrationsuccess', 'auth_licensetracker'), null, \core\output\notification::NOTIFY_SUCCESS);
}

// Display the form
echo $OUTPUT->header();
$mform->display();
echo $OUTPUT->footer();
