# Moodle License Key Management System

A comprehensive license key management system for Moodle consisting of three interconnected plugins:

1. **local_licensetracker** - Core license key management with admin/partner dashboards
2. **auth_licensetracker** - Authentication plugin for license key validation during registration  
3. **tool_licensetrackeradmin** - Advanced admin tools for bulk operations and API access

## Features

### Core Features (local_licensetracker)
- **License Key Management**: Generate, track, and manage license keys with configurable formats
- **Partner Management**: Create and manage partners with specific course type assignments
- **Course Type Management**: Define course types with enrollment methods and default roles
- **Admin Dashboard**: Comprehensive interface for administrators with 6 tabs:
  - Keys: View and manage all license keys with filtering
  - Generate Keys: Create new keys individually or in bulk
  - Partners: Manage partner accounts and assignments
  - Course Types: Configure course types and enrollment settings
  - Statistics: View system-wide usage statistics
  - Purchase: Placeholder for future e-commerce integration
- **Partner Dashboard**: Restricted interface for partners with 4 tabs:
  - Assigned Keys: View keys assigned to their organization
  - Statistics: Partner-specific usage statistics
  - Register New Student: Direct student registration using license keys
  - Purchase: Placeholder for future partner purchasing

### Authentication Features (auth_licensetracker)
- **Registration Integration**: Adds license key field to Moodle's self-registration form
- **Key Validation**: Comprehensive validation including format, availability, and date ranges
- **Automatic Enrollment**: Enrolls users in courses based on key's course type configuration
- **Partner Language Support**: Sets user language preference based on partner settings
- **Flexible Requirements**: Configurable to require keys for all users or specific countries

### Advanced Admin Tools (tool_licensetrackeradmin)
- **Bulk Import/Export**: CSV-based bulk operations for keys and partners
- **Activity Logging**: Detailed audit trail of all license key activities
- **Configurable Key Format**: JSON-based key format configuration
- **API Access**: RESTful API for external system integration
- **Asynchronous Processing**: Background tasks for large operations

## Installation

### Prerequisites
- Moodle 4.0 or higher
- PHP 7.4 or higher
- MySQL 5.7 or PostgreSQL 10 or higher

### ⚠️ CRITICAL: This System ENFORCES License Requirements
**WARNING**: Once installed and configured, this system will **BLOCK ALL STUDENT REGISTRATION** without valid license keys (unless configured for local students). Make sure you understand the configuration before enabling!

### Installation Steps

1. **Download and Extract**
   ```bash
   # Extract plugins to appropriate directories
   cp -r local_licensetracker /path/to/moodle/local/
   cp -r auth_licensetracker /path/to/moodle/auth/
   cp -r tool_licensetrackeradmin /path/to/moodle/admin/tool/
   ```

2. **Install Plugins**
   - Log in to Moodle as administrator
   - Navigate to Site Administration → Notifications
   - Follow the installation prompts for all three plugins

3. **Configure Authentication**
   - Go to Site Administration → Plugins → Authentication → Manage authentication
   - Enable "License Tracker Authentication"
   - Configure settings as needed

4. **Create System Partner Role**
   - Go to Site Administration → Users → Permissions → Define roles
   - Create a new role called "System Partner"
   - Assign the following capabilities:
     - `local/licensetracker:viewpartnerkeys`
     - `local/licensetracker:usekey`
     - `local/licensetracker:viewpartnerstats`

5. **CRITICAL: Configure License Enforcement**
   - Go to Site Administration → Plugins → Local plugins → License Tracker Settings
   - Configure these CRITICAL settings:
     - **Enabled**: Enable/disable the entire system
     - **Enforce license for all students**: If checked, ALL students need licenses
     - **Local countries**: Countries that don't need licenses (e.g., "US,CA,GB")
   - **WARNING**: Test thoroughly before enabling in production!

6. **Initial Setup**
   - Navigate to Site Administration → Local plugins → License Tracker
   - Create your first course types (link to Moodle courses)
   - Add partners and assign them to Moodle users
   - Generate initial license keys

7. **MANDATORY: Replace Default Registration (CRITICAL)**
   - **Option A**: Redirect default signup to license tracker
     - Edit `/login/signup.php` and add at the top:
     ```php
     // Redirect to license tracker signup if enabled
     if (get_config('local_licensetracker', 'enabled')) {
         redirect(new moodle_url('/local/licensetracker/signup_override.php'));
     }
     ```
   - **Option B**: Disable self-registration and use partner registration only
     - Go to Site Administration → Plugins → Authentication → Manage authentication
     - Disable "Email-based self-registration"
     - Partners will register students through their dashboard

## Configuration

### Key Format Configuration
Configure license key format in Site Administration → Plugins → Admin tools → License Tracker Admin Tools → Settings:

```json
{
  "segments": 4,
  "segment_length": 4,
  "separator": "-",
  "charset": "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
}
```

### Authentication Settings
Configure in Site Administration → Plugins → Authentication → License Tracker Authentication:
- **Enable license key authentication**: Enable/disable the plugin
- **Require key for all students**: Whether all students need keys
- **Local countries**: Countries that don't require keys (if not requiring for all)

## Usage

### For Administrators

1. **Create Course Types**
   - Define course types with associated Moodle courses
   - Set enrollment methods and default roles
   - Configure pricing information

2. **Manage Partners**
   - Add partner organizations
   - Link partners to Moodle user accounts
   - Assign course types with custom pricing

3. **Generate License Keys**
   - Create keys individually or in bulk
   - Set validity date ranges
   - Associate with specific partners and course types

4. **Monitor Usage**
   - View comprehensive statistics
   - Track key usage and partner activity
   - Export data for reporting

### For Partners

1. **View Assigned Keys**
   - See all keys assigned to your organization
   - Filter by course type and status
   - Monitor usage statistics

2. **Register Students**
   - Use the built-in registration form
   - Validate license keys in real-time
   - Automatically enroll students in courses

### For Students

1. **Self-Registration**
   - Access Moodle's registration page
   - Enter license key when prompted
   - Complete registration and get automatically enrolled

## API Access

The system provides RESTful API endpoints for external integration:

### Available Endpoints
- `tool_licensetrackeradmin_generate_keys`: Generate new license keys
- `tool_licensetrackeradmin_check_key_status`: Check key validity and status
- `tool_licensetrackeradmin_use_key`: Mark a key as used and enroll user

### Authentication
API access requires proper Moodle web service authentication and appropriate capabilities.

## Database Schema

### Core Tables
- `local_licensetracker_keys`: License key records
- `local_licensetracker_partners`: Partner information
- `local_licensetracker_coursetypes`: Course type definitions
- `local_licensetracker_partner_coursetypes`: Partner-course type assignments

### Key Fields
- **Keys**: keystring, status, validfrom, expireson, usage tracking
- **Partners**: name, contact info, language preferences, Moodle user links
- **Course Types**: enrollment methods, default roles, pricing

## Security Considerations

- All database operations use Moodle's database abstraction layer
- Capability checks enforce proper permissions
- License keys are validated for format, availability, and date ranges
- IP addresses and user agents are logged for audit purposes
- Bulk operations are processed asynchronously to prevent timeouts

## Troubleshooting

### Common Issues

1. **Keys not validating during registration**
   - Check auth plugin is enabled
   - Verify key format configuration
   - Ensure key status is 'available'
   - Check validity date ranges

2. **Partner dashboard not showing**
   - Verify user has System Partner role
   - Check partner record exists and is linked to user
   - Confirm proper capabilities are assigned

3. **Bulk operations failing**
   - Check file format and permissions
   - Verify CSV column headers match expected format
   - Monitor task queue for processing status

### Debug Mode
Enable Moodle debugging to see detailed error messages:
- Site Administration → Development → Debugging
- Set to "DEVELOPER" level for detailed logs

## Support

For issues and feature requests, please check:
1. Plugin documentation
2. Moodle forums
3. Plugin issue tracker

## License

This plugin is licensed under the GNU GPL v3 or later.

## Version History

- **1.0.0** (2025-06-28): Initial release with full feature set
