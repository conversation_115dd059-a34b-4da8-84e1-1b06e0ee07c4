# 👨‍💼 **ADMINISTRATOR MANUAL - FRANCHISE PARTNER LICENSE SYSTEM**

## 📋 **ADMINISTRATOR OVERVIEW**

As a system administrator, you have complete control over:
- **Partner Management** - Create and manage franchise partners
- **License Key Generation** - Generate keys for partners
- **Course Type Configuration** - Set up course types with prefixes
- **System Analytics** - Monitor usage and performance
- **User Management** - Register users manually when needed

---

## 🏠 **ADMIN DASHBOARD ACCESS**

### **Access URL**
- **Main Dashboard**: `/local/licensetracker/`
- **System Validation**: `/local/licensetracker/system_validation.php`
- **Security Audit**: `/local/licensetracker/security_audit.php`

### **Required Permissions**
You need these capabilities:
- ✅ `local/licensetracker:manageallkeys`
- ✅ `local/licensetracker:managepartners`
- ✅ `local/licensetracker:managecoursetypes`

---

## 📊 **DASHBOARD OVERVIEW TAB**

### **System Health Summary**
- **System Status** - Operational/Maintenance mode
- **Critical Issues** - Partners with no stock, missing prefixes
- **Quick Actions** - Run validation, view security audit

### **Key Statistics Cards**
- **Total License Keys** - All keys in system
- **Available Keys** - Keys ready for use
- **Used Keys** - Keys already consumed
- **Active Partners** - Partners with available stock

### **Partner Analytics Table**
For each partner, you can see:
- **Partner Name** and contact email
- **Course Types** assigned
- **Total/Available/Used Keys**
- **Last Activity** date
- **Usage Percentage**
- **Stock Status** (Active/Low Stock/Out of Stock)

---

## 🔑 **GENERATE KEYS TAB**

### **Key Generation Form**
1. **Select Partner** - Choose which partner gets the keys
2. **Select Course Type** - Determines the license prefix
3. **Quantity** - Number of keys to generate (1-1000)
4. **Valid From** - Optional start date
5. **Expires On** - Optional expiration date

### **Key Generation Best Practices**
- **Generate in batches** of 100-500 keys
- **Set expiration dates** for time-limited courses
- **Monitor partner stock** before generating
- **Keep audit trail** of all generations

### **Key Format**
Keys are generated with course type prefixes:
- **Cyber Security**: `CHCSS-1234-ABCD-5678`
- **AI Course**: `AI-9876-EFGH-5432`
- **Custom format**: Based on course type configuration

---

## 🤝 **PARTNERS TAB**

### **Partner Management**
**Add New Partner:**
1. **Partner Name** - Organization name
2. **Contact Email** - Primary contact
3. **Country** - Partner location
4. **Language** - Default language for their students
5. **Moodle User** - Link to existing user account

**Edit Existing Partner:**
- Update contact information
- Change linked Moodle user
- Modify language preferences
- View partner statistics

### **Partner-Course Type Assignments**
For each partner, configure:
- **Assigned Course Types** - Which courses they can sell
- **Custom Pricing** - Partner-specific prices (optional)
- **Stock Limits** - Maximum keys per course type

### **Partner Role Assignment**
**CRITICAL**: After creating a partner:
1. **Go to**: Site Administration → Users → Permissions → Assign system roles
2. **Select**: System Partner role
3. **Assign**: The Moodle user linked to the partner

---

## 🎓 **COURSE TYPES TAB**

### **Course Type Configuration**
**Add New Course Type:**
1. **Name** - Course type name (e.g., "Cyber Security Certification")
2. **License Prefix** - 3-5 character prefix (e.g., "CHCSS")
3. **Moodle Course** - Link to actual Moodle course
4. **Enrollment Method** - How students are enrolled (manual/self)
5. **Default Role** - Role assigned to students (usually "Student")
6. **Default Price** - Base price for this course type

### **License Prefix Rules**
- **3-5 characters** maximum
- **Uppercase letters and numbers** only
- **Must be unique** across all course types
- **Examples**: CHCSS, AI, PROJ, MGMT, TECH

### **Enrollment Methods**
- **Manual** - Admin/teacher enrolls students
- **Self** - Students auto-enroll with license key
- **Cohort** - Enroll via cohort membership

---

## 📈 **STATISTICS TAB**

### **System-Wide Statistics**
- **Total Keys Generated** - All time
- **Keys by Status** - Available/Used/Revoked/Expired
- **Partner Breakdown** - Usage by partner
- **Course Type Breakdown** - Usage by course type

### **Partner Performance Metrics**
- **Top Performing Partners** - Highest key usage
- **Low Activity Partners** - Partners with unused stock
- **Recent Activity** - Latest key usage events

### **Usage Trends**
- **Daily/Weekly/Monthly** key usage
- **Peak usage periods**
- **Growth trends** by partner and course type

---

## 👤 **REGISTER USER TAB**

### **Manual User Registration**
When you need to register users manually:

1. **User Type Selection**:
   - **Local Student** - Regular student (no license key required)
   - **Partner Student** - Student registered by partner (requires license key)

2. **User Information**:
   - Username, email, password
   - First name, last name
   - Country, city

3. **License Key** (for partner students):
   - Must be valid and available
   - Must belong to the selected partner
   - Will be marked as used after registration

### **When to Use Manual Registration**
- **Emergency registrations**
- **Special cases** not handled by partners
- **Testing purposes**
- **Administrative overrides**

---

## 🔧 **SYSTEM MAINTENANCE**

### **Daily Tasks**
- **Monitor system health** via dashboard
- **Check partner stock levels**
- **Review recent activity logs**
- **Respond to partner requests**

### **Weekly Tasks**
- **Generate new license keys** as needed
- **Review partner performance**
- **Check for system updates**
- **Backup license key data**

### **Monthly Tasks**
- **Analyze usage trends**
- **Review partner agreements**
- **Clean up expired keys**
- **Performance optimization**

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues**

**Partners Can't Access Dashboard:**
- ✅ Check System Partner role assignment
- ✅ Verify partner is linked to Moodle user
- ✅ Confirm capabilities are assigned

**License Keys Not Working:**
- ✅ Check key status (available/used/expired)
- ✅ Verify course type assignment
- ✅ Confirm partner has access to course type

**Students Can't Register:**
- ✅ Verify license key is valid
- ✅ Check partner stock levels
- ✅ Confirm course enrollment settings

### **System Validation Tools**
- **System Validation**: `/local/licensetracker/system_validation.php`
- **Security Audit**: `/local/licensetracker/security_audit.php`
- **Partner Workflow Test**: `/local/licensetracker/test_partner_workflow.php`

---

## 📞 **SUPPORT RESOURCES**

### **System Health Monitoring**
- **Dashboard Alerts** - Critical issues highlighted
- **Email Notifications** - System events (if configured)
- **Log Files** - Detailed activity logs

### **Partner Support**
- **Partner Manual** - Provide to partners
- **Training Materials** - How to use the system
- **Support Contact** - Your contact information

### **Technical Support**
- **System Documentation** - Complete technical docs
- **API Documentation** - For external integrations
- **Database Schema** - For advanced troubleshooting

---

## 🎯 **BEST PRACTICES**

### **Security**
- ✅ **Regular backups** of license key data
- ✅ **Monitor partner activity** for unusual patterns
- ✅ **Keep system updated** with latest patches
- ✅ **Review partner permissions** regularly

### **Performance**
- ✅ **Monitor key generation** performance
- ✅ **Archive old keys** periodically
- ✅ **Optimize database** queries
- ✅ **Use caching** where appropriate

### **Partner Management**
- ✅ **Regular communication** with partners
- ✅ **Proactive stock monitoring**
- ✅ **Clear documentation** and training
- ✅ **Responsive support** for issues

**Your franchise partner system is now fully operational and ready for production use!** 🚀
