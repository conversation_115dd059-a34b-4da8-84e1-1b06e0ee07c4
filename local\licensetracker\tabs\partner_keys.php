<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Partner keys tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

// CRITICAL: Verify partner permissions
$context = context_system::instance();
require_capability('local/licensetracker:viewpartnerkeys', $context);

// Ensure partner can only see their own keys
if (!$partner) {
    throw new moodle_exception('nopermissions', 'error', '', 'Partner access required');
}

// Get filters
$coursetypefilter = optional_param('coursetype', 0, PARAM_INT);
$statusfilter = optional_param('status', '', PARAM_ALPHA);

$filters = array('partner' => $partner->id);
if ($coursetypefilter) {
    $filters['coursetype'] = $coursetypefilter;
}
if ($statusfilter) {
    $filters['status'] = $statusfilter;
}

// Get keys for this partner
$keys = local_licensetracker_get_keys($filters);

// Build filter form
$filterform = '';
$filterform .= '<div class="card mb-3">';
$filterform .= '<div class="card-header">' . get_string('filters', 'core') . '</div>';
$filterform .= '<div class="card-body">';
$filterform .= '<form method="get" action="' . $CFG->wwwroot . '/local/licensetracker/index.php">';
$filterform .= '<input type="hidden" name="tab" value="assignedkeys">';
$filterform .= '<div class="row">';

// Course type filter
$sql = "SELECT DISTINCT ct.id, ct.name
        FROM {local_lt_coursetypes} ct
        JOIN {local_lt_keys} k ON k.coursetypeid = ct.id
        WHERE k.partnerid = ?
        ORDER BY ct.name";
$coursetypes = $DB->get_records_sql_menu($sql, array($partner->id));

$filterform .= '<div class="col-md-4">';
$filterform .= '<label for="coursetype">' . get_string('coursetype', 'local_licensetracker') . '</label>';
$filterform .= '<select name="coursetype" id="coursetype" class="form-control">';
$filterform .= '<option value="">' . get_string('all') . '</option>';
foreach ($coursetypes as $id => $name) {
    $selected = ($coursetypefilter == $id) ? 'selected' : '';
    $filterform .= '<option value="' . $id . '" ' . $selected . '>' . $name . '</option>';
}
$filterform .= '</select>';
$filterform .= '</div>';

// Status filter
$statuses = array('available', 'used', 'revoked');
$filterform .= '<div class="col-md-4">';
$filterform .= '<label for="status">' . get_string('status', 'local_licensetracker') . '</label>';
$filterform .= '<select name="status" id="status" class="form-control">';
$filterform .= '<option value="">' . get_string('all') . '</option>';
foreach ($statuses as $status) {
    $selected = ($statusfilter == $status) ? 'selected' : '';
    $filterform .= '<option value="' . $status . '" ' . $selected . '>' . get_string($status, 'local_licensetracker') . '</option>';
}
$filterform .= '</select>';
$filterform .= '</div>';

$filterform .= '<div class="col-md-4 d-flex align-items-end">';
$filterform .= '<button type="submit" class="btn btn-primary mr-2">' . get_string('filter', 'core') . '</button>';
$filterform .= '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php?tab=assignedkeys" class="btn btn-secondary">' . get_string('clear', 'core') . '</a>';
$filterform .= '</div>';

$filterform .= '</div>';
$filterform .= '</form>';
$filterform .= '</div>';
$filterform .= '</div>';

// Build keys table
$keytable = new \local_licensetracker\output\key_table($keys, false, $filters);
$keystablehtml = $renderer->render_key_table($keytable);

return $filterform . $keystablehtml;
