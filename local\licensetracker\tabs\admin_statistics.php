<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Admin statistics tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

// CRITICAL: Verify admin permissions
$context = context_system::instance();
require_capability('local/licensetracker:manageallkeys', $context);

// Get overall statistics
$stats = local_licensetracker_get_partner_stats(); // Without partner ID gets all stats

// Get partner breakdown
$sql = "SELECT p.partnername,
               COUNT(*) as total,
               SUM(CASE WHEN k.status = 'available' THEN 1 ELSE 0 END) as available,
               SUM(CASE WHEN k.status = 'used' THEN 1 ELSE 0 END) as used,
               SUM(CASE WHEN k.status = 'revoked' THEN 1 ELSE 0 END) as revoked
        FROM {local_lt_keys} k
        JOIN {local_lt_partners} p ON k.partnerid = p.id
        GROUP BY p.id, p.partnername
        ORDER BY p.partnername";

$partnerstats = $DB->get_records_sql($sql);

// Get course type breakdown
$sql = "SELECT ct.name,
               COUNT(*) as total,
               SUM(CASE WHEN k.status = 'available' THEN 1 ELSE 0 END) as available,
               SUM(CASE WHEN k.status = 'used' THEN 1 ELSE 0 END) as used,
               SUM(CASE WHEN k.status = 'revoked' THEN 1 ELSE 0 END) as revoked
        FROM {local_lt_keys} k
        JOIN {local_lt_coursetypes} ct ON k.coursetypeid = ct.id
        GROUP BY ct.id, ct.name
        ORDER BY ct.name";

$coursetypestats = $DB->get_records_sql($sql);

// Get system counts
$partnercount = $DB->count_records('local_lt_partners');
$coursetypecount = $DB->count_records('local_lt_coursetypes');

$output = '';

// Include Chart.js for charts
$PAGE->requires->js_call_amd('core/chartjs', 'init');

// Overall statistics
$output .= '<div class="row mb-4">';
$output .= '<div class="col-md-2">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('totalkeys', 'local_licensetracker') . '</h5>';
$output .= '<h2 class="text-primary">' . $stats->total . '</h2>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

$output .= '<div class="col-md-2">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('availablekeys', 'local_licensetracker') . '</h5>';
$output .= '<h2 class="text-success">' . $stats->available . '</h2>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

$output .= '<div class="col-md-2">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('usedkeys', 'local_licensetracker') . '</h5>';
$output .= '<h2 class="text-info">' . $stats->used . '</h2>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

$output .= '<div class="col-md-2">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('revokedkeys', 'local_licensetracker') . '</h5>';
$output .= '<h2 class="text-danger">' . $stats->revoked . '</h2>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

$output .= '<div class="col-md-2">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('totalpartners', 'local_licensetracker') . '</h5>';
$output .= '<h2 class="text-secondary">' . $partnercount . '</h2>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

$output .= '<div class="col-md-2">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('totalcoursetypes', 'local_licensetracker') . '</h5>';
$output .= '<h2 class="text-secondary">' . $coursetypecount . '</h2>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

// Partner breakdown
if (!empty($partnerstats)) {
    $output .= '<div class="card mb-4">';
    $output .= '<div class="card-header">';
    $output .= '<h4>' . get_string('partnerbreakdown', 'local_licensetracker') . '</h4>';
    $output .= '</div>';
    $output .= '<div class="card-body">';
    $output .= '<div class="table-responsive">';
    $output .= '<table class="table table-striped">';
    $output .= '<thead>';
    $output .= '<tr>';
    $output .= '<th>' . get_string('partner', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('totalkeys', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('availablekeys', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('usedkeys', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('revokedkeys', 'local_licensetracker') . '</th>';
    $output .= '</tr>';
    $output .= '</thead>';
    $output .= '<tbody>';
    
    foreach ($partnerstats as $pstat) {
        $output .= '<tr>';
        $output .= '<td>' . $pstat->partnername . '</td>';
        $output .= '<td>' . $pstat->total . '</td>';
        $output .= '<td><span class="badge badge-success">' . $pstat->available . '</span></td>';
        $output .= '<td><span class="badge badge-info">' . $pstat->used . '</span></td>';
        $output .= '<td><span class="badge badge-danger">' . $pstat->revoked . '</span></td>';
        $output .= '</tr>';
    }
    
    $output .= '</tbody>';
    $output .= '</table>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
}

// Course type breakdown
if (!empty($coursetypestats)) {
    $output .= '<div class="card">';
    $output .= '<div class="card-header">';
    $output .= '<h4>' . get_string('coursetypebreakdown', 'local_licensetracker') . '</h4>';
    $output .= '</div>';
    $output .= '<div class="card-body">';
    $output .= '<div class="table-responsive">';
    $output .= '<table class="table table-striped">';
    $output .= '<thead>';
    $output .= '<tr>';
    $output .= '<th>' . get_string('coursetype', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('totalkeys', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('availablekeys', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('usedkeys', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('revokedkeys', 'local_licensetracker') . '</th>';
    $output .= '</tr>';
    $output .= '</thead>';
    $output .= '<tbody>';
    
    foreach ($coursetypestats as $ctstat) {
        $output .= '<tr>';
        $output .= '<td>' . $ctstat->name . '</td>';
        $output .= '<td>' . $ctstat->total . '</td>';
        $output .= '<td><span class="badge badge-success">' . $ctstat->available . '</span></td>';
        $output .= '<td><span class="badge badge-info">' . $ctstat->used . '</span></td>';
        $output .= '<td><span class="badge badge-danger">' . $ctstat->revoked . '</span></td>';
        $output .= '</tr>';
    }
    
    $output .= '</tbody>';
    $output .= '</table>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
}

// Add advanced analytics section
$output .= '<div class="row mt-4">';
$output .= '<div class="col-md-6">';
$output .= '<div class="card">';
$output .= '<div class="card-header">';
$output .= '<h5>' . get_string('usagetrends', 'local_licensetracker') . '</h5>';
$output .= '</div>';
$output .= '<div class="card-body">';

// Get usage trends for last 30 days
$sql = "SELECT DATE(FROM_UNIXTIME(timeused)) as usage_date,
               COUNT(*) as keys_used
        FROM {local_lt_keys}
        WHERE status = 'used' AND timeused > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY))
        GROUP BY DATE(FROM_UNIXTIME(timeused))
        ORDER BY usage_date DESC
        LIMIT 30";

$usagetrends = $DB->get_records_sql($sql);

if (!empty($usagetrends)) {
    $output .= '<canvas id="usageTrendsChart" width="400" height="200"></canvas>';

    // Prepare data for chart
    $dates = array();
    $counts = array();
    foreach (array_reverse($usagetrends) as $trend) {
        $dates[] = userdate(strtotime($trend->usage_date), '%m/%d');
        $counts[] = $trend->keys_used;
    }

    $output .= '<script>
    document.addEventListener("DOMContentLoaded", function() {
        var ctx = document.getElementById("usageTrendsChart").getContext("2d");
        new Chart(ctx, {
            type: "line",
            data: {
                labels: ' . json_encode($dates) . ',
                datasets: [{
                    label: "Keys Used",
                    data: ' . json_encode($counts) . ',
                    borderColor: "rgb(75, 192, 192)",
                    backgroundColor: "rgba(75, 192, 192, 0.2)",
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });
    </script>';
} else {
    $output .= '<p class="text-muted">' . get_string('nousagedata', 'local_licensetracker') . '</p>';
}

$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

// Partner performance metrics
$output .= '<div class="col-md-6">';
$output .= '<div class="card">';
$output .= '<div class="card-header">';
$output .= '<h5>' . get_string('partnerperformance', 'local_licensetracker') . '</h5>';
$output .= '</div>';
$output .= '<div class="card-body">';

// Get top performing partners
$sql = "SELECT p.partnername,
               COUNT(CASE WHEN k.status = 'used' THEN 1 END) as keys_used,
               COUNT(CASE WHEN k.status = 'available' THEN 1 END) as keys_available,
               ROUND((COUNT(CASE WHEN k.status = 'used' THEN 1 END) / COUNT(*)) * 100, 1) as usage_rate
        FROM {local_lt_partners} p
        LEFT JOIN {local_lt_keys} k ON k.partnerid = p.id
        GROUP BY p.id, p.partnername
        HAVING COUNT(*) > 0
        ORDER BY usage_rate DESC, keys_used DESC
        LIMIT 10";

$toppartners = $DB->get_records_sql($sql);

if (!empty($toppartners)) {
    $output .= '<div class="table-responsive">';
    $output .= '<table class="table table-sm">';
    $output .= '<thead>';
    $output .= '<tr>';
    $output .= '<th>' . get_string('partner', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('usagerate', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('keysused', 'local_licensetracker') . '</th>';
    $output .= '</tr>';
    $output .= '</thead>';
    $output .= '<tbody>';

    foreach ($toppartners as $partner) {
        $rateclass = $partner->usage_rate >= 80 ? 'text-success' :
                    ($partner->usage_rate >= 50 ? 'text-warning' : 'text-danger');

        $output .= '<tr>';
        $output .= '<td>' . $partner->partnername . '</td>';
        $output .= '<td><span class="' . $rateclass . '">' . $partner->usage_rate . '%</span></td>';
        $output .= '<td>' . $partner->keys_used . '</td>';
        $output .= '</tr>';
    }

    $output .= '</tbody>';
    $output .= '</table>';
    $output .= '</div>';
} else {
    $output .= '<p class="text-muted">' . get_string('nopartnerdata', 'local_licensetracker') . '</p>';
}

$output .= '</div>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

// System health indicators
$output .= '<div class="row mt-4">';
$output .= '<div class="col-12">';
$output .= '<div class="card">';
$output .= '<div class="card-header">';
$output .= '<h5>' . get_string('systemhealth', 'local_licensetracker') . '</h5>';
$output .= '</div>';
$output .= '<div class="card-body">';

// Check for various system health indicators
$healthchecks = array();

// Check for partners with low stock
$lowstockpartners = $DB->count_records_sql(
    "SELECT COUNT(DISTINCT p.id)
     FROM {local_lt_partners} p
     LEFT JOIN {local_lt_keys} k ON k.partnerid = p.id AND k.status = 'available'
     GROUP BY p.id
     HAVING COUNT(k.id) <= 5 AND COUNT(k.id) > 0"
);

$healthchecks[] = array(
    'name' => get_string('lowstockpartners', 'local_licensetracker'),
    'value' => $lowstockpartners,
    'status' => $lowstockpartners == 0 ? 'success' : 'warning',
    'description' => get_string('lowstockpartners_desc', 'local_licensetracker')
);

// Check for expired keys
$expiredkeys = $DB->count_records_sql(
    "SELECT COUNT(*) FROM {local_lt_keys}
     WHERE expireson IS NOT NULL AND expireson < UNIX_TIMESTAMP() AND status = 'available'"
);

$healthchecks[] = array(
    'name' => get_string('expiredkeys', 'local_licensetracker'),
    'value' => $expiredkeys,
    'status' => $expiredkeys == 0 ? 'success' : 'danger',
    'description' => get_string('expiredkeys_desc', 'local_licensetracker')
);

// Check for partners with no course type assignments
$unassignedpartners = $DB->count_records_sql(
    "SELECT COUNT(DISTINCT p.id)
     FROM {local_lt_partners} p
     LEFT JOIN {local_lt_partner_coursetypes} pct ON pct.partnerid = p.id
     WHERE pct.id IS NULL"
);

$healthchecks[] = array(
    'name' => get_string('unassignedpartners', 'local_licensetracker'),
    'value' => $unassignedpartners,
    'status' => $unassignedpartners == 0 ? 'success' : 'warning',
    'description' => get_string('unassignedpartners_desc', 'local_licensetracker')
);

$output .= '<div class="row">';
foreach ($healthchecks as $check) {
    $badgeclass = $check['status'] === 'success' ? 'badge-success' :
                 ($check['status'] === 'warning' ? 'badge-warning' : 'badge-danger');

    $output .= '<div class="col-md-4 mb-3">';
    $output .= '<div class="card border-' . $check['status'] . '">';
    $output .= '<div class="card-body text-center">';
    $output .= '<h5>' . $check['name'] . '</h5>';
    $output .= '<h3><span class="badge ' . $badgeclass . '">' . $check['value'] . '</span></h3>';
    $output .= '<p class="text-muted small">' . $check['description'] . '</p>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '</div>';
}
$output .= '</div>';

$output .= '</div>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

return $output;
