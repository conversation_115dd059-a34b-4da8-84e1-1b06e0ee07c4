<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Key generation form for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\form;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/formslib.php');

/**
 * Form for generating license keys.
 */
class key_generation_form extends \moodleform {

    /**
     * Define the form.
     */
    public function definition() {
        global $DB;

        $mform = $this->_form;

        // Number of keys
        $mform->addElement('text', 'numberofkeys', get_string('numberofkeys', 'local_licensetracker'));
        $mform->setType('numberofkeys', PARAM_INT);
        $mform->addRule('numberofkeys', null, 'required', null, 'client');
        $mform->addRule('numberofkeys', null, 'numeric', null, 'client');
        $mform->setDefault('numberofkeys', 1);

        // Partner selection
        $partners = $DB->get_records_menu('local_lt_partners', null, 'partnername', 'id, partnername');
        $mform->addElement('select', 'partnerid', get_string('partner', 'local_licensetracker'), $partners);
        $mform->addRule('partnerid', null, 'required', null, 'client');

        // Course type selection
        $coursetypes = $DB->get_records_menu('local_lt_coursetypes', null, 'name', 'id, name');
        $mform->addElement('select', 'coursetypeid', get_string('coursetype', 'local_licensetracker'), $coursetypes);
        $mform->addRule('coursetypeid', null, 'required', null, 'client');

        // Valid from date (optional)
        $mform->addElement('date_time_selector', 'validfrom', get_string('validfrom', 'local_licensetracker'), 
                          array('optional' => true));

        // Expires on date (optional)
        $mform->addElement('date_time_selector', 'expireson', get_string('expireson', 'local_licensetracker'), 
                          array('optional' => true));

        // Action buttons
        $this->add_action_buttons(true, get_string('generatekey', 'local_licensetracker'));
    }

    /**
     * Validate the form data.
     *
     * @param array $data Form data
     * @param array $files Form files
     * @return array Validation errors
     */
    public function validation($data, $files) {
        $errors = parent::validation($data, $files);

        // Validate number of keys
        if (isset($data['numberofkeys'])) {
            if ($data['numberofkeys'] < 1) {
                $errors['numberofkeys'] = 'Number of keys must be at least 1';
            }
            if ($data['numberofkeys'] > 10000) {
                $errors['numberofkeys'] = 'Number of keys cannot exceed 10,000';
            }
        }

        // Validate date range
        if (!empty($data['validfrom']) && !empty($data['expireson'])) {
            if ($data['validfrom'] >= $data['expireson']) {
                $errors['expireson'] = 'Expiry date must be after valid from date';
            }
        }

        return $errors;
    }
}
