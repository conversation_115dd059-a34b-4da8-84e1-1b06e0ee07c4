<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Bulk export keys tab for tool_licensetrackeradmin plugin.
 *
 * @package    tool_licensetrackeradmin
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

require_capability('tool/licensetrackeradmin:bulkexport', $context);

$form = new \tool_licensetrackeradmin\form\bulk_export_keys_form();

if ($form->is_cancelled()) {
    redirect($PAGE->url);
} else if ($data = $form->get_data()) {
    
    // Build filters for export
    $filters = array();
    if (!empty($data->partnerid)) {
        $filters['partner'] = $data->partnerid;
    }
    if (!empty($data->coursetypeid)) {
        $filters['coursetype'] = $data->coursetypeid;
    }
    if (!empty($data->status)) {
        $filters['status'] = $data->status;
    }
    
    // Get keys for export
    $keys = local_licensetracker_get_keys($filters);
    
    // Filter by date range if specified
    if (!empty($data->datefrom) || !empty($data->dateto)) {
        $filteredkeys = array();
        foreach ($keys as $key) {
            $include = true;
            if (!empty($data->datefrom) && $key->timecreated < $data->datefrom) {
                $include = false;
            }
            if (!empty($data->dateto) && $key->timecreated > $data->dateto) {
                $include = false;
            }
            if ($include) {
                $filteredkeys[] = $key;
            }
        }
        $keys = $filteredkeys;
    }
    
    // Generate CSV
    $filename = 'license_keys_export_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    
    $output = fopen('php://output', 'w');
    
    // Add header row if requested
    if (!empty($data->includeheader)) {
        fputcsv($output, array(
            'Key String',
            'Partner',
            'Course Type',
            'Status',
            'Valid From',
            'Expires On',
            'Used By',
            'Date Used',
            'IP Address',
            'User Agent',
            'Created',
            'Modified'
        ));
    }
    
    // Add data rows
    foreach ($keys as $key) {
        $row = array(
            $key->keystring,
            $key->partnername,
            $key->coursetypename,
            $key->status,
            $key->validfrom ? date('Y-m-d H:i:s', $key->validfrom) : '',
            $key->expireson ? date('Y-m-d H:i:s', $key->expireson) : '',
            $key->status === 'used' ? $key->firstname . ' ' . $key->lastname : '',
            $key->dateused ? date('Y-m-d H:i:s', $key->dateused) : '',
            $key->ipaddressused ?: '',
            $key->useragent ?: '',
            date('Y-m-d H:i:s', $key->timecreated),
            date('Y-m-d H:i:s', $key->timemodified)
        );
        fputcsv($output, $row);
    }
    
    fclose($output);
    exit;
}

echo '<div class="card">';
echo '<div class="card-header">';
echo '<h3>' . get_string('bulkexportkeys', 'tool_licensetrackeradmin') . '</h3>';
echo '</div>';
echo '<div class="card-body">';

echo '<div class="alert alert-info">';
echo '<p>Export license keys to CSV format with optional filtering.</p>';
echo '</div>';

$form->display();

echo '</div>';
echo '</div>';
