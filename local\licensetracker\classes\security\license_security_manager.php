<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with <PERSON>odle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * License security manager for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\security;

defined('MOODLE_INTERNAL') || die();

/**
 * Class for managing license key security and preventing misuse.
 */
class license_security_manager {

    /** @var int Maximum key usage attempts per IP per hour */
    const MAX_ATTEMPTS_PER_IP_HOUR = 10;

    /** @var int Maximum key usage attempts per user per hour */
    const MAX_ATTEMPTS_PER_USER_HOUR = 5;

    /** @var int Time window for rate limiting (in seconds) */
    const RATE_LIMIT_WINDOW = 3600; // 1 hour

    /**
     * Check if a partner can access a specific license key.
     *
     * @param int $partnerid Partner ID
     * @param string $keystring License key string
     * @param int $userid User ID attempting to use the key
     * @return array Array with 'allowed' boolean and 'reason' string
     */
    public static function check_partner_key_access($partnerid, $keystring, $userid = null) {
        global $DB, $USER;

        if (!$userid) {
            $userid = $USER->id;
        }

        // Get the key record
        $key = $DB->get_record('local_lt_keys', array('keystring' => $keystring));
        if (!$key) {
            return array('allowed' => false, 'reason' => 'Key not found');
        }

        // Check if key belongs to the partner
        if ($key->partnerid != $partnerid) {
            self::log_security_violation($userid, 'unauthorized_key_access', array(
                'keystring' => $keystring,
                'attempted_partner' => $partnerid,
                'actual_partner' => $key->partnerid
            ));
            return array('allowed' => false, 'reason' => 'Key does not belong to your organization');
        }

        // Check if key is available
        if ($key->status !== 'available') {
            return array('allowed' => false, 'reason' => 'Key is not available (status: ' . $key->status . ')');
        }

        // Check expiration
        if ($key->expireson && $key->expireson < time()) {
            return array('allowed' => false, 'reason' => 'Key has expired');
        }

        // Check valid from date
        if ($key->validfrom && $key->validfrom > time()) {
            return array('allowed' => false, 'reason' => 'Key is not yet valid');
        }

        return array('allowed' => true, 'reason' => 'Access granted');
    }

    /**
     * Check rate limiting for key usage attempts.
     *
     * @param int $userid User ID
     * @param string $ipaddress IP address
     * @return array Array with 'allowed' boolean and 'reason' string
     */
    public static function check_rate_limiting($userid, $ipaddress) {
        global $DB;

        $now = time();
        $window_start = $now - self::RATE_LIMIT_WINDOW;

        // Check IP-based rate limiting
        $ip_attempts = $DB->count_records_sql(
            "SELECT COUNT(*) FROM {local_lt_security_log} 
             WHERE ipaddress = ? AND timecreated > ? AND action IN ('key_usage_attempt', 'key_used')",
            array($ipaddress, $window_start)
        );

        if ($ip_attempts >= self::MAX_ATTEMPTS_PER_IP_HOUR) {
            self::log_security_violation($userid, 'rate_limit_exceeded_ip', array(
                'ipaddress' => $ipaddress,
                'attempts' => $ip_attempts
            ));
            return array('allowed' => false, 'reason' => 'Too many attempts from this IP address');
        }

        // Check user-based rate limiting
        $user_attempts = $DB->count_records_sql(
            "SELECT COUNT(*) FROM {local_lt_security_log} 
             WHERE userid = ? AND timecreated > ? AND action IN ('key_usage_attempt', 'key_used')",
            array($userid, $window_start)
        );

        if ($user_attempts >= self::MAX_ATTEMPTS_PER_USER_HOUR) {
            self::log_security_violation($userid, 'rate_limit_exceeded_user', array(
                'userid' => $userid,
                'attempts' => $user_attempts
            ));
            return array('allowed' => false, 'reason' => 'Too many attempts by this user');
        }

        return array('allowed' => true, 'reason' => 'Rate limit check passed');
    }

    /**
     * Validate key format and detect potential tampering.
     *
     * @param string $keystring License key string
     * @return array Array with 'valid' boolean and 'reason' string
     */
    public static function validate_key_format($keystring) {
        // Basic format validation
        if (empty($keystring)) {
            return array('valid' => false, 'reason' => 'Empty key string');
        }

        // Check length (reasonable bounds)
        if (strlen($keystring) < 5 || strlen($keystring) > 100) {
            return array('valid' => false, 'reason' => 'Invalid key length');
        }

        // Check for valid characters only
        if (!preg_match('/^[A-Z0-9\-]+$/', $keystring)) {
            return array('valid' => false, 'reason' => 'Invalid characters in key');
        }

        // Check for suspicious patterns (too many repeated characters)
        if (preg_match('/(.)\1{5,}/', $keystring)) {
            return array('valid' => false, 'reason' => 'Suspicious key pattern detected');
        }

        return array('valid' => true, 'reason' => 'Key format is valid');
    }

    /**
     * Log security violations and suspicious activities.
     *
     * @param int $userid User ID
     * @param string $action Action type
     * @param array $details Additional details
     */
    public static function log_security_violation($userid, $action, $details = array()) {
        global $DB;

        $log = new \stdClass();
        $log->userid = $userid;
        $log->action = $action;
        $log->details = json_encode($details);
        $log->ipaddress = getremoteaddr();
        $log->useragent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $log->timecreated = time();

        try {
            $DB->insert_record('local_lt_security_log', $log);
        } catch (\Exception $e) {
            // If security log table doesn't exist, create it
            self::ensure_security_log_table();
            $DB->insert_record('local_lt_security_log', $log);
        }

        // Also log to Moodle's standard log
        $context = \context_system::instance();
        $event = \local_licensetracker\event\security_violation::create(array(
            'context' => $context,
            'userid' => $userid,
            'other' => array(
                'action' => $action,
                'details' => $details
            )
        ));
        $event->trigger();
    }

    /**
     * Log successful key usage for audit trail.
     *
     * @param int $keyid Key ID
     * @param int $userid User ID
     * @param string $action Action performed
     */
    public static function log_key_usage($keyid, $userid, $action = 'key_used') {
        global $DB;

        $log = new \stdClass();
        $log->userid = $userid;
        $log->keyid = $keyid;
        $log->action = $action;
        $log->ipaddress = getremoteaddr();
        $log->useragent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $log->timecreated = time();

        try {
            $DB->insert_record('local_lt_security_log', $log);
        } catch (\Exception $e) {
            self::ensure_security_log_table();
            $DB->insert_record('local_lt_security_log', $log);
        }
    }

    /**
     * Check for suspicious key usage patterns.
     *
     * @param int $partnerid Partner ID
     * @return array Array of detected issues
     */
    public static function detect_suspicious_patterns($partnerid = null) {
        global $DB;

        $issues = array();
        $now = time();
        $day_ago = $now - 86400; // 24 hours

        // Check for rapid key usage from same IP
        $sql = "SELECT ipaddress, COUNT(*) as count
                FROM {local_lt_security_log} 
                WHERE action = 'key_used' AND timecreated > ?
                " . ($partnerid ? "AND keyid IN (SELECT id FROM {local_lt_keys} WHERE partnerid = ?)" : "") . "
                GROUP BY ipaddress
                HAVING COUNT(*) > 20";
        
        $params = array($day_ago);
        if ($partnerid) {
            $params[] = $partnerid;
        }

        $rapid_usage = $DB->get_records_sql($sql, $params);
        foreach ($rapid_usage as $usage) {
            $issues[] = array(
                'type' => 'rapid_usage_ip',
                'description' => "Rapid key usage from IP {$usage->ipaddress} ({$usage->count} keys in 24h)",
                'severity' => 'high'
            );
        }

        // Check for keys used outside business hours (if configured)
        $business_hours_start = get_config('local_licensetracker', 'business_hours_start') ?: 8;
        $business_hours_end = get_config('local_licensetracker', 'business_hours_end') ?: 18;
        
        $after_hours_count = $DB->count_records_sql(
            "SELECT COUNT(*) FROM {local_lt_security_log} 
             WHERE action = 'key_used' AND timecreated > ?
             AND (HOUR(FROM_UNIXTIME(timecreated)) < ? OR HOUR(FROM_UNIXTIME(timecreated)) > ?)",
            array($day_ago, $business_hours_start, $business_hours_end)
        );

        if ($after_hours_count > 10) {
            $issues[] = array(
                'type' => 'after_hours_usage',
                'description' => "High after-hours key usage ({$after_hours_count} keys used outside business hours)",
                'severity' => 'medium'
            );
        }

        return $issues;
    }

    /**
     * Ensure the security log table exists.
     */
    private static function ensure_security_log_table() {
        global $DB;

        $dbman = $DB->get_manager();
        $table = new \xmldb_table('local_lt_security_log');

        if (!$dbman->table_exists($table)) {
            $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
            $table->add_field('userid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
            $table->add_field('keyid', XMLDB_TYPE_INTEGER, '10', null, null, null, null);
            $table->add_field('action', XMLDB_TYPE_CHAR, '50', null, XMLDB_NOTNULL, null, null);
            $table->add_field('details', XMLDB_TYPE_TEXT, null, null, null, null, null);
            $table->add_field('ipaddress', XMLDB_TYPE_CHAR, '45', null, null, null, null);
            $table->add_field('useragent', XMLDB_TYPE_TEXT, null, null, null, null, null);
            $table->add_field('timecreated', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);

            $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
            $table->add_key('userid', XMLDB_KEY_FOREIGN, array('userid'), 'user', array('id'));
            $table->add_key('keyid', XMLDB_KEY_FOREIGN, array('keyid'), 'local_lt_keys', array('id'));

            $table->add_index('action', XMLDB_INDEX_NOTUNIQUE, array('action'));
            $table->add_index('timecreated', XMLDB_INDEX_NOTUNIQUE, array('timecreated'));
            $table->add_index('ipaddress', XMLDB_INDEX_NOTUNIQUE, array('ipaddress'));

            $dbman->create_table($table);
        }
    }
}
