<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Database performance analysis and optimization tool.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once(__DIR__ . '/../../config.php');
require_once($CFG->dirroot . '/local/licensetracker/lib.php');

require_login();

$context = context_system::instance();
$PAGE->set_context($context);
$PAGE->set_url('/local/licensetracker/db_performance.php');
$PAGE->set_title('License Tracker - Database Performance');
$PAGE->set_heading('License Tracker - Database Performance');

// Require admin permissions
require_capability('moodle/site:config', $context);

$PAGE->set_pagelayout('admin');

echo $OUTPUT->header();

echo '<div class="card">';
echo '<div class="card-header bg-info text-white">';
echo '<h3><i class="fa fa-database"></i> Database Performance Analysis</h3>';
echo '</div>';
echo '<div class="card-body">';

// Table size analysis
echo '<h4>📊 Table Size Analysis</h4>';
$tables = array('local_lt_keys', 'local_lt_partners', 'local_lt_coursetypes', 'local_lt_partner_courses');

echo '<table class="table table-striped">';
echo '<thead>';
echo '<tr><th>Table</th><th>Record Count</th><th>Performance Status</th><th>Recommendations</th></tr>';
echo '</thead>';
echo '<tbody>';

foreach ($tables as $table) {
    $count = $DB->count_records($table);
    
    // Performance thresholds
    $status = 'Good';
    $recommendations = 'No action needed';
    $class = 'success';
    
    if ($table === 'local_lt_keys') {
        if ($count > 100000) {
            $status = 'Monitor';
            $recommendations = 'Consider archiving old keys';
            $class = 'warning';
        }
        if ($count > 500000) {
            $status = 'Action Required';
            $recommendations = 'Implement key archiving and cleanup';
            $class = 'danger';
        }
    }
    
    echo '<tr class="table-' . $class . '">';
    echo '<td><strong>' . $table . '</strong></td>';
    echo '<td>' . number_format($count) . '</td>';
    echo '<td>' . $status . '</td>';
    echo '<td>' . $recommendations . '</td>';
    echo '</tr>';
}

echo '</tbody>';
echo '</table>';

// Index analysis
echo '<h4>🔍 Index Analysis</h4>';
echo '<div class="alert alert-info">';
echo '<strong>Database Indexes:</strong> The following indexes are configured for optimal performance:';
echo '<ul>';
echo '<li><strong>local_lt_keys:</strong> keystring (unique), status, partner_status, coursetype_status, status_dates</li>';
echo '<li><strong>local_lt_partners:</strong> partnername (unique), moodleuserid (unique), country</li>';
echo '<li><strong>local_lt_coursetypes:</strong> name (unique)</li>';
echo '<li><strong>local_lt_partner_courses:</strong> partner_coursetype (unique)</li>';
echo '</ul>';
echo '</div>';

// Query performance test
echo '<h4>⚡ Query Performance Test</h4>';
$start_time = microtime(true);

// Test common queries
$queries = array();

// Query 1: Get available keys for a partner
$start = microtime(true);
$DB->get_records_sql("SELECT COUNT(*) FROM {local_lt_keys} WHERE partnerid = 1 AND status = 'available'");
$queries['available_keys'] = microtime(true) - $start;

// Query 2: Get partner statistics
$start = microtime(true);
$DB->get_record_sql("SELECT COUNT(*) as total FROM {local_lt_keys} WHERE partnerid = 1");
$queries['partner_stats'] = microtime(true) - $start;

// Query 3: Validate key
$start = microtime(true);
$DB->get_record_sql("SELECT * FROM {local_lt_keys} WHERE keystring = 'TEST-KEY-1234' AND status = 'available'");
$queries['key_validation'] = microtime(true) - $start;

// Query 4: Get keys with joins
$start = microtime(true);
$DB->get_records_sql("
    SELECT k.*, p.partnername, ct.name as coursetypename 
    FROM {local_lt_keys} k
    LEFT JOIN {local_lt_partners} p ON k.partnerid = p.id
    LEFT JOIN {local_lt_coursetypes} ct ON k.coursetypeid = ct.id
    LIMIT 10
");
$queries['keys_with_joins'] = microtime(true) - $start;

echo '<table class="table table-striped">';
echo '<thead>';
echo '<tr><th>Query Type</th><th>Execution Time (ms)</th><th>Performance</th></tr>';
echo '</thead>';
echo '<tbody>';

foreach ($queries as $query => $time) {
    $time_ms = round($time * 1000, 2);
    
    $performance = 'Excellent';
    $class = 'success';
    
    if ($time_ms > 10) {
        $performance = 'Good';
        $class = 'info';
    }
    if ($time_ms > 50) {
        $performance = 'Slow';
        $class = 'warning';
    }
    if ($time_ms > 100) {
        $performance = 'Very Slow';
        $class = 'danger';
    }
    
    echo '<tr class="table-' . $class . '">';
    echo '<td>' . ucwords(str_replace('_', ' ', $query)) . '</td>';
    echo '<td>' . $time_ms . ' ms</td>';
    echo '<td>' . $performance . '</td>';
    echo '</tr>';
}

echo '</tbody>';
echo '</table>';

// Optimization recommendations
echo '<h4>🚀 Optimization Recommendations</h4>';
echo '<div class="row">';

echo '<div class="col-md-6">';
echo '<div class="card">';
echo '<div class="card-header bg-success text-white">';
echo '<h5>✅ Current Optimizations</h5>';
echo '</div>';
echo '<div class="card-body">';
echo '<ul>';
echo '<li>Proper database indexes on frequently queried fields</li>';
echo '<li>Foreign key constraints for data integrity</li>';
echo '<li>Parameterized queries to prevent SQL injection</li>';
echo '<li>Efficient table structure with appropriate field types</li>';
echo '<li>Composite indexes for multi-field queries</li>';
echo '</ul>';
echo '</div>';
echo '</div>';
echo '</div>';

echo '<div class="col-md-6">';
echo '<div class="card">';
echo '<div class="card-header bg-warning text-dark">';
echo '<h5>⚠️ Future Considerations</h5>';
echo '</div>';
echo '<div class="card-body">';
echo '<ul>';
echo '<li>Implement key archiving for old/expired keys</li>';
echo '<li>Add database connection pooling for high load</li>';
echo '<li>Consider read replicas for reporting queries</li>';
echo '<li>Implement query result caching</li>';
echo '<li>Monitor slow query log regularly</li>';
echo '</ul>';
echo '</div>';
echo '</div>';
echo '</div>';

echo '</div>';

// Database maintenance
echo '<h4>🔧 Database Maintenance</h4>';
echo '<div class="alert alert-info">';
echo '<strong>Recommended Maintenance Tasks:</strong>';
echo '<ul>';
echo '<li><strong>Weekly:</strong> Check for expired keys and update status</li>';
echo '<li><strong>Monthly:</strong> Archive old used/revoked keys (older than 1 year)</li>';
echo '<li><strong>Quarterly:</strong> Analyze query performance and optimize indexes</li>';
echo '<li><strong>Annually:</strong> Full database optimization and cleanup</li>';
echo '</ul>';
echo '</div>';

echo '</div>';
echo '</div>';

// Action buttons
echo '<div class="mt-3 text-center">';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/system_status.php" class="btn btn-primary">System Status</a>';
echo '<a href="' . $CFG->wwwroot . '/admin/tool/task/scheduledtasks.php" class="btn btn-secondary ml-2">Scheduled Tasks</a>';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php" class="btn btn-info ml-2">Dashboard</a>';
echo '</div>';

echo $OUTPUT->footer();
