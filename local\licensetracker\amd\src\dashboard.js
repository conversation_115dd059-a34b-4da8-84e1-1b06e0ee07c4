/**
 * License Tracker Dashboard JavaScript
 *
 * @module     local_licensetracker/dashboard
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

define(['jquery', 'core/notification', 'core/ajax', 'core/templates'], function($, Notification, Ajax, Templates) {
    'use strict';

    /**
     * Initialize dashboard functionality
     */
    var init = function() {
        initTableSorting();
        initConfirmDialogs();
        initTooltips();
        initAutoRefresh();
        initKeyValidation();
        initBulkActions();
    };

    /**
     * Initialize table sorting and filtering
     */
    var initTableSorting = function() {
        $('.license-key-table table').each(function() {
            var $table = $(this);
            
            // Add sorting to headers
            $table.find('thead th').each(function(index) {
                var $th = $(this);
                if (!$th.hasClass('no-sort')) {
                    $th.addClass('sortable').css('cursor', 'pointer');
                    $th.on('click', function() {
                        sortTable($table, index);
                    });
                }
            });
        });
    };

    /**
     * Sort table by column
     */
    var sortTable = function($table, columnIndex) {
        var $tbody = $table.find('tbody');
        var rows = $tbody.find('tr').toArray();
        var isAscending = !$table.data('sort-asc-' + columnIndex);
        
        rows.sort(function(a, b) {
            var aText = $(a).find('td').eq(columnIndex).text().trim();
            var bText = $(b).find('td').eq(columnIndex).text().trim();
            
            // Try to parse as numbers
            var aNum = parseFloat(aText);
            var bNum = parseFloat(bText);
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return isAscending ? aNum - bNum : bNum - aNum;
            }
            
            // Sort as strings
            return isAscending ? aText.localeCompare(bText) : bText.localeCompare(aText);
        });
        
        $tbody.empty().append(rows);
        $table.data('sort-asc-' + columnIndex, isAscending);
        
        // Update header indicators
        $table.find('thead th').removeClass('sort-asc sort-desc');
        $table.find('thead th').eq(columnIndex).addClass(isAscending ? 'sort-asc' : 'sort-desc');
    };

    /**
     * Initialize confirmation dialogs
     */
    var initConfirmDialogs = function() {
        $('body').on('click', '[data-confirm]', function(e) {
            e.preventDefault();
            var $this = $(this);
            var message = $this.data('confirm') || 'Are you sure?';
            
            if (confirm(message)) {
                if ($this.is('a')) {
                    window.location.href = $this.attr('href');
                } else if ($this.is('form')) {
                    $this.submit();
                }
            }
        });
    };

    /**
     * Initialize tooltips
     */
    var initTooltips = function() {
        $('[data-toggle="tooltip"]').tooltip();
        
        // Add tooltips to status badges
        $('.status-badge').each(function() {
            var $badge = $(this);
            var status = $badge.text().toLowerCase();
            var tooltipText = getStatusTooltip(status);
            if (tooltipText) {
                $badge.attr('title', tooltipText).tooltip();
            }
        });
    };

    /**
     * Get tooltip text for status
     */
    var getStatusTooltip = function(status) {
        var tooltips = {
            'available': 'This license key is ready to be used',
            'used': 'This license key has been used to register a student',
            'revoked': 'This license key has been revoked and cannot be used',
            'expired': 'This license key has passed its expiration date'
        };
        return tooltips[status] || '';
    };

    /**
     * Initialize auto-refresh for statistics
     */
    var initAutoRefresh = function() {
        var $statsCards = $('.stats-card');
        if ($statsCards.length > 0) {
            setInterval(function() {
                refreshStatistics();
            }, 30000); // Refresh every 30 seconds
        }
    };

    /**
     * Refresh statistics via AJAX
     */
    var refreshStatistics = function() {
        Ajax.call([{
            methodname: 'local_licensetracker_get_statistics',
            args: {},
            done: function(data) {
                updateStatisticsDisplay(data);
            },
            fail: function(error) {
                console.log('Failed to refresh statistics:', error);
            }
        }]);
    };

    /**
     * Update statistics display
     */
    var updateStatisticsDisplay = function(data) {
        $('.stats-card').each(function() {
            var $card = $(this);
            var statType = $card.data('stat-type');
            if (data[statType] !== undefined) {
                $card.find('h2').text(data[statType]);
            }
        });
    };

    /**
     * Initialize key validation
     */
    var initKeyValidation = function() {
        $('input[name="licensekey"]').on('blur', function() {
            var $input = $(this);
            var keyValue = $input.val().trim();
            
            if (keyValue.length > 0) {
                validateLicenseKey(keyValue, $input);
            }
        });
    };

    /**
     * Validate license key via AJAX
     */
    var validateLicenseKey = function(keyValue, $input) {
        $input.removeClass('is-valid is-invalid');
        
        Ajax.call([{
            methodname: 'local_licensetracker_validate_key',
            args: {keystring: keyValue},
            done: function(data) {
                if (data.valid) {
                    $input.addClass('is-valid');
                    showKeyInfo(data, $input);
                } else {
                    $input.addClass('is-invalid');
                    showKeyError(data.message, $input);
                }
            },
            fail: function(error) {
                $input.addClass('is-invalid');
                showKeyError('Failed to validate key', $input);
            }
        }]);
    };

    /**
     * Show key information
     */
    var showKeyInfo = function(data, $input) {
        var $feedback = $input.siblings('.valid-feedback');
        if ($feedback.length === 0) {
            $feedback = $('<div class="valid-feedback"></div>');
            $input.after($feedback);
        }
        $feedback.text('Valid key for ' + data.coursetype);
    };

    /**
     * Show key error
     */
    var showKeyError = function(message, $input) {
        var $feedback = $input.siblings('.invalid-feedback');
        if ($feedback.length === 0) {
            $feedback = $('<div class="invalid-feedback"></div>');
            $input.after($feedback);
        }
        $feedback.text(message);
    };

    /**
     * Initialize bulk actions
     */
    var initBulkActions = function() {
        // Select all checkbox
        $('body').on('change', '.select-all-keys', function() {
            var isChecked = $(this).is(':checked');
            $('.key-checkbox').prop('checked', isChecked);
            updateBulkActionButtons();
        });

        // Individual checkboxes
        $('body').on('change', '.key-checkbox', function() {
            updateBulkActionButtons();
            updateSelectAllState();
        });

        // Bulk action buttons
        $('body').on('click', '.bulk-action-btn', function(e) {
            e.preventDefault();
            var action = $(this).data('action');
            var selectedKeys = getSelectedKeys();
            
            if (selectedKeys.length === 0) {
                Notification.alert('No Selection', 'Please select at least one license key.');
                return;
            }
            
            performBulkAction(action, selectedKeys);
        });
    };

    /**
     * Update bulk action button states
     */
    var updateBulkActionButtons = function() {
        var selectedCount = $('.key-checkbox:checked').length;
        $('.bulk-action-btn').prop('disabled', selectedCount === 0);
        $('.selected-count').text(selectedCount);
    };

    /**
     * Update select all checkbox state
     */
    var updateSelectAllState = function() {
        var totalCheckboxes = $('.key-checkbox').length;
        var checkedCheckboxes = $('.key-checkbox:checked').length;
        var $selectAll = $('.select-all-keys');
        
        if (checkedCheckboxes === 0) {
            $selectAll.prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes === totalCheckboxes) {
            $selectAll.prop('indeterminate', false).prop('checked', true);
        } else {
            $selectAll.prop('indeterminate', true);
        }
    };

    /**
     * Get selected key IDs
     */
    var getSelectedKeys = function() {
        var keys = [];
        $('.key-checkbox:checked').each(function() {
            keys.push($(this).val());
        });
        return keys;
    };

    /**
     * Perform bulk action
     */
    var performBulkAction = function(action, keyIds) {
        var confirmMessage = 'Are you sure you want to ' + action + ' ' + keyIds.length + ' license key(s)?';
        
        if (!confirm(confirmMessage)) {
            return;
        }
        
        // Show loading state
        $('.bulk-action-btn').prop('disabled', true).text('Processing...');
        
        Ajax.call([{
            methodname: 'local_licensetracker_bulk_action',
            args: {
                action: action,
                keyids: keyIds
            },
            done: function(data) {
                if (data.success) {
                    Notification.addNotification({
                        message: data.message,
                        type: 'success'
                    });
                    location.reload(); // Refresh the page
                } else {
                    Notification.addNotification({
                        message: data.message || 'Bulk action failed',
                        type: 'error'
                    });
                }
            },
            fail: function(error) {
                Notification.addNotification({
                    message: 'Failed to perform bulk action: ' + error.message,
                    type: 'error'
                });
            },
            always: function() {
                $('.bulk-action-btn').prop('disabled', false).text('Apply');
            }
        }]);
    };

    return {
        init: init
    };
});
