<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Bulk export partners tab for tool_licensetrackeradmin plugin.
 *
 * @package    tool_licensetrackeradmin
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

require_capability('tool/licensetrackeradmin:bulkexport', $context);

$export = optional_param('export', 0, PARAM_INT);

if ($export && confirm_sesskey()) {
    // Get all partners with their course type assignments
    $sql = "SELECT p.*, u.username as moodleusername,
                   GROUP_CONCAT(CONCAT(ct.name, ':', pct.price) SEPARATOR ';') as coursetypes
            FROM {local_lt_partners} p
            LEFT JOIN {user} u ON p.moodleuserid = u.id
            LEFT JOIN {local_lt_partner_courses} pct ON p.id = pct.partnerid
            LEFT JOIN {local_lt_coursetypes} ct ON pct.coursetypeid = ct.id
            GROUP BY p.id
            ORDER BY p.partnername";
    
    $partners = $DB->get_records_sql($sql);
    
    // Generate CSV
    $filename = 'partners_export_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    
    $output = fopen('php://output', 'w');
    
    // Add header row
    fputcsv($output, array(
        'Partner Name',
        'Contact Email',
        'Country',
        'Language',
        'Moodle Username',
        'Course Types',
        'Created',
        'Modified'
    ));
    
    // Add data rows
    foreach ($partners as $partner) {
        $row = array(
            $partner->partnername,
            $partner->contactemail,
            $partner->country ?: '',
            $partner->lang ?: '',
            $partner->moodleusername ?: '',
            $partner->coursetypes ?: '',
            date('Y-m-d H:i:s', $partner->timecreated),
            date('Y-m-d H:i:s', $partner->timemodified)
        );
        fputcsv($output, $row);
    }
    
    fclose($output);
    exit;
}

echo '<div class="card">';
echo '<div class="card-header">';
echo '<h3>' . get_string('bulkexportpartners', 'tool_licensetrackeradmin') . '</h3>';
echo '</div>';
echo '<div class="card-body">';

echo '<div class="alert alert-info">';
echo '<p>Export all partners to CSV format including their course type assignments.</p>';
echo '</div>';

echo '<form method="post" action="">';
echo '<input type="hidden" name="sesskey" value="' . sesskey() . '">';
echo '<input type="hidden" name="export" value="1">';
echo '<button type="submit" class="btn btn-primary">' . get_string('exportpartners', 'tool_licensetrackeradmin') . '</button>';
echo '</form>';

echo '</div>';
echo '</div>';
