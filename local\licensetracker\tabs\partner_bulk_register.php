<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Partner bulk student registration tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

// CRITICAL: Verify partner permissions
$context = context_system::instance();
require_capability('local/licensetracker:usekey', $context);

// Ensure partner can only register with their own keys
if (!$partner) {
    throw new moodle_exception('nopermissions', 'error', '', 'Partner access required');
}

$form = new \local_licensetracker\form\partner_bulk_registration_form();

if ($form->is_cancelled()) {
    redirect(new moodle_url('/local/licensetracker/index.php', array('tab' => 'bulkregister')));
} else if ($data = $form->get_data()) {
    
    // Process CSV file
    $csvdata = $form->get_file_content('csvfile');
    if (!$csvdata) {
        redirect($PAGE->url, 'No file uploaded or file is empty', null, \core\output\notification::NOTIFY_ERROR);
    }
    
    // Parse CSV
    $lines = str_getcsv($csvdata, "\n");
    $header = str_getcsv(array_shift($lines));
    
    // Validate CSV format
    $requiredfields = array('username', 'firstname', 'lastname', 'email', 'password', 'licensekey');
    $optionalfields = array('city', 'country');
    
    $missingfields = array_diff($requiredfields, $header);
    if (!empty($missingfields)) {
        $error = 'Missing required CSV columns: ' . implode(', ', $missingfields);
        redirect($PAGE->url, $error, null, \core\output\notification::NOTIFY_ERROR);
    }
    
    $results = array(
        'success' => 0,
        'errors' => 0,
        'messages' => array()
    );
    
    foreach ($lines as $linenum => $line) {
        if (empty(trim($line))) {
            continue; // Skip empty lines
        }
        
        $row = str_getcsv($line);
        if (count($row) != count($header)) {
            $results['errors']++;
            $results['messages'][] = "Line " . ($linenum + 2) . ": Invalid number of columns";
            continue;
        }
        
        $userdata = array_combine($header, $row);
        
        try {
            // Validate license key and ensure it belongs to this partner
            $key = local_licensetracker_validate_key($userdata['licensekey']);
            if (!$key || $key->partnerid != $partner->id) {
                throw new Exception("Invalid license key or key does not belong to your organization");
            }
            
            // Check for duplicate username/email
            if ($DB->record_exists('user', array('username' => $userdata['username'], 'deleted' => 0))) {
                throw new Exception("Username already exists: " . $userdata['username']);
            }
            
            if ($DB->record_exists('user', array('email' => $userdata['email'], 'deleted' => 0))) {
                throw new Exception("Email already exists: " . $userdata['email']);
            }
            
            // Create user account - Partners can ONLY create partner_student users
            $newuser = new stdClass();
            $newuser->username = $userdata['username'];
            $newuser->firstname = $userdata['firstname'];
            $newuser->lastname = $userdata['lastname'];
            $newuser->email = $userdata['email'];
            $newuser->password = hash_internal_user_password($userdata['password']);
            $newuser->city = isset($userdata['city']) ? $userdata['city'] : '';
            $newuser->country = isset($userdata['country']) ? $userdata['country'] : '';
            $newuser->confirmed = 1;
            $newuser->auth = 'manual';
            $newuser->mnethostid = $CFG->mnet_localhost_id;
            $newuser->timecreated = time();
            $newuser->timemodified = time();
            
            // Set language from partner if available
            if (!empty($partner->lang)) {
                $newuser->lang = $partner->lang;
            }

            $userid = user_create_user($newuser, false, false);

            // ENFORCE: Partners can only create partner_student users
            local_licensetracker_set_user_type($userid, 'partner_student');
            
            if (!$userid) {
                throw new Exception('Failed to create user account');
            }
            
            // Update license key status
            $ipaddress = getremoteaddr();
            $useragent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            local_licensetracker_update_key_status($key->id, 'used', $userid, $ipaddress, $useragent);
            
            // Get course type and enroll user
            $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $key->coursetypeid));
            if ($coursetype && $coursetype->moodlecourseid) {
                $course = $DB->get_record('course', array('id' => $coursetype->moodlecourseid));
                if ($course) {
                    $enrolmethod = $coursetype->enrolmethod ?: 'manual';
                    $roleid = $coursetype->defaultroleid ?: 5; // Default to student role
                    
                    $enrol = enrol_get_plugin($enrolmethod);
                    if (!$enrol) {
                        $enrol = enrol_get_plugin('manual'); // Fallback to manual
                        $enrolmethod = 'manual';
                    }
                    
                    // Get or create enrollment instance
                    $instance = $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => $enrolmethod));
                    if (!$instance) {
                        $instanceid = $enrol->add_instance($course);
                        $instance = $DB->get_record('enrol', array('id' => $instanceid));
                    }
                    
                    // Enroll the user
                    $enrol->enrol_user($instance, $userid, $roleid, time(), 0, ENROL_USER_ACTIVE);
                }
            }
            
            $results['success']++;
            $results['messages'][] = "✓ Successfully registered: " . $userdata['username'];
            
        } catch (Exception $e) {
            $results['errors']++;
            $results['messages'][] = "✗ Line " . ($linenum + 2) . " (" . $userdata['username'] . "): " . $e->getMessage();
        }
    }
    
    // Show results
    $message = "Bulk registration completed. Success: {$results['success']}, Errors: {$results['errors']}";
    
    // Store detailed results in session for display
    $SESSION->bulk_registration_results = $results;
    
    redirect($PAGE->url, $message, null, 
        $results['errors'] > 0 ? \core\output\notification::NOTIFY_WARNING : \core\output\notification::NOTIFY_SUCCESS);
}

$output = '';

// Show previous results if available
if (isset($SESSION->bulk_registration_results)) {
    $results = $SESSION->bulk_registration_results;
    unset($SESSION->bulk_registration_results);
    
    $output .= '<div class="card mb-3">';
    $output .= '<div class="card-header">';
    $output .= '<h4>Bulk Registration Results</h4>';
    $output .= '</div>';
    $output .= '<div class="card-body">';
    $output .= '<p><strong>Success:</strong> ' . $results['success'] . ' users registered</p>';
    $output .= '<p><strong>Errors:</strong> ' . $results['errors'] . ' failed registrations</p>';
    
    if (!empty($results['messages'])) {
        $output .= '<div class="mt-3">';
        $output .= '<h5>Detailed Results:</h5>';
        $output .= '<div style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px;">';
        foreach ($results['messages'] as $message) {
            $class = strpos($message, '✓') === 0 ? 'text-success' : 'text-danger';
            $output .= '<div class="' . $class . '">' . $message . '</div>';
        }
        $output .= '</div>';
        $output .= '</div>';
    }
    
    $output .= '</div>';
    $output .= '</div>';
}

$output .= '<div class="card">';
$output .= '<div class="card-header">';
$output .= '<h3>' . get_string('bulkregisterstudents', 'local_licensetracker') . '</h3>';
$output .= '</div>';
$output .= '<div class="card-body">';

// Get partner stock by course type with alerts
$stockbycourse = local_licensetracker_get_partner_stock($partner->id);
$totalavailable = 0;

foreach ($stockbycourse as $stock) {
    $totalavailable += $stock->available_count;
}

if ($totalavailable == 0) {
    $output .= '<div class="alert alert-danger">';
    $output .= '<h5><i class="fa fa-exclamation-triangle"></i> Out of Stock</h5>';
    $output .= get_string('noavailablekeys', 'local_licensetracker');
    $output .= '<p class="mt-2">Please contact your administrator to purchase more license keys.</p>';
    $output .= '</div>';
} else {
    // Check for low stock warnings
    $lowstockwarnings = array();
    $criticalstockwarnings = array();

    foreach ($stockbycourse as $stock) {
        if ($stock->available_count > 0) {
            if ($stock->available_count <= 5) {
                $criticalstockwarnings[] = $stock->name . ' (' . $stock->available_count . ' keys remaining)';
            } else if ($stock->available_count <= 10) {
                $lowstockwarnings[] = $stock->name . ' (' . $stock->available_count . ' keys remaining)';
            }
        }
    }

    // Show critical stock warnings (higher threshold for bulk operations)
    if (!empty($criticalstockwarnings)) {
        $output .= '<div class="alert alert-danger">';
        $output .= '<h5><i class="fa fa-exclamation-triangle"></i> Low Stock Alert for Bulk Operations</h5>';
        $output .= '<p>The following course types have limited license keys for bulk registration:</p>';
        $output .= '<ul>';
        foreach ($criticalstockwarnings as $warning) {
            $output .= '<li>' . $warning . '</li>';
        }
        $output .= '</ul>';
        $output .= '<p><strong>Consider ordering more licenses before bulk registration.</strong></p>';
        $output .= '</div>';
    }

    $output .= '<div class="alert alert-info">';
    $output .= '<h5>Available License Keys for Bulk Registration:</h5>';
    $output .= '<div class="row">';

    foreach ($stockbycourse as $stock) {
        if ($stock->available_count > 0) {
            $cardclass = 'card';
            $textclass = 'text-success';

            if ($stock->available_count <= 5) {
                $cardclass = 'card border-warning';
                $textclass = 'text-warning';
            }

            $output .= '<div class="col-md-4 mb-2">';
            $output .= '<div class="' . $cardclass . '">';
            $output .= '<div class="card-body text-center">';
            $output .= '<span class="badge badge-primary">' . $stock->licenseprefix . '</span>';
            $output .= '<h6 class="mt-2">' . $stock->name . '</h6>';
            $output .= '<h4 class="' . $textclass . '">' . $stock->available_count . '</h4>';
            $output .= '<small class="text-muted">available keys</small>';
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</div>';
        }
    }

    $output .= '</div>';
    $output .= '<p class="mt-2"><strong>Total Available:</strong> ' . $totalavailable . ' license keys</p>';
    $output .= '</div>';
    
    // CSV format instructions
    $output .= '<div class="alert alert-secondary">';
    $output .= '<h5>CSV Format Requirements:</h5>';
    $output .= '<p>Your CSV file must include these columns (in any order):</p>';
    $output .= '<ul>';
    $output .= '<li><strong>username</strong> - Unique username for the student</li>';
    $output .= '<li><strong>firstname</strong> - Student\'s first name</li>';
    $output .= '<li><strong>lastname</strong> - Student\'s last name</li>';
    $output .= '<li><strong>email</strong> - Student\'s email address (must be unique)</li>';
    $output .= '<li><strong>password</strong> - Initial password for the student</li>';
    $output .= '<li><strong>licensekey</strong> - Valid license key from your allocation</li>';
    $output .= '<li><strong>city</strong> - Student\'s city (optional)</li>';
    $output .= '<li><strong>country</strong> - Student\'s country (optional)</li>';
    $output .= '</ul>';
    $output .= '<p><strong>Note:</strong> All license keys must belong to your organization and be available for use.</p>';
    $output .= '</div>';
    
    $output .= $form->render();
}

$output .= '</div>';
$output .= '</div>';

return $output;
