<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Bulk export keys form for tool_licensetrackeradmin plugin.
 *
 * @package    tool_licensetrackeradmin
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace tool_licensetrackeradmin\form;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/formslib.php');

/**
 * Form for bulk exporting license keys.
 */
class bulk_export_keys_form extends \moodleform {

    /**
     * Define the form.
     */
    public function definition() {
        global $DB;

        $mform = $this->_form;

        // Export filters
        $mform->addElement('header', 'filtersheader', get_string('filters', 'core'));

        // Partner filter
        $partners = $DB->get_records_menu('local_lt_partners', null, 'partnername', 'id, partnername');
        $partners = array('' => get_string('all')) + $partners;
        $mform->addElement('select', 'partnerid', get_string('partner', 'local_licensetracker'), $partners);

        // Course type filter
        $coursetypes = $DB->get_records_menu('local_lt_coursetypes', null, 'name', 'id, name');
        $coursetypes = array('' => get_string('all')) + $coursetypes;
        $mform->addElement('select', 'coursetypeid', get_string('coursetype', 'local_licensetracker'), $coursetypes);

        // Status filter
        $statuses = array(
            '' => get_string('all'),
            'available' => get_string('available', 'local_licensetracker'),
            'used' => get_string('used', 'local_licensetracker'),
            'revoked' => get_string('revoked', 'local_licensetracker')
        );
        $mform->addElement('select', 'status', get_string('status', 'local_licensetracker'), $statuses);

        // Date range
        $mform->addElement('date_selector', 'datefrom', get_string('datefrom', 'tool_licensetrackeradmin'), 
                          array('optional' => true));
        $mform->addElement('date_selector', 'dateto', get_string('dateto', 'tool_licensetrackeradmin'), 
                          array('optional' => true));

        // Export options
        $mform->addElement('header', 'optionsheader', get_string('options', 'core'));
        
        $mform->addElement('advcheckbox', 'includeheader', get_string('includeheader', 'core'), 
                          get_string('includeheader_desc', 'core'));
        $mform->setDefault('includeheader', 1);

        // Action buttons
        $this->add_action_buttons(true, get_string('exportkeys', 'tool_licensetrackeradmin'));
    }

    /**
     * Validate the form data.
     *
     * @param array $data Form data
     * @param array $files Form files
     * @return array Validation errors
     */
    public function validation($data, $files) {
        $errors = parent::validation($data, $files);

        // Validate date range
        if (!empty($data['datefrom']) && !empty($data['dateto'])) {
            if ($data['datefrom'] > $data['dateto']) {
                $errors['dateto'] = 'End date must be after start date';
            }
        }

        return $errors;
    }
}
