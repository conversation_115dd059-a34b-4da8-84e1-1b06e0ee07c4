<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Staff user registration form for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\form;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/formslib.php');

/**
 * Form for staff to register users with license keys.
 */
class staff_user_registration_form extends \moodleform {

    /**
     * Define the form.
     */
    public function definition() {
        global $DB;

        $mform = $this->_form;

        // User type selection
        $usertypes = array(
            'partner_student' => get_string('partnerstudent', 'local_licensetracker'),
            'local_student' => get_string('localstudent', 'local_licensetracker')
        );
        $mform->addElement('select', 'usertype', get_string('usertype', 'local_licensetracker'), $usertypes);
        $mform->addRule('usertype', null, 'required', null, 'client');

        // License key
        $mform->addElement('text', 'licensekey', get_string('licensekey', 'local_licensetracker'));
        $mform->setType('licensekey', PARAM_TEXT);
        $mform->addRule('licensekey', null, 'required', null, 'client');
        $mform->addHelpButton('licensekey', 'licensekey', 'local_licensetracker');

        // Course type selection
        $coursetypes = $DB->get_records_menu('local_lt_coursetypes', null, 'name', 'id, name');
        $coursetypes = array('' => get_string('selectcoursetype', 'local_licensetracker')) + $coursetypes;
        $mform->addElement('select', 'coursetypeid', get_string('coursetype', 'local_licensetracker'), $coursetypes);
        $mform->addRule('coursetypeid', null, 'required', null, 'client');

        // User details
        $mform->addElement('header', 'userdetails', get_string('userdetails', 'local_licensetracker'));

        $mform->addElement('text', 'username', get_string('username', 'local_licensetracker'));
        $mform->setType('username', PARAM_USERNAME);
        $mform->addRule('username', null, 'required', null, 'client');

        $mform->addElement('text', 'firstname', get_string('firstname', 'local_licensetracker'));
        $mform->setType('firstname', PARAM_TEXT);
        $mform->addRule('firstname', null, 'required', null, 'client');

        $mform->addElement('text', 'lastname', get_string('lastname', 'local_licensetracker'));
        $mform->setType('lastname', PARAM_TEXT);
        $mform->addRule('lastname', null, 'required', null, 'client');

        $mform->addElement('text', 'email', get_string('email', 'local_licensetracker'));
        $mform->setType('email', PARAM_EMAIL);
        $mform->addRule('email', null, 'required', null, 'client');
        $mform->addRule('email', null, 'email', null, 'client');

        $mform->addElement('password', 'password', get_string('password', 'local_licensetracker'));
        $mform->addRule('password', null, 'required', null, 'client');
        $mform->addRule('password', null, 'minlength', 8, 'client');

        $mform->addElement('text', 'city', get_string('city', 'local_licensetracker'));
        $mform->setType('city', PARAM_TEXT);

        // Country selection
        $countries = get_string_manager()->get_list_of_countries();
        $countries = array('' => get_string('selectacountry')) + $countries;
        $mform->addElement('select', 'country', get_string('country', 'local_licensetracker'), $countries);
        $mform->addRule('country', null, 'required', null, 'client');

        // Action buttons
        $this->add_action_buttons(true, get_string('registeruser', 'local_licensetracker'));
    }

    /**
     * Validate the form data.
     *
     * @param array $data Form data
     * @param array $files Form files
     * @return array Validation errors
     */
    public function validation($data, $files) {
        global $DB;
        
        $errors = parent::validation($data, $files);

        // Check for duplicate username
        if (!empty($data['username'])) {
            if ($DB->record_exists('user', array('username' => $data['username'], 'deleted' => 0))) {
                $errors['username'] = get_string('usernametaken', 'core');
            }
        }

        // Check for duplicate email
        if (!empty($data['email'])) {
            if ($DB->record_exists('user', array('email' => $data['email'], 'deleted' => 0))) {
                $errors['email'] = get_string('emailexists', 'core');
            }
        }

        // Validate license key if provided
        if (!empty($data['licensekey']) && !empty($data['coursetypeid'])) {
            $validation = local_licensetracker_validate_key_for_course($data['licensekey'], $data['coursetypeid']);
            if (!$validation['valid']) {
                $errors['licensekey'] = $validation['message'];
            }
        }

        return $errors;
    }
}
