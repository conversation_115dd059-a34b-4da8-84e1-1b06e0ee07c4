<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Navigation helper for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker;

defined('MOODLE_INTERNAL') || die();

/**
 * Helper class for navigation and breadcrumbs.
 */
class navigation_helper {

    /**
     * Add breadcrumb navigation for license tracker pages.
     *
     * @param string $page Current page identifier
     * @param array $params Additional parameters
     */
    public static function add_breadcrumbs($page, $params = array()) {
        global $PAGE;

        // Base breadcrumb
        $PAGE->navbar->add(get_string('pluginname', 'local_licensetracker'), 
                          new \moodle_url('/local/licensetracker/index.php'));

        switch ($page) {
            case 'admin_dashboard':
                $PAGE->navbar->add(get_string('admindashboard', 'local_licensetracker'));
                break;
                
            case 'partner_dashboard':
                $PAGE->navbar->add(get_string('partnerdashboard', 'local_licensetracker'));
                if (!empty($params['partnername'])) {
                    $PAGE->navbar->add($params['partnername']);
                }
                break;
                
            case 'security_audit':
                $PAGE->navbar->add(get_string('securityaudit', 'local_licensetracker'));
                break;
                
            case 'system_validation':
                $PAGE->navbar->add(get_string('systemvalidation', 'local_licensetracker'));
                break;
                
            case 'partner_details':
                $PAGE->navbar->add(get_string('partners', 'local_licensetracker'), 
                                  new \moodle_url('/local/licensetracker/index.php', array('tab' => 'partners')));
                if (!empty($params['partnername'])) {
                    $PAGE->navbar->add($params['partnername']);
                }
                break;
        }
    }

    /**
     * Get navigation menu for admin users.
     *
     * @param string $current_tab Current active tab
     * @return array Navigation menu items
     */
    public static function get_admin_navigation($current_tab = '') {
        global $CFG;

        $menu = array(
            'overview' => array(
                'name' => get_string('overview', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'overview')),
                'icon' => 'fa-dashboard',
                'active' => ($current_tab === 'overview')
            ),
            'keys' => array(
                'name' => get_string('keys', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'keys')),
                'icon' => 'fa-key',
                'active' => ($current_tab === 'keys')
            ),
            'generatekeys' => array(
                'name' => get_string('generatekeys', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'generatekeys')),
                'icon' => 'fa-plus-circle',
                'active' => ($current_tab === 'generatekeys')
            ),
            'manualkeyaddition' => array(
                'name' => get_string('manualkeyaddition', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'manualkeyaddition')),
                'icon' => 'fa-edit',
                'active' => ($current_tab === 'manualkeyaddition')
            ),
            'partners' => array(
                'name' => get_string('partners', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'partners')),
                'icon' => 'fa-users',
                'active' => ($current_tab === 'partners')
            ),
            'coursetypes' => array(
                'name' => get_string('coursetypes', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'coursetypes')),
                'icon' => 'fa-tags',
                'active' => ($current_tab === 'coursetypes')
            ),
            'registeruser' => array(
                'name' => get_string('registeruser', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'registeruser')),
                'icon' => 'fa-user-plus',
                'active' => ($current_tab === 'registeruser')
            ),
            'statistics' => array(
                'name' => get_string('statistics', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'statistics')),
                'icon' => 'fa-bar-chart',
                'active' => ($current_tab === 'statistics')
            ),
            'security' => array(
                'name' => get_string('security', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'security')),
                'icon' => 'fa-shield',
                'active' => ($current_tab === 'security')
            )
        );

        return $menu;
    }

    /**
     * Get navigation menu for partner users.
     *
     * @param string $current_tab Current active tab
     * @return array Navigation menu items
     */
    public static function get_partner_navigation($current_tab = '') {
        $menu = array(
            'assignedkeys' => array(
                'name' => get_string('assignedkeys', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'assignedkeys')),
                'icon' => 'fa-key',
                'active' => ($current_tab === 'assignedkeys')
            ),
            'statistics' => array(
                'name' => get_string('statistics', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'statistics')),
                'icon' => 'fa-bar-chart',
                'active' => ($current_tab === 'statistics')
            ),
            'register' => array(
                'name' => get_string('registernewstudent', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'register')),
                'icon' => 'fa-user-plus',
                'active' => ($current_tab === 'register')
            ),
            'bulkregister' => array(
                'name' => get_string('bulkregisterstudents', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'bulkregister')),
                'icon' => 'fa-upload',
                'active' => ($current_tab === 'bulkregister')
            ),
            'requestlicenses' => array(
                'name' => get_string('requestmorelicenses', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'requestlicenses')),
                'icon' => 'fa-shopping-cart',
                'active' => ($current_tab === 'requestlicenses')
            )
        );

        return $menu;
    }

    /**
     * Get quick action buttons for admin dashboard.
     *
     * @return array Quick action buttons
     */
    public static function get_admin_quick_actions() {
        global $CFG;

        return array(
            array(
                'name' => get_string('generatekeys', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'generatekeys')),
                'icon' => 'fa-plus-circle',
                'class' => 'btn-primary'
            ),
            array(
                'name' => get_string('addpartner', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'partners', 'action' => 'add')),
                'icon' => 'fa-user-plus',
                'class' => 'btn-success'
            ),
            array(
                'name' => get_string('securityaudit', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/security_audit.php'),
                'icon' => 'fa-shield',
                'class' => 'btn-warning'
            ),
            array(
                'name' => get_string('advancedtools', 'local_licensetracker'),
                'url' => new \moodle_url('/admin/tool/licensetrackeradmin/'),
                'icon' => 'fa-cogs',
                'class' => 'btn-info'
            )
        );
    }

    /**
     * Get quick action buttons for partner dashboard.
     *
     * @return array Quick action buttons
     */
    public static function get_partner_quick_actions() {
        return array(
            array(
                'name' => get_string('registernewstudent', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'register')),
                'icon' => 'fa-user-plus',
                'class' => 'btn-primary'
            ),
            array(
                'name' => get_string('bulkregisterstudents', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'bulkregister')),
                'icon' => 'fa-upload',
                'class' => 'btn-success'
            ),
            array(
                'name' => get_string('requestmorelicenses', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'requestlicenses')),
                'icon' => 'fa-shopping-cart',
                'class' => 'btn-info'
            )
        );
    }

    /**
     * Get contextual help links for current page.
     *
     * @param string $page Current page identifier
     * @return array Help links
     */
    public static function get_help_links($page) {
        global $CFG;

        $base_help = array(
            array(
                'name' => get_string('documentation', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/README.md'),
                'icon' => 'fa-book'
            ),
            array(
                'name' => get_string('systemvalidation', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/system_validation.php'),
                'icon' => 'fa-check-circle'
            )
        );

        $page_specific = array();

        switch ($page) {
            case 'admin_dashboard':
                $page_specific = array(
                    array(
                        'name' => get_string('adminmanual', 'local_licensetracker'),
                        'url' => new \moodle_url('/local/licensetracker/ADMINISTRATOR_MANUAL.md'),
                        'icon' => 'fa-file-text'
                    )
                );
                break;
                
            case 'partner_dashboard':
                $page_specific = array(
                    array(
                        'name' => get_string('partnermanual', 'local_licensetracker'),
                        'url' => new \moodle_url('/local/licensetracker/PARTNER_MANUAL.md'),
                        'icon' => 'fa-file-text'
                    )
                );
                break;
        }

        return array_merge($base_help, $page_specific);
    }

    /**
     * Check if user has access to admin features.
     *
     * @return bool True if user has admin access
     */
    public static function has_admin_access() {
        $context = \context_system::instance();
        return has_capability('local/licensetracker:manageallkeys', $context);
    }

    /**
     * Check if user has access to partner features.
     *
     * @return bool True if user has partner access
     */
    public static function has_partner_access() {
        $context = \context_system::instance();
        return has_capability('local/licensetracker:viewpartnerkeys', $context);
    }

    /**
     * Get user's partner record if they are a partner.
     *
     * @return object|false Partner record or false
     */
    public static function get_user_partner() {
        global $DB, $USER;

        return $DB->get_record('local_lt_partners', array('userid' => $USER->id));
    }
}
