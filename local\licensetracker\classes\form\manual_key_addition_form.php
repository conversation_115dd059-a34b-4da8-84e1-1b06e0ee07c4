<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Manual key addition form for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\form;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/formslib.php');

/**
 * Form for administrators to manually add specific license keys.
 */
class manual_key_addition_form extends \moodleform {

    /**
     * Define the form.
     */
    public function definition() {
        global $DB;

        $mform = $this->_form;

        // Header
        $mform->addElement('header', 'manualkey', get_string('manualkeyaddition', 'local_licensetracker'));
        
        // License key string
        $mform->addElement('text', 'keystring', get_string('licensekey', 'local_licensetracker'));
        $mform->setType('keystring', PARAM_TEXT);
        $mform->addRule('keystring', null, 'required', null, 'client');
        $mform->addRule('keystring', null, 'maxlength', 255, 'client');
        $mform->addHelpButton('keystring', 'manualkeystring', 'local_licensetracker');

        // Partner selection
        $partners = $DB->get_records_menu('local_lt_partners', null, 'partnername', 'id, partnername');
        if (empty($partners)) {
            $mform->addElement('static', 'nopartners', '', 
                get_string('nopartnersavailable', 'local_licensetracker'));
            return;
        }
        $mform->addElement('select', 'partnerid', get_string('partner', 'local_licensetracker'), $partners);
        $mform->addRule('partnerid', null, 'required', null, 'client');
        $mform->addHelpButton('partnerid', 'selectpartner', 'local_licensetracker');

        // Course type selection
        $coursetypes = $DB->get_records_menu('local_lt_coursetypes', null, 'name', 'id, name');
        if (empty($coursetypes)) {
            $mform->addElement('static', 'nocoursetypes', '', 
                get_string('nocoursetypesavailable', 'local_licensetracker'));
            return;
        }
        $mform->addElement('select', 'coursetypeid', get_string('coursetype', 'local_licensetracker'), $coursetypes);
        $mform->addRule('coursetypeid', null, 'required', null, 'client');
        $mform->addHelpButton('coursetypeid', 'selectcoursetype', 'local_licensetracker');

        // Valid from date (optional)
        $mform->addElement('date_time_selector', 'validfrom', get_string('validfrom', 'local_licensetracker'), 
                          array('optional' => true));
        $mform->addHelpButton('validfrom', 'validfrom', 'local_licensetracker');

        // Expires on date (optional)
        $mform->addElement('date_time_selector', 'expireson', get_string('expireson', 'local_licensetracker'), 
                          array('optional' => true));
        $mform->addHelpButton('expireson', 'expireson', 'local_licensetracker');

        // Status selection (default to available)
        $statusoptions = array(
            'available' => get_string('available', 'local_licensetracker'),
            'revoked' => get_string('revoked', 'local_licensetracker')
        );
        $mform->addElement('select', 'status', get_string('status', 'local_licensetracker'), $statusoptions);
        $mform->setDefault('status', 'available');
        $mform->addHelpButton('status', 'keystatus', 'local_licensetracker');

        // Notes field
        $mform->addElement('textarea', 'notes', get_string('notes', 'local_licensetracker'), 
                          array('rows' => 3, 'cols' => 50));
        $mform->setType('notes', PARAM_TEXT);
        $mform->addHelpButton('notes', 'keynotes', 'local_licensetracker');

        // Action buttons
        $this->add_action_buttons(true, get_string('addkey', 'local_licensetracker'));
    }

    /**
     * Validate the form data.
     *
     * @param array $data Form data
     * @param array $files Form files
     * @return array Validation errors
     */
    public function validation($data, $files) {
        global $DB;
        
        $errors = parent::validation($data, $files);

        // Validate key string format and uniqueness
        if (!empty($data['keystring'])) {
            // Check if key already exists
            if ($DB->record_exists('local_lt_keys', array('keystring' => $data['keystring']))) {
                $errors['keystring'] = get_string('error:keyalreadyexists', 'local_licensetracker');
            }

            // Validate key format (basic validation)
            $keystring = trim($data['keystring']);
            if (strlen($keystring) < 5) {
                $errors['keystring'] = get_string('error:keytooShort', 'local_licensetracker');
            }

            // Check if key contains only allowed characters
            if (!preg_match('/^[A-Z0-9\-]+$/', $keystring)) {
                $errors['keystring'] = get_string('error:invalidkeyformat', 'local_licensetracker');
            }
        }

        // Validate partner and course type combination
        if (!empty($data['partnerid']) && !empty($data['coursetypeid'])) {
            // Check if partner is assigned to this course type
            $assigned = $DB->record_exists('local_lt_partner_coursetypes', 
                array('partnerid' => $data['partnerid'], 'coursetypeid' => $data['coursetypeid']));
            
            if (!$assigned) {
                $partner = $DB->get_record('local_lt_partners', array('id' => $data['partnerid']));
                $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $data['coursetypeid']));
                $errors['coursetypeid'] = get_string('error:partnernotassignedtocoursetype', 'local_licensetracker', 
                    array('partner' => $partner->partnername, 'coursetype' => $coursetype->name));
            }
        }

        // Validate date range
        if (!empty($data['validfrom']) && !empty($data['expireson'])) {
            if ($data['validfrom'] >= $data['expireson']) {
                $errors['expireson'] = get_string('error:expirydatebeforevalidfrom', 'local_licensetracker');
            }
        }

        // Validate notes length
        if (!empty($data['notes']) && strlen($data['notes']) > 500) {
            $errors['notes'] = get_string('error:notestoolong', 'local_licensetracker');
        }

        return $errors;
    }
}
