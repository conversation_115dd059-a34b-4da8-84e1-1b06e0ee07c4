{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template local_licensetracker/partner_dashboard

    Partner dashboard template for license tracker.

    Context variables required for this template:
    * title - Dashboard title
    * partnername - Partner name
    * tabs - Array of tab objects
    * activetab - Currently active tab
    * content - Tab content HTML
    * wwwroot - Moodle WWW root
}}

<div class="license-tracker-partner-dashboard">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2>{{title}}</h2>
            {{#partnername}}
            <p class="text-muted">{{#str}}partner, local_licensetracker{{/str}}: {{partnername}}</p>
            {{/partnername}}
        </div>
    </div>

    <ul class="nav nav-tabs mb-4" role="tablist">
        {{#tabs}}
        <li class="nav-item" role="presentation">
            <a class="nav-link {{#active}}active{{/active}}" 
               href="{{url}}" 
               role="tab">
                {{name}}
            </a>
        </li>
        {{/tabs}}
    </ul>

    <div class="tab-content">
        <div class="tab-pane fade show active" role="tabpanel">
            {{{content}}}
        </div>
    </div>
</div>

{{#js}}
require(['jquery'], function($) {
    // Initialize any JavaScript functionality
    $('.license-tracker-partner-dashboard').ready(function() {
        // Add any partner dashboard-specific JavaScript here
    });
});
{{/js}}
