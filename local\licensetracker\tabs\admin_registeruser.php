<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Admin user registration tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

// CRITICAL: Verify admin permissions
$context = context_system::instance();
require_capability('local/licensetracker:manageallkeys', $context);

$form = new \local_licensetracker\form\staff_user_registration_form();

if ($form->is_cancelled()) {
    redirect(new moodle_url('/local/licensetracker/index.php', array('tab' => 'registeruser')));
} else if ($data = $form->get_data()) {
    
    // Validate license key for the selected course type
    $validation = local_licensetracker_validate_key_for_course($data->licensekey, $data->coursetypeid);
    
    if (!$validation['valid']) {
        redirect($PAGE->url, $validation['message'], null, \core\output\notification::NOTIFY_ERROR);
    }
    
    // Get the validated key
    $key = local_licensetracker_validate_key($data->licensekey);
    
    try {
        // Create user account
        $user = new stdClass();
        $user->username = $data->username;
        $user->firstname = $data->firstname;
        $user->lastname = $data->lastname;
        $user->email = $data->email;
        $user->city = $data->city;
        $user->country = $data->country;
        $user->auth = 'manual';
        $user->confirmed = 1;
        $user->mnethostid = $CFG->mnet_localhost_id;
        $user->timecreated = time();
        $user->timemodified = time();
        
        // Set password
        $user->password = hash_internal_user_password($data->password);
        
        // Create the user
        $userid = user_create_user($user, false, false);

        // Set user type based on selection
        $usertype = ($data->usertype === 'partner_student') ? 'partner_student' : 'local_student';
        local_licensetracker_set_user_type($userid, $usertype);
        
        if (!$userid) {
            throw new moodle_exception('cannotcreateuser');
        }
        
        // Update license key status
        $ipaddress = getremoteaddr();
        $useragent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        local_licensetracker_update_key_status($key->id, 'used', $userid, $ipaddress, $useragent);
        
        // Get course and enrollment information
        $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $key->coursetypeid));
        if ($coursetype && $coursetype->moodlecourseid) {
            $course = $DB->get_record('course', array('id' => $coursetype->moodlecourseid));
            if ($course) {
                // Enroll user in course
                $enrolmethod = $coursetype->enrolmethod ?: 'manual';
                $roleid = $coursetype->defaultroleid ?: 5; // Default to student role
                
                $enrol = enrol_get_plugin($enrolmethod);
                if (!$enrol) {
                    $enrol = enrol_get_plugin('manual'); // Fallback to manual
                    $enrolmethod = 'manual';
                }
                
                // Get or create enrollment instance
                $instance = $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => $enrolmethod));
                if (!$instance) {
                    $instanceid = $enrol->add_instance($course);
                    $instance = $DB->get_record('enrol', array('id' => $instanceid));
                }
                
                // Enroll the user
                $enrol->enrol_user($instance, $userid, $roleid, time(), 0, ENROL_USER_ACTIVE);
            }
        }
        
        $message = get_string('userregistered', 'local_licensetracker', $user->username);
        redirect($PAGE->url, $message, null, \core\output\notification::NOTIFY_SUCCESS);
        
    } catch (Exception $e) {
        redirect($PAGE->url, 'Error creating user: ' . $e->getMessage(), null, \core\output\notification::NOTIFY_ERROR);
    }
}

$output = '';

$output .= '<div class="card">';
$output .= '<div class="card-header">';
$output .= '<h4>' . get_string('registeruser', 'local_licensetracker') . '</h4>';
$output .= '<p class="text-muted">' . get_string('staffonlyregistration', 'local_licensetracker') . '</p>';
$output .= '</div>';
$output .= '<div class="card-body">';

$form->display();

$output .= '</div>';
$output .= '</div>';

return $output;
