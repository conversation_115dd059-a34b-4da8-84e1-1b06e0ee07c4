{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template local_licensetracker/key_table

    Key table template for license tracker.

    Context variables required for this template:
    * keys - Array of key objects
    * isadmin - Whether user is admin
    * haskeys - Whether there are keys to display
    * wwwroot - Moodle WWW root
}}

<div class="license-key-table">
    {{#haskeys}}
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="thead-dark">
                <tr>
                    <th>{{#str}}keystring, local_licensetracker{{/str}}</th>
                    <th>{{#str}}partner, local_licensetracker{{/str}}</th>
                    <th>{{#str}}coursetype, local_licensetracker{{/str}}</th>
                    <th>{{#str}}status, local_licensetracker{{/str}}</th>
                    {{#isadmin}}
                    <th>{{#str}}validfrom, local_licensetracker{{/str}}</th>
                    <th>{{#str}}expireson, local_licensetracker{{/str}}</th>
                    {{/isadmin}}
                    <th>{{#str}}usedby, local_licensetracker{{/str}}</th>
                    <th>{{#str}}dateused, local_licensetracker{{/str}}</th>
                    {{#isadmin}}
                    <th>{{#str}}actions, local_licensetracker{{/str}}</th>
                    {{/isadmin}}
                </tr>
            </thead>
            <tbody>
                {{#keys}}
                <tr>
                    <td><code>{{keystring}}</code></td>
                    <td>{{partnername}}</td>
                    <td>{{coursetypename}}</td>
                    <td>
                        <span class="badge {{statusclass}}">
                            {{#str}}{{status}}, local_licensetracker{{/str}}
                        </span>
                    </td>
                    {{#isadmin}}
                    <td>{{validfrom}}</td>
                    <td>{{expireson}}</td>
                    {{/isadmin}}
                    <td>{{usedby}}</td>
                    <td>{{dateused}}</td>
                    {{#isadmin}}
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            {{#canrevoke}}
                            <a href="{{wwwroot}}/local/licensetracker/index.php?tab=keys&action=revokekey&id={{id}}&sesskey={{sesskey}}" 
                               class="btn btn-warning btn-sm"
                               onclick="return confirm('{{#str}}confirmrevoke, local_licensetracker{{/str}}')">
                                {{#str}}revoke, local_licensetracker{{/str}}
                            </a>
                            {{/canrevoke}}
                            {{#candelete}}
                            <a href="{{wwwroot}}/local/licensetracker/index.php?tab=keys&action=deletekey&id={{id}}&sesskey={{sesskey}}" 
                               class="btn btn-danger btn-sm"
                               onclick="return confirm('{{#str}}confirmdelete, core{{/str}}')">
                                {{#str}}delete, local_licensetracker{{/str}}
                            </a>
                            {{/candelete}}
                        </div>
                    </td>
                    {{/isadmin}}
                </tr>
                {{/keys}}
            </tbody>
        </table>
    </div>
    {{/haskeys}}

    {{^haskeys}}
    <div class="alert alert-info">
        <i class="fa fa-info-circle"></i>
        {{#str}}nokeysavailable, local_licensetracker{{/str}}
    </div>
    {{/haskeys}}
</div>

{{#js}}
require(['jquery'], function($) {
    // Initialize DataTables or other table functionality
    $('.license-key-table table').ready(function() {
        // Add sorting, pagination, etc.
    });
});
{{/js}}
