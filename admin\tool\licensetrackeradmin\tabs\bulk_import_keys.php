<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Bulk import keys tab for tool_licensetrackeradmin plugin.
 *
 * @package    tool_licensetrackeradmin
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

require_capability('tool/licensetrackeradmin:bulkimport', $context);

$form = new \tool_licensetrackeradmin\form\bulk_import_keys_form();

if ($form->is_cancelled()) {
    redirect($PAGE->url);
} else if ($data = $form->get_data()) {
    
    // Handle file upload
    $fs = get_file_storage();
    $usercontext = context_user::instance($USER->id);
    
    // Get uploaded file
    $files = $fs->get_area_files($usercontext->id, 'user', 'draft', $data->csvfile, 'id DESC', false);
    $file = reset($files);
    
    if ($file) {
        // Save file to temp location
        $tempdir = make_temp_directory('licensetrackeradmin');
        $tempfile = $tempdir . '/' . uniqid() . '_' . $file->get_filename();
        $file->copy_content_to($tempfile);
        
        // Prepare task data
        $taskdata = new stdClass();
        $taskdata->filepath = $tempfile;
        $taskdata->options = new stdClass();
        $taskdata->options->skipfirstrow = !empty($data->skipfirstrow);
        $taskdata->options->updateexisting = !empty($data->updateexisting);
        $taskdata->userid = $USER->id;
        
        // Queue import task
        $task = new \tool_licensetrackeradmin\task\import_keys_task();
        $task->set_custom_data($taskdata);
        \core\task\manager::queue_adhoc_task($task);
        
        redirect($PAGE->url, get_string('importstarted', 'tool_licensetrackeradmin'), 
                null, \core\output\notification::NOTIFY_SUCCESS);
    } else {
        redirect($PAGE->url, get_string('error:invalidfile', 'tool_licensetrackeradmin'), 
                null, \core\output\notification::NOTIFY_ERROR);
    }
}

echo '<div class="card">';
echo '<div class="card-header">';
echo '<h3>' . get_string('bulkimportkeys', 'tool_licensetrackeradmin') . '</h3>';
echo '</div>';
echo '<div class="card-body">';

echo '<div class="alert alert-info">';
echo '<h5>' . get_string('csvformat', 'tool_licensetrackeradmin') . '</h5>';
echo '<p>' . get_string('csvheader_keys', 'tool_licensetrackeradmin') . '</p>';
echo '<ul>';
echo '<li><strong>keystring</strong>: The license key string (required)</li>';
echo '<li><strong>coursetypename</strong>: Name of the course type (required)</li>';
echo '<li><strong>partnername</strong>: Name of the partner (required)</li>';
echo '<li><strong>validfrom</strong>: Valid from date in YYYY-MM-DD format (optional)</li>';
echo '<li><strong>expireson</strong>: Expires on date in YYYY-MM-DD format (optional)</li>';
echo '</ul>';
echo '</div>';

$form->display();

echo '</div>';
echo '</div>';
