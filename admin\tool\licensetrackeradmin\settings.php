<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Settings for tool_licensetrackeradmin plugin.
 *
 * @package    tool_licensetrackeradmin
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

if ($hassiteconfig) {
    $ADMIN->add('tools', new admin_externalpage(
        'tool_licensetrackeradmin',
        get_string('pluginname', 'tool_licensetrackeradmin'),
        new moodle_url('/admin/tool/licensetrackeradmin/index.php'),
        'tool/licensetrackeradmin:manage'
    ));

    $settings = new admin_settingpage('tool_licensetrackeradmin_settings', 
                                     get_string('settings', 'tool_licensetrackeradmin'));

    if ($ADMIN->fulltree) {
        // Key format configuration
        $settings->add(new admin_setting_configtextarea(
            'tool_licensetrackeradmin/keyformat',
            get_string('keyformat', 'tool_licensetrackeradmin'),
            get_string('keyformat_desc', 'tool_licensetrackeradmin'),
            get_string('keyformat_default', 'tool_licensetrackeradmin'),
            PARAM_TEXT
        ));

        // API key for external access
        $settings->add(new admin_setting_configpasswordunmask(
            'tool_licensetrackeradmin/apikey',
            get_string('apikey', 'tool_licensetrackeradmin'),
            get_string('apikey_desc', 'tool_licensetrackeradmin'),
            ''
        ));
    }

    $ADMIN->add('tools', $settings);
}
