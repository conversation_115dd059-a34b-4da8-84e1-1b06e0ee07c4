<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * System status and health check page.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once(__DIR__ . '/../../config.php');
require_once($CFG->dirroot . '/local/licensetracker/lib.php');

require_login();

$context = context_system::instance();
$PAGE->set_context($context);
$PAGE->set_url('/local/licensetracker/system_status.php');
$PAGE->set_title('License Tracker - System Status');
$PAGE->set_heading('License Tracker - System Status');

// Require admin permissions
require_capability('local/licensetracker:manageallkeys', $context);

$PAGE->set_pagelayout('admin');

echo $OUTPUT->header();

// Get system configuration
$config = get_config('local_licensetracker');
$authconfig = get_config('auth_licensetracker');

// System status checks
$systemstatus = array();
$systemstatus['enabled'] = !empty($config->enabled);
$systemstatus['maintenance'] = !empty($config->maintenance_mode);
$systemstatus['auth_enabled'] = !empty($authconfig->enabled);
$systemstatus['enforce_all'] = !empty($config->enforce_all);

// Get statistics
$stats = local_licensetracker_get_partner_stats();
$userstats = local_licensetracker_get_user_type_stats();

echo '<div class="row">';

// System Status Card
echo '<div class="col-md-6">';
echo '<div class="card">';
echo '<div class="card-header bg-primary text-white">';
echo '<h4><i class="fa fa-heartbeat"></i> ' . get_string('system_status', 'local_licensetracker') . '</h4>';
echo '</div>';
echo '<div class="card-body">';

if ($systemstatus['maintenance']) {
    echo '<div class="alert alert-warning">';
    echo '<i class="fa fa-wrench"></i> ' . get_string('system_maintenance', 'local_licensetracker');
    echo '</div>';
} else {
    echo '<div class="alert alert-success">';
    echo '<i class="fa fa-check-circle"></i> ' . get_string('system_operational', 'local_licensetracker');
    echo '</div>';
}

if ($systemstatus['enabled'] && $systemstatus['auth_enabled']) {
    echo '<div class="alert alert-info">';
    echo '<i class="fa fa-shield"></i> ' . get_string('license_enforcement_active', 'local_licensetracker');
    echo '</div>';
} else {
    echo '<div class="alert alert-warning">';
    echo '<i class="fa fa-exclamation-triangle"></i> ' . get_string('license_enforcement_disabled', 'local_licensetracker');
    echo '</div>';
}

echo '<table class="table table-sm">';
echo '<tr><td><strong>System Enabled:</strong></td><td>' . ($systemstatus['enabled'] ? '✅ Yes' : '❌ No') . '</td></tr>';
echo '<tr><td><strong>Auth Plugin Enabled:</strong></td><td>' . ($systemstatus['auth_enabled'] ? '✅ Yes' : '❌ No') . '</td></tr>';
echo '<tr><td><strong>Enforce All Students:</strong></td><td>' . ($systemstatus['enforce_all'] ? '✅ Yes' : '❌ No') . '</td></tr>';
echo '<tr><td><strong>Maintenance Mode:</strong></td><td>' . ($systemstatus['maintenance'] ? '⚠️ Yes' : '✅ No') . '</td></tr>';
echo '</table>';

echo '</div>';
echo '</div>';
echo '</div>';

// Quick Statistics Card
echo '<div class="col-md-6">';
echo '<div class="card">';
echo '<div class="card-header bg-success text-white">';
echo '<h4><i class="fa fa-chart-bar"></i> ' . get_string('quick_stats', 'local_licensetracker') . '</h4>';
echo '</div>';
echo '<div class="card-body">';

echo '<div class="row text-center">';
echo '<div class="col-6">';
echo '<h3 class="text-primary">' . $stats->total . '</h3>';
echo '<small>Total Keys</small>';
echo '</div>';
echo '<div class="col-6">';
echo '<h3 class="text-success">' . $stats->available . '</h3>';
echo '<small>Available</small>';
echo '</div>';
echo '</div>';

echo '<div class="row text-center mt-2">';
echo '<div class="col-6">';
echo '<h3 class="text-info">' . $stats->used . '</h3>';
echo '<small>Used Keys</small>';
echo '</div>';
echo '<div class="col-6">';
echo '<h3 class="text-danger">' . $stats->revoked . '</h3>';
echo '<small>Revoked</small>';
echo '</div>';
echo '</div>';

echo '<hr>';

$partnercount = $DB->count_records('local_lt_partners');
$coursetypecount = $DB->count_records('local_lt_coursetypes');

echo '<div class="row text-center">';
echo '<div class="col-6">';
echo '<h4 class="text-secondary">' . $partnercount . '</h4>';
echo '<small>Partners</small>';
echo '</div>';
echo '<div class="col-6">';
echo '<h4 class="text-secondary">' . $coursetypecount . '</h4>';
echo '<small>Course Types</small>';
echo '</div>';
echo '</div>';

echo '</div>';
echo '</div>';
echo '</div>';

echo '</div>'; // End row

// System Health Checks
echo '<div class="row mt-4">';
echo '<div class="col-12">';
echo '<div class="card">';
echo '<div class="card-header bg-info text-white">';
echo '<h4><i class="fa fa-stethoscope"></i> ' . get_string('system_health', 'local_licensetracker') . '</h4>';
echo '</div>';
echo '<div class="card-body">';

$healthchecks = array();

// Database connectivity
try {
    $DB->get_record('local_lt_keys', array(), '*', IGNORE_MULTIPLE);
    $healthchecks['database'] = array('status' => true, 'message' => 'Database connectivity OK');
} catch (Exception $e) {
    $healthchecks['database'] = array('status' => false, 'message' => 'Database error: ' . $e->getMessage());
}

// Table integrity
$tables = array('local_lt_keys', 'local_lt_partners', 'local_lt_coursetypes', 'local_lt_partner_courses');
$tablecheck = true;
foreach ($tables as $table) {
    if (!$DB->get_manager()->table_exists($table)) {
        $tablecheck = false;
        break;
    }
}
$healthchecks['tables'] = array('status' => $tablecheck, 'message' => $tablecheck ? 'All tables exist' : 'Missing database tables');

// Capability definitions
$caps = array('local/licensetracker:manageallkeys', 'local/licensetracker:viewpartnerkeys');
$capcheck = true;
foreach ($caps as $cap) {
    if (!$DB->record_exists('capabilities', array('name' => $cap))) {
        $capcheck = false;
        break;
    }
}
$healthchecks['capabilities'] = array('status' => $capcheck, 'message' => $capcheck ? 'All capabilities defined' : 'Missing capability definitions');

// Event observers
$observers = $DB->get_records('events_handlers', array('component' => 'local_licensetracker'));
$healthchecks['observers'] = array('status' => !empty($observers), 'message' => !empty($observers) ? 'Event observers registered' : 'No event observers found');

// Display health checks
echo '<div class="row">';
foreach ($healthchecks as $check => $result) {
    $icon = $result['status'] ? 'fa-check-circle text-success' : 'fa-times-circle text-danger';
    echo '<div class="col-md-6 mb-2">';
    echo '<i class="fa ' . $icon . '"></i> <strong>' . ucfirst($check) . ':</strong> ' . $result['message'];
    echo '</div>';
}
echo '</div>';

echo '</div>';
echo '</div>';
echo '</div>';
echo '</div>';

// Action buttons
echo '<div class="mt-4 text-center">';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php" class="btn btn-primary">Dashboard</a>';
echo '<a href="' . $CFG->wwwroot . '/admin/settings.php?section=local_licensetracker_settings" class="btn btn-secondary ml-2">Settings</a>';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/admin_verification.php" class="btn btn-info ml-2">Admin Verification</a>';
echo '</div>';

echo $OUTPUT->footer();
