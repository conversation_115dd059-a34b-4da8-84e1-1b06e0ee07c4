/* License Tracker Plugin Styles */

/* Dashboard Enhancements */
.license-tracker-dashboard,
.license-tracker-partner-dashboard {
    margin-bottom: 2rem;
}

.license-tracker-dashboard .nav-tabs,
.license-tracker-partner-dashboard .nav-tabs {
    border-bottom: 2px solid #dee2e6;
}

.license-tracker-dashboard .nav-link,
.license-tracker-partner-dashboard .nav-link {
    color: #495057;
    font-weight: 500;
    padding: 0.75rem 1.25rem;
    border: none;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.license-tracker-dashboard .nav-link:hover,
.license-tracker-partner-dashboard .nav-link:hover {
    color: #007bff;
    border-bottom-color: #007bff;
    background-color: transparent;
}

.license-tracker-dashboard .nav-link.active,
.license-tracker-partner-dashboard .nav-link.active {
    color: #007bff;
    background-color: transparent;
    border-bottom-color: #007bff;
    font-weight: 600;
}

/* Card Enhancements */
.license-key-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.license-key-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Statistics Cards */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card h2 {
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 0;
}

.stats-card .card-title {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.9;
}

/* Status Badges */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-available {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-used {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-revoked {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-expired {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Table Enhancements */
.license-key-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.license-key-table table {
    margin-bottom: 0;
}

.license-key-table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    padding: 1rem 0.75rem;
}

.license-key-table tbody tr {
    transition: background-color 0.2s ease;
}

.license-key-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Form Enhancements */
.license-form {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.license-form .form-group {
    margin-bottom: 1.5rem;
}

.license-form label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.license-form .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.75rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.license-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Button Enhancements */
.btn-license-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 6px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-license-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

/* Alert Enhancements */
.alert-license {
    border: none;
    border-radius: 8px;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
}

.alert-license.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.alert-license.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-license.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-license.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

/* Loading States */
.loading-overlay {
    position: relative;
}

.loading-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .license-tracker-dashboard .nav-tabs,
    .license-tracker-partner-dashboard .nav-tabs {
        flex-wrap: wrap;
    }
    
    .license-tracker-dashboard .nav-link,
    .license-tracker-partner-dashboard .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }
    
    .stats-card h2 {
        font-size: 2rem;
    }
    
    .license-form {
        padding: 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .license-key-table {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .license-key-table thead th {
        background-color: #4a5568;
        color: #e2e8f0;
        border-bottom-color: #4a5568;
    }
    
    .license-form {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .license-form .form-control {
        background-color: #4a5568;
        border-color: #4a5568;
        color: #e2e8f0;
    }
}
