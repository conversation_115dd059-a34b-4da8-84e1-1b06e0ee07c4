<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Language strings for tool_licensetrackeradmin plugin.
 *
 * @package    tool_licensetrackeradmin
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['pluginname'] = 'License Tracker Admin Tools';
$string['licensetrackeradmin'] = 'License Tracker Admin Tools';

// Capabilities
$string['licensetrackeradmin:manage'] = 'Manage license tracker admin tools';
$string['licensetrackeradmin:bulkimport'] = 'Bulk import license data';
$string['licensetrackeradmin:bulkexport'] = 'Bulk export license data';
$string['licensetrackeradmin:viewlogs'] = 'View license tracker logs';

// Navigation
$string['bulkimportkeys'] = 'Bulk Import Keys';
$string['bulkimportpartners'] = 'Bulk Import Partners';
$string['bulkexportkeys'] = 'Bulk Export Keys';
$string['bulkexportpartners'] = 'Bulk Export Partners';
$string['activitylogs'] = 'Activity Logs';
$string['settings'] = 'Settings';

// Settings
$string['keyformat'] = 'Key Format';
$string['keyformat_desc'] = 'JSON configuration for license key format';
$string['keyformat_default'] = '{"segments": 4, "segment_length": 4, "separator": "-", "charset": "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"}';
$string['segments'] = 'Number of segments';
$string['segmentlength'] = 'Segment length';
$string['separator'] = 'Separator character';
$string['charset'] = 'Character set';

// Import/Export
$string['csvfile'] = 'CSV File';
$string['csvformat'] = 'CSV Format';
$string['uploadfile'] = 'Upload File';
$string['downloadfile'] = 'Download File';
$string['importkeys'] = 'Import Keys';
$string['importpartners'] = 'Import Partners';
$string['exportkeys'] = 'Export Keys';
$string['exportpartners'] = 'Export Partners';

// CSV Headers
$string['csvheader_keys'] = 'Expected CSV columns: keystring, coursetypename, partnername, validfrom (optional), expireson (optional)';
$string['csvheader_partners'] = 'Expected CSV columns: partnername, contactemail, country (optional), moodleusername, coursetypes (optional)';

// Messages
$string['importstarted'] = 'Import task has been queued for processing';
$string['exportcomplete'] = 'Export completed successfully';
$string['invalidcsvformat'] = 'Invalid CSV format';
$string['importcomplete'] = 'Import completed: {$a->success} successful, {$a->errors} errors';

// Logs
$string['eventtype'] = 'Event Type';
$string['actor'] = 'Actor';
$string['timestamp'] = 'Timestamp';
$string['details'] = 'Details';
$string['ipaddress'] = 'IP Address';
$string['useragent'] = 'User Agent';

// Event types
$string['event_keycreated'] = 'Key Created';
$string['event_keyused'] = 'Key Used';
$string['event_keyrevoked'] = 'Key Revoked';
$string['event_keydeleted'] = 'Key Deleted';
$string['event_partneradded'] = 'Partner Added';
$string['event_partnerupdated'] = 'Partner Updated';
$string['event_partnerdeleted'] = 'Partner Deleted';
$string['event_coursetypeadded'] = 'Course Type Added';
$string['event_coursetypeupdated'] = 'Course Type Updated';
$string['event_coursetypedeleted'] = 'Course Type Deleted';
$string['event_bulkimport'] = 'Bulk Import';
$string['event_bulkexport'] = 'Bulk Export';

// Filters
$string['filtereventtype'] = 'Filter by event type';
$string['filteractor'] = 'Filter by actor';
$string['filterdaterange'] = 'Filter by date range';
$string['datefrom'] = 'Date from';
$string['dateto'] = 'Date to';

// API
$string['apiendpoints'] = 'API Endpoints';
$string['generatekeys_api'] = 'Generate Keys';
$string['checkkeystatus_api'] = 'Check Key Status';
$string['usekey_api'] = 'Use Key';
$string['apikey'] = 'API Key';
$string['apikey_desc'] = 'API key for external access';

// Errors
$string['error:nopermission'] = 'You do not have permission to access this feature';
$string['error:invalidfile'] = 'Invalid file uploaded';
$string['error:csvprocessing'] = 'Error processing CSV file';
$string['error:taskfailed'] = 'Task execution failed';
