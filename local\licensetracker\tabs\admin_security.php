<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Admin security tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

// CRITICAL: Verify admin permissions
$context = context_system::instance();
require_capability('local/licensetracker:manageallkeys', $context);

$security_manager = new \local_licensetracker\security\license_security_manager();

$output = '';

// Security overview cards
$output .= '<div class="row mb-4">';

// Recent security violations
$recent_violations = $DB->count_records_sql(
    "SELECT COUNT(*) FROM {local_lt_security_log} 
     WHERE action LIKE '%violation%' AND timecreated > ?",
    array(time() - 86400) // Last 24 hours
);

$output .= '<div class="col-md-3">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('securityviolations24h', 'local_licensetracker') . '</h5>';
$violationclass = $recent_violations > 0 ? 'text-danger' : 'text-success';
$output .= '<h2 class="' . $violationclass . '">' . $recent_violations . '</h2>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

// Rate limit hits
$rate_limit_hits = $DB->count_records_sql(
    "SELECT COUNT(*) FROM {local_lt_security_log} 
     WHERE action LIKE '%rate_limit%' AND timecreated > ?",
    array(time() - 86400)
);

$output .= '<div class="col-md-3">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('ratelimithits24h', 'local_licensetracker') . '</h5>';
$rateclass = $rate_limit_hits > 10 ? 'text-warning' : 'text-success';
$output .= '<h2 class="' . $rateclass . '">' . $rate_limit_hits . '</h2>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

// Suspicious patterns detected
$suspicious_patterns = count($security_manager::detect_suspicious_patterns());

$output .= '<div class="col-md-3">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('suspiciouspatterns', 'local_licensetracker') . '</h5>';
$suspiciousclass = $suspicious_patterns > 0 ? 'text-warning' : 'text-success';
$output .= '<h2 class="' . $suspiciousclass . '">' . $suspicious_patterns . '</h2>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

// Active security monitoring
$monitoring_active = get_config('local_licensetracker', 'security_monitoring_enabled') ? 1 : 0;

$output .= '<div class="col-md-3">';
$output .= '<div class="card text-center">';
$output .= '<div class="card-body">';
$output .= '<h5 class="card-title">' . get_string('securitymonitoring', 'local_licensetracker') . '</h5>';
$monitoringclass = $monitoring_active ? 'text-success' : 'text-danger';
$monitoringtext = $monitoring_active ? get_string('active', 'local_licensetracker') : get_string('inactive', 'local_licensetracker');
$output .= '<h3 class="' . $monitoringclass . '">' . $monitoringtext . '</h3>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

$output .= '</div>';

// Suspicious patterns details
if ($suspicious_patterns > 0) {
    $patterns = $security_manager::detect_suspicious_patterns();
    
    $output .= '<div class="alert alert-warning mb-4">';
    $output .= '<h5><i class="fa fa-exclamation-triangle"></i> ' . get_string('suspiciousactivity', 'local_licensetracker') . '</h5>';
    $output .= '<ul class="mb-0">';
    
    foreach ($patterns as $pattern) {
        $severityclass = $pattern['severity'] === 'high' ? 'text-danger' : 'text-warning';
        $output .= '<li class="' . $severityclass . '">' . $pattern['description'] . '</li>';
    }
    
    $output .= '</ul>';
    $output .= '</div>';
}

// Recent security log entries
$output .= '<div class="card mb-4">';
$output .= '<div class="card-header">';
$output .= '<h5>' . get_string('recentsecuritylog', 'local_licensetracker') . '</h5>';
$output .= '</div>';
$output .= '<div class="card-body">';

$recent_logs = $DB->get_records_sql(
    "SELECT sl.*, u.firstname, u.lastname, u.email
     FROM {local_lt_security_log} sl
     LEFT JOIN {user} u ON sl.userid = u.id
     ORDER BY sl.timecreated DESC
     LIMIT 50",
    array()
);

if (!empty($recent_logs)) {
    $output .= '<div class="table-responsive">';
    $output .= '<table class="table table-sm table-striped">';
    $output .= '<thead>';
    $output .= '<tr>';
    $output .= '<th>' . get_string('timestamp', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('user', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('action', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('ipaddress', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('details', 'local_licensetracker') . '</th>';
    $output .= '</tr>';
    $output .= '</thead>';
    $output .= '<tbody>';
    
    foreach ($recent_logs as $log) {
        $actionclass = '';
        if (strpos($log->action, 'violation') !== false) {
            $actionclass = 'text-danger';
        } else if (strpos($log->action, 'rate_limit') !== false) {
            $actionclass = 'text-warning';
        }
        
        $output .= '<tr>';
        $output .= '<td>' . userdate($log->timecreated, get_string('strftimedatetimeshort', 'core_langconfig')) . '</td>';
        $output .= '<td>';
        if ($log->firstname) {
            $output .= $log->firstname . ' ' . $log->lastname . '<br><small class="text-muted">' . $log->email . '</small>';
        } else {
            $output .= get_string('unknown', 'local_licensetracker');
        }
        $output .= '</td>';
        $output .= '<td><span class="' . $actionclass . '">' . $log->action . '</span></td>';
        $output .= '<td>' . $log->ipaddress . '</td>';
        $output .= '<td>';
        if ($log->details) {
            $details = json_decode($log->details, true);
            if ($details) {
                $output .= '<small>';
                foreach ($details as $key => $value) {
                    $output .= '<strong>' . $key . ':</strong> ' . htmlspecialchars($value) . '<br>';
                }
                $output .= '</small>';
            }
        }
        $output .= '</td>';
        $output .= '</tr>';
    }
    
    $output .= '</tbody>';
    $output .= '</table>';
    $output .= '</div>';
} else {
    $output .= '<p class="text-muted">' . get_string('nosecuritylogs', 'local_licensetracker') . '</p>';
}

$output .= '</div>';
$output .= '</div>';

// Security configuration
$output .= '<div class="card">';
$output .= '<div class="card-header">';
$output .= '<h5>' . get_string('securityconfiguration', 'local_licensetracker') . '</h5>';
$output .= '</div>';
$output .= '<div class="card-body">';

$output .= '<div class="row">';
$output .= '<div class="col-md-6">';
$output .= '<h6>' . get_string('ratelimitsettings', 'local_licensetracker') . '</h6>';
$output .= '<ul class="list-unstyled">';
$output .= '<li><strong>' . get_string('maxattemptsperip', 'local_licensetracker') . ':</strong> ' . $security_manager::MAX_ATTEMPTS_PER_IP_HOUR . ' per hour</li>';
$output .= '<li><strong>' . get_string('maxattemptsperuser', 'local_licensetracker') . ':</strong> ' . $security_manager::MAX_ATTEMPTS_PER_USER_HOUR . ' per hour</li>';
$output .= '<li><strong>' . get_string('ratelimitwindow', 'local_licensetracker') . ':</strong> ' . ($security_manager::RATE_LIMIT_WINDOW / 60) . ' minutes</li>';
$output .= '</ul>';
$output .= '</div>';

$output .= '<div class="col-md-6">';
$output .= '<h6>' . get_string('securityfeatures', 'local_licensetracker') . '</h6>';
$output .= '<ul class="list-unstyled">';
$output .= '<li><i class="fa fa-check text-success"></i> ' . get_string('keyformatvalidation', 'local_licensetracker') . '</li>';
$output .= '<li><i class="fa fa-check text-success"></i> ' . get_string('partneraccesscontrol', 'local_licensetracker') . '</li>';
$output .= '<li><i class="fa fa-check text-success"></i> ' . get_string('ratelimiting', 'local_licensetracker') . '</li>';
$output .= '<li><i class="fa fa-check text-success"></i> ' . get_string('auditlogging', 'local_licensetracker') . '</li>';
$output .= '<li><i class="fa fa-check text-success"></i> ' . get_string('suspiciouspatterndetection', 'local_licensetracker') . '</li>';
$output .= '</ul>';
$output .= '</div>';
$output .= '</div>';

$output .= '<div class="mt-3">';
$output .= '<a href="' . $CFG->wwwroot . '/local/licensetracker/security_audit.php" class="btn btn-primary">';
$output .= get_string('runsecurityaudit', 'local_licensetracker') . '</a>';
$output .= '</div>';

$output .= '</div>';
$output .= '</div>';

return $output;
