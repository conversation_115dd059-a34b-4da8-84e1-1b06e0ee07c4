<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Partner details page for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once(__DIR__ . '/../../config.php');
require_once($CFG->libdir . '/adminlib.php');

$partnerid = required_param('id', PARAM_INT);

// Security checks
require_login();
$context = context_system::instance();
require_capability('local/licensetracker:manageallkeys', $context);

// Get partner information
$partner = $DB->get_record('local_lt_partners', array('id' => $partnerid));
if (!$partner) {
    throw new moodle_exception('partnernotfound', 'local_licensetracker');
}

// Set up page
$PAGE->set_url('/local/licensetracker/partner_details.php', array('id' => $partnerid));
$PAGE->set_context($context);
$PAGE->set_title(get_string('partnerdetails', 'local_licensetracker', $partner->name));
$PAGE->set_heading(get_string('partnerdetails', 'local_licensetracker', $partner->name));
$PAGE->navbar->add(get_string('licensetracker', 'local_licensetracker'), new moodle_url('/local/licensetracker/'));
$PAGE->navbar->add($partner->name);

echo $OUTPUT->header();

// Partner Information Card
echo '<div class="card mb-4">';
echo '<div class="card-header">';
echo '<h4>' . get_string('partnerinformation', 'local_licensetracker') . '</h4>';
echo '</div>';
echo '<div class="card-body">';
echo '<div class="row">';
echo '<div class="col-md-6">';
echo '<p><strong>' . get_string('name', 'local_licensetracker') . ':</strong> ' . $partner->name . '</p>';
echo '<p><strong>' . get_string('contactemail', 'local_licensetracker') . ':</strong> ' . $partner->contactemail . '</p>';
echo '</div>';
echo '<div class="col-md-6">';
echo '<p><strong>' . get_string('timecreated', 'local_licensetracker') . ':</strong> ' . userdate($partner->timecreated) . '</p>';
echo '<p><strong>' . get_string('timemodified', 'local_licensetracker') . ':</strong> ' . userdate($partner->timemodified) . '</p>';
echo '</div>';
echo '</div>';
echo '</div>';
echo '</div>';

// License Key Statistics
$sql = "SELECT 
            COUNT(*) as total_keys,
            COUNT(CASE WHEN status = 'available' THEN 1 END) as available_keys,
            COUNT(CASE WHEN status = 'used' THEN 1 END) as used_keys,
            COUNT(CASE WHEN status = 'revoked' THEN 1 END) as revoked_keys
        FROM {local_lt_keys} 
        WHERE partnerid = ?";
$keystats = $DB->get_record_sql($sql, array($partnerid));

echo '<div class="row mb-4">';
echo '<div class="col-md-3">';
echo '<div class="card bg-secondary text-white">';
echo '<div class="card-body text-center">';
echo '<h3>' . $keystats->total_keys . '</h3>';
echo '<p>Total Keys</p>';
echo '</div>';
echo '</div>';
echo '</div>';

echo '<div class="col-md-3">';
echo '<div class="card bg-success text-white">';
echo '<div class="card-body text-center">';
echo '<h3>' . $keystats->available_keys . '</h3>';
echo '<p>Available Keys</p>';
echo '</div>';
echo '</div>';
echo '</div>';

echo '<div class="col-md-3">';
echo '<div class="card bg-warning text-white">';
echo '<div class="card-body text-center">';
echo '<h3>' . $keystats->used_keys . '</h3>';
echo '<p>Used Keys</p>';
echo '</div>';
echo '</div>';
echo '</div>';

echo '<div class="col-md-3">';
echo '<div class="card bg-danger text-white">';
echo '<div class="card-body text-center">';
echo '<h3>' . $keystats->revoked_keys . '</h3>';
echo '<p>Revoked Keys</p>';
echo '</div>';
echo '</div>';
echo '</div>';
echo '</div>';

// Course Type Breakdown
echo '<div class="card mb-4">';
echo '<div class="card-header">';
echo '<h4>' . get_string('coursetypebreakdown', 'local_licensetracker') . '</h4>';
echo '</div>';
echo '<div class="card-body">';

$sql = "SELECT ct.name, ct.licenseprefix,
               COUNT(*) as total_keys,
               COUNT(CASE WHEN k.status = 'available' THEN 1 END) as available_keys,
               COUNT(CASE WHEN k.status = 'used' THEN 1 END) as used_keys,
               COUNT(CASE WHEN k.status = 'revoked' THEN 1 END) as revoked_keys
        FROM {local_lt_keys} k
        JOIN {local_lt_coursetypes} ct ON ct.id = k.coursetypeid
        WHERE k.partnerid = ?
        GROUP BY ct.id, ct.name, ct.licenseprefix
        ORDER BY ct.name";

$coursetypes = $DB->get_records_sql($sql, array($partnerid));

if ($coursetypes) {
    echo '<div class="table-responsive">';
    echo '<table class="table table-striped">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>Course Type</th>';
    echo '<th>Prefix</th>';
    echo '<th>Total</th>';
    echo '<th>Available</th>';
    echo '<th>Used</th>';
    echo '<th>Revoked</th>';
    echo '<th>Usage Rate</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($coursetypes as $coursetype) {
        $usagerate = $coursetype->total_keys > 0 ? 
            round(($coursetype->used_keys / $coursetype->total_keys) * 100, 1) : 0;
        
        echo '<tr>';
        echo '<td>' . $coursetype->name . '</td>';
        echo '<td><span class="badge badge-primary">' . $coursetype->licenseprefix . '</span></td>';
        echo '<td><span class="badge badge-secondary">' . $coursetype->total_keys . '</span></td>';
        echo '<td><span class="badge badge-success">' . $coursetype->available_keys . '</span></td>';
        echo '<td><span class="badge badge-warning">' . $coursetype->used_keys . '</span></td>';
        echo '<td><span class="badge badge-danger">' . $coursetype->revoked_keys . '</span></td>';
        echo '<td>';
        echo '<div class="progress" style="width: 100px;">';
        echo '<div class="progress-bar" role="progressbar" style="width: ' . $usagerate . '%">';
        echo $usagerate . '%';
        echo '</div>';
        echo '</div>';
        echo '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
} else {
    echo '<p class="text-muted">No license keys found for this partner.</p>';
}

echo '</div>';
echo '</div>';

// Recent Activity
echo '<div class="card">';
echo '<div class="card-header">';
echo '<h4>' . get_string('recentactivity', 'local_licensetracker') . '</h4>';
echo '</div>';
echo '<div class="card-body">';

$sql = "SELECT k.keystring, k.status, k.timecreated, k.timeused, 
               ct.name as coursetype, ct.licenseprefix,
               u.firstname, u.lastname, u.email
        FROM {local_lt_keys} k
        JOIN {local_lt_coursetypes} ct ON ct.id = k.coursetypeid
        LEFT JOIN {user} u ON u.id = k.usedby
        WHERE k.partnerid = ?
        ORDER BY COALESCE(k.timeused, k.timecreated) DESC
        LIMIT 20";

$recentkeys = $DB->get_records_sql($sql, array($partnerid));

if ($recentkeys) {
    echo '<div class="table-responsive">';
    echo '<table class="table table-striped">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>License Key</th>';
    echo '<th>Course Type</th>';
    echo '<th>Status</th>';
    echo '<th>Used By</th>';
    echo '<th>Date</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($recentkeys as $key) {
        echo '<tr>';
        echo '<td><code>' . $key->keystring . '</code></td>';
        echo '<td><span class="badge badge-primary">' . $key->licenseprefix . '</span> ' . $key->coursetype . '</td>';
        
        $statusclass = '';
        switch ($key->status) {
            case 'available':
                $statusclass = 'badge-success';
                break;
            case 'used':
                $statusclass = 'badge-warning';
                break;
            case 'revoked':
                $statusclass = 'badge-danger';
                break;
        }
        echo '<td><span class="badge ' . $statusclass . '">' . ucfirst($key->status) . '</span></td>';
        
        if ($key->status === 'used' && $key->firstname) {
            echo '<td>' . $key->firstname . ' ' . $key->lastname . '<br><small>' . $key->email . '</small></td>';
        } else {
            echo '<td>-</td>';
        }
        
        $date = $key->status === 'used' && $key->timeused ? $key->timeused : $key->timecreated;
        echo '<td>' . userdate($date) . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
} else {
    echo '<p class="text-muted">No recent activity found.</p>';
}

echo '</div>';
echo '</div>';

// Back button
echo '<div class="mt-3">';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php?tab=overview" class="btn btn-secondary">';
echo get_string('back', 'core');
echo '</a>';
echo '</div>';

echo $OUTPUT->footer();
