<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Partner request more licenses tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

// CRITICAL: Verify partner permissions
$context = context_system::instance();
require_capability('local/licensetracker:usekey', $context);

// Ensure partner can only request licenses for their own organization
if (!$partner) {
    throw new moodle_exception('nopermissions', 'error', '', 'Partner access required');
}

$form = new \local_licensetracker\form\partner_request_licenses_form(null, array('partner' => $partner));

if ($form->is_cancelled()) {
    redirect(new moodle_url('/local/licensetracker/index.php', array('tab' => 'assignedkeys')));
} else if ($data = $form->get_data()) {
    
    try {
        // Get course type information
        $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $data->coursetypeid));
        if (!$coursetype) {
            redirect($PAGE->url, get_string('error:coursetypenotfound', 'local_licensetracker'), 
                    null, \core\output\notification::NOTIFY_ERROR);
        }

        // Get partner price for this course type
        $partnerprice = $DB->get_field_sql(
            "SELECT COALESCE(pct.price, ct.defaultprice) 
             FROM {local_lt_coursetypes} ct
             LEFT JOIN {local_lt_partner_coursetypes} pct ON ct.id = pct.coursetypeid AND pct.partnerid = ?
             WHERE ct.id = ?",
            array($partner->id, $data->coursetypeid)
        );

        // Calculate total cost
        $totalcost = $partnerprice * $data->quantity;

        // Store request in database (create license_requests table if needed)
        $request = new stdClass();
        $request->partnerid = $partner->id;
        $request->coursetypeid = $data->coursetypeid;
        $request->quantity = $data->quantity;
        $request->urgency = $data->urgency;
        $request->notes = $data->notes;
        $request->unitprice = $partnerprice;
        $request->totalcost = $totalcost;
        $request->status = 'pending';
        $request->timecreated = time();
        $request->timemodified = time();

        // For now, we'll send an email directly since we don't have the requests table yet
        // In a full implementation, you'd store this in a database table first
        
        // Send email notification to administrators
        local_licensetracker_send_license_request_email($partner, $coursetype, $data);
        
        // Show success message
        $successmsg = get_string('requestsubmitted', 'local_licensetracker', array(
            'quantity' => $data->quantity,
            'coursetype' => $coursetype->name,
            'totalcost' => number_format($totalcost, 2)
        ));
        
        redirect(new moodle_url('/local/licensetracker/index.php', array('tab' => 'assignedkeys')), 
                $successmsg, null, \core\output\notification::NOTIFY_SUCCESS);
        
    } catch (Exception $e) {
        redirect($PAGE->url, get_string('error:requestfailed', 'local_licensetracker') . ': ' . $e->getMessage(), 
                null, \core\output\notification::NOTIFY_ERROR);
    }
}

// Display current stock levels for context
echo '<div class="alert alert-info mb-4">';
echo '<h5>' . get_string('currentstock', 'local_licensetracker') . '</h5>';

$sql = "SELECT ct.name, ct.licenseprefix,
               COUNT(CASE WHEN k.status = 'available' THEN 1 END) as available,
               COUNT(CASE WHEN k.status = 'used' THEN 1 END) as used,
               COUNT(CASE WHEN k.status = 'revoked' THEN 1 END) as revoked,
               COUNT(*) as total
        FROM {local_lt_coursetypes} ct
        LEFT JOIN {local_lt_keys} k ON ct.id = k.coursetypeid AND k.partnerid = ?
        WHERE ct.id IN (
            SELECT DISTINCT pct.coursetypeid 
            FROM {local_lt_partner_coursetypes} pct 
            WHERE pct.partnerid = ?
        )
        GROUP BY ct.id, ct.name, ct.licenseprefix
        ORDER BY ct.name";

$stocklevels = $DB->get_records_sql($sql, array($partner->id, $partner->id));

if (!empty($stocklevels)) {
    echo '<div class="row">';
    foreach ($stocklevels as $stock) {
        $availableclass = $stock->available < 10 ? 'text-danger' : ($stock->available < 50 ? 'text-warning' : 'text-success');
        echo '<div class="col-md-4 mb-2">';
        echo '<strong>' . $stock->name . '</strong><br>';
        echo '<span class="' . $availableclass . '">' . $stock->available . ' ' . get_string('available', 'local_licensetracker') . '</span> | ';
        echo $stock->used . ' ' . get_string('used', 'local_licensetracker') . ' | ';
        echo $stock->revoked . ' ' . get_string('revoked', 'local_licensetracker');
        echo '</div>';
    }
    echo '</div>';
} else {
    echo '<p>' . get_string('nostockdata', 'local_licensetracker') . '</p>';
}

echo '</div>';

// Display the form
echo '<div class="card">';
echo '<div class="card-header">';
echo '<h4>' . get_string('requestmorelicenses', 'local_licensetracker') . '</h4>';
echo '<p class="text-muted mb-0">' . get_string('requestmorelicenses_desc', 'local_licensetracker') . '</p>';
echo '</div>';
echo '<div class="card-body">';
$form->display();
echo '</div>';
echo '</div>';

// Display recent requests (if we had a requests table)
echo '<div class="card mt-4">';
echo '<div class="card-header">';
echo '<h5>' . get_string('recentrequests', 'local_licensetracker') . '</h5>';
echo '</div>';
echo '<div class="card-body">';
echo '<p class="text-muted">' . get_string('recentrequests_placeholder', 'local_licensetracker') . '</p>';
echo '</div>';
echo '</div>';
