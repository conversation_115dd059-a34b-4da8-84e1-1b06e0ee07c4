<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Admin course types tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

// CRITICAL: Verify admin permissions
$context = context_system::instance();
require_capability('local/licensetracker:managecoursetypes', $context);

$action = optional_param('action', '', PARAM_ALPHA);
$coursetypeid = optional_param('coursetypeid', 0, PARAM_INT);

// Handle actions
if ($action && confirm_sesskey()) {
    switch ($action) {
        case 'delete':
            if ($coursetypeid) {
                // Check if course type is in use
                $keycount = $DB->count_records('local_lt_keys', array('coursetypeid' => $coursetypeid));
                if ($keycount > 0) {
                    redirect($PAGE->url, get_string('coursetypeinuse', 'local_licensetracker'),
                            null, \core\output\notification::NOTIFY_ERROR);
                } else {
                    $DB->delete_records('local_lt_partner_courses', array('coursetypeid' => $coursetypeid));
                    $DB->delete_records('local_lt_coursetypes', array('id' => $coursetypeid));
                    redirect($PAGE->url, get_string('coursetypedeleted', 'local_licensetracker'), 
                            null, \core\output\notification::NOTIFY_SUCCESS);
                }
            }
            break;
    }
}

// Handle form submission for add/edit
if ($coursetypeid) {
    $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $coursetypeid));
    $form = new \local_licensetracker\form\coursetype_form(null, null, 'post', '', null, true, $coursetype);
} else {
    $form = new \local_licensetracker\form\coursetype_form();
}

if ($form->is_cancelled()) {
    redirect(new moodle_url('/local/licensetracker/index.php', array('tab' => 'coursetypes')));
} else if ($data = $form->get_data()) {
    
    $coursetypedata = new stdClass();
    $coursetypedata->name = $data->name;
    $coursetypedata->description = $data->description;
    $coursetypedata->defaultprice = $data->defaultprice;
    $coursetypedata->licenseprefix = strtoupper($data->licenseprefix);
    $coursetypedata->moodlecourseid = $data->moodlecourseid;
    $coursetypedata->enrolmethod = $data->enrolmethod;
    $coursetypedata->defaultroleid = $data->defaultroleid;
    $coursetypedata->timemodified = time();
    
    if (!empty($data->id)) {
        // Update existing course type
        $coursetypedata->id = $data->id;
        $DB->update_record('local_lt_coursetypes', $coursetypedata);
        $message = get_string('coursetypeupdated', 'local_licensetracker');
    } else {
        // Create new course type
        $coursetypedata->timecreated = time();
        $DB->insert_record('local_lt_coursetypes', $coursetypedata);
        $message = get_string('coursetypeadded', 'local_licensetracker');
    }
    
    redirect(new moodle_url('/local/licensetracker/index.php', array('tab' => 'coursetypes')), 
            $message, null, \core\output\notification::NOTIFY_SUCCESS);
}

$output = '';

// Add/Edit form
if ($action === 'add' || $action === 'edit') {
    $output .= '<div class="card mb-4">';
    $output .= '<div class="card-header">';
    $output .= '<h4>' . ($action === 'edit' ? get_string('editcoursetype', 'local_licensetracker') : get_string('addcoursetype', 'local_licensetracker')) . '</h4>';
    $output .= '</div>';
    $output .= '<div class="card-body">';
    $output .= $form->render();
    $output .= '</div>';
    $output .= '</div>';
} else {
    // Add button
    $output .= '<div class="mb-3">';
    $output .= '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php?tab=coursetypes&action=add" class="btn btn-primary">';
    $output .= get_string('addcoursetype', 'local_licensetracker') . '</a>';
    $output .= '</div>';
}

// Course types table
$coursetypes = local_licensetracker_get_coursetypes();

$output .= '<div class="card">';
$output .= '<div class="card-header">';
$output .= '<h4>' . get_string('coursetypes', 'local_licensetracker') . '</h4>';
$output .= '</div>';
$output .= '<div class="card-body">';

if (!empty($coursetypes)) {
    $output .= '<div class="table-responsive">';
    $output .= '<table class="table table-striped table-hover">';
    $output .= '<thead class="thead-dark">';
    $output .= '<tr>';
    $output .= '<th>' . get_string('name', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('licenseprefix', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('description', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('defaultprice', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('moodlecourse', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('enrolmethod', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('defaultrole', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('actions', 'local_licensetracker') . '</th>';
    $output .= '</tr>';
    $output .= '</thead>';
    $output .= '<tbody>';
    
    foreach ($coursetypes as $coursetype) {
        $output .= '<tr>';
        $output .= '<td>' . $coursetype->name . '</td>';
        $output .= '<td><span class="badge badge-primary">' . $coursetype->licenseprefix . '</span></td>';
        $output .= '<td>' . format_text($coursetype->description, FORMAT_PLAIN) . '</td>';
        $output .= '<td>$' . number_format($coursetype->defaultprice, 2) . '</td>';
        $output .= '<td>' . ($coursetype->coursename ? $coursetype->coursename : 'N/A') . '</td>';
        $output .= '<td>' . ($coursetype->enrolmethod ? $coursetype->enrolmethod : 'N/A') . '</td>';
        $output .= '<td>' . ($coursetype->rolename ? $coursetype->rolename : 'N/A') . '</td>';
        $output .= '<td>';
        $output .= '<div class="btn-group btn-group-sm">';
        $output .= '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php?tab=coursetypes&action=edit&coursetypeid=' . $coursetype->id . '" class="btn btn-primary btn-sm">' . get_string('edit', 'local_licensetracker') . '</a>';
        $output .= '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php?tab=coursetypes&action=delete&coursetypeid=' . $coursetype->id . '&sesskey=' . sesskey() . '" class="btn btn-danger btn-sm" onclick="return confirm(\'' . get_string('confirmdelete', 'core') . '\')">' . get_string('delete', 'local_licensetracker') . '</a>';
        $output .= '</div>';
        $output .= '</td>';
        $output .= '</tr>';
    }
    
    $output .= '</tbody>';
    $output .= '</table>';
    $output .= '</div>';
} else {
    $output .= '<div class="alert alert-info">';
    $output .= get_string('nocoursetypesavailable', 'local_licensetracker');
    $output .= '</div>';
}

$output .= '</div>';
$output .= '</div>';

return $output;
