<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Final system validation and readiness check.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once(__DIR__ . '/../../config.php');
require_once($CFG->dirroot . '/local/licensetracker/lib.php');

require_login();

$context = context_system::instance();
$PAGE->set_context($context);
$PAGE->set_url('/local/licensetracker/final_validation.php');
$PAGE->set_title('License Tracker - Final Validation');
$PAGE->set_heading('License Tracker - Final Validation');

// Require admin permissions
require_capability('moodle/site:config', $context);

$PAGE->set_pagelayout('admin');

echo $OUTPUT->header();

echo '<div class="card">';
echo '<div class="card-header bg-success text-white">';
echo '<h3><i class="fa fa-check-circle"></i> Final System Validation</h3>';
echo '</div>';
echo '<div class="card-body">';

// Validation results
$validation_results = array();
$critical_issues = array();
$warnings = array();

// 1. Core System Validation
echo '<h4>🔧 Core System Validation</h4>';

// Check plugin installation
$plugins_installed = array(
    'local_licensetracker' => $DB->record_exists('config_plugins', array('plugin' => 'local_licensetracker')),
    'auth_licensetracker' => $DB->record_exists('config_plugins', array('plugin' => 'auth_licensetracker')),
    'tool_licensetrackeradmin' => $DB->record_exists('config_plugins', array('plugin' => 'tool_licensetrackeradmin'))
);

$all_plugins_installed = array_sum($plugins_installed) == 3;
if (!$all_plugins_installed) {
    $critical_issues[] = 'Not all plugins are properly installed';
}

echo '<div class="alert alert-' . ($all_plugins_installed ? 'success' : 'danger') . '">';
echo '<strong>Plugin Installation:</strong> ' . ($all_plugins_installed ? '✅ All plugins installed' : '❌ Missing plugins');
echo '</div>';

// Check database tables
$required_tables = array('local_lt_keys', 'local_lt_partners', 'local_lt_coursetypes', 'local_lt_partner_coursetypes');
$missing_tables = array();

foreach ($required_tables as $table) {
    if (!$DB->get_manager()->table_exists($table)) {
        $missing_tables[] = $table;
    }
}

$tables_ok = empty($missing_tables);
if (!$tables_ok) {
    $critical_issues[] = 'Missing database tables: ' . implode(', ', $missing_tables);
}

echo '<div class="alert alert-' . ($tables_ok ? 'success' : 'danger') . '">';
echo '<strong>Database Tables:</strong> ' . ($tables_ok ? '✅ All tables present' : '❌ Missing tables');
echo '</div>';

// 2. Configuration Validation
echo '<h4>⚙️ Configuration Validation</h4>';

$config = get_config('local_licensetracker');
$authconfig = get_config('auth_licensetracker');

$system_enabled = !empty($config->enabled);
$auth_enabled = !empty($authconfig->enabled);
$properly_configured = $system_enabled && $auth_enabled;

if (!$properly_configured) {
    $warnings[] = 'System not fully enabled - license enforcement may not work';
}

echo '<div class="alert alert-' . ($properly_configured ? 'success' : 'warning') . '">';
echo '<strong>System Configuration:</strong> ' . ($properly_configured ? '✅ Properly configured' : '⚠️ Needs configuration');
echo '</div>';

// Check signup override
$signup_file = $CFG->dirroot . '/login/signup.php';
$signup_overridden = false;
if (file_exists($signup_file)) {
    $content = file_get_contents($signup_file);
    $signup_overridden = strpos($content, 'licensetracker') !== false;
}

if (!$signup_overridden && $properly_configured) {
    $critical_issues[] = 'Default signup not overridden - students can bypass license validation';
}

echo '<div class="alert alert-' . ($signup_overridden ? 'success' : 'danger') . '">';
echo '<strong>Signup Override:</strong> ' . ($signup_overridden ? '✅ Implemented' : '❌ NOT IMPLEMENTED - CRITICAL');
echo '</div>';

// 3. Security Validation
echo '<h4>🔒 Security Validation</h4>';

// Check capability definitions
$required_caps = array(
    'local/licensetracker:manageallkeys',
    'local/licensetracker:managecoursetypes',
    'local/licensetracker:managepartners',
    'local/licensetracker:viewpartnerkeys',
    'local/licensetracker:viewpartnerstats',
    'local/licensetracker:usekey'
);

$missing_caps = array();
foreach ($required_caps as $cap) {
    if (!$DB->record_exists('capabilities', array('name' => $cap))) {
        $missing_caps[] = $cap;
    }
}

$caps_ok = empty($missing_caps);
if (!$caps_ok) {
    $critical_issues[] = 'Missing capability definitions';
}

echo '<div class="alert alert-' . ($caps_ok ? 'success' : 'danger') . '">';
echo '<strong>Capabilities:</strong> ' . ($caps_ok ? '✅ All capabilities defined' : '❌ Missing capabilities');
echo '</div>';

// Check admin file protection
$admin_files = array(
    'tabs/admin_keys.php',
    'tabs/admin_generatekeys.php',
    'tabs/admin_partners.php',
    'tabs/admin_coursetypes.php'
);

$unprotected_files = 0;
foreach ($admin_files as $file) {
    $filepath = $CFG->dirroot . '/local/licensetracker/' . $file;
    if (file_exists($filepath)) {
        $content = file_get_contents($filepath);
        if (strpos($content, 'require_capability') === false) {
            $unprotected_files++;
        }
    }
}

$security_ok = $unprotected_files == 0;
if (!$security_ok) {
    $critical_issues[] = 'Admin files not properly protected';
}

echo '<div class="alert alert-' . ($security_ok ? 'success' : 'danger') . '">';
echo '<strong>File Security:</strong> ' . ($security_ok ? '✅ All files protected' : '❌ Unprotected files found');
echo '</div>';

// 4. Functional Validation
echo '<h4>🔧 Functional Validation</h4>';

// Test key generation function
$key_generation_works = false;
try {
    $test_key = local_licensetracker_generate_unique_key();
    $key_generation_works = !empty($test_key) && strlen($test_key) > 10;
} catch (Exception $e) {
    $warnings[] = 'Key generation function error: ' . $e->getMessage();
}

echo '<div class="alert alert-' . ($key_generation_works ? 'success' : 'warning') . '">';
echo '<strong>Key Generation:</strong> ' . ($key_generation_works ? '✅ Working' : '⚠️ Issues detected');
echo '</div>';

// Test validation function
$validation_works = false;
try {
    $result = local_licensetracker_validate_key('TEST-NONEXISTENT-KEY');
    $validation_works = ($result === false); // Should return false for non-existent key
} catch (Exception $e) {
    $warnings[] = 'Key validation function error: ' . $e->getMessage();
}

echo '<div class="alert alert-' . ($validation_works ? 'success' : 'warning') . '">';
echo '<strong>Key Validation:</strong> ' . ($validation_works ? '✅ Working' : '⚠️ Issues detected');
echo '</div>';

// 5. Performance Check
echo '<h4>⚡ Performance Check</h4>';

$start_time = microtime(true);
$DB->count_records('local_lt_keys');
$db_time = (microtime(true) - $start_time) * 1000;

$performance_ok = $db_time < 100;
if (!$performance_ok) {
    $warnings[] = 'Database performance may be slow';
}

echo '<div class="alert alert-' . ($performance_ok ? 'success' : 'warning') . '">';
echo '<strong>Database Performance:</strong> ' . sprintf('%.2f ms', $db_time) . ($performance_ok ? ' ✅ Good' : ' ⚠️ Slow');
echo '</div>';

// 6. Final Assessment
echo '<hr>';
echo '<h4>📊 Final Assessment</h4>';

$total_critical = count($critical_issues);
$total_warnings = count($warnings);

if ($total_critical == 0 && $total_warnings == 0) {
    echo '<div class="alert alert-success">';
    echo '<h5>🎉 SYSTEM READY FOR PRODUCTION!</h5>';
    echo '<p><strong>All validations passed.</strong> Your License Tracker system is fully operational and secure.</p>';
    echo '<ul>';
    echo '<li>✅ All plugins properly installed</li>';
    echo '<li>✅ Database schema complete</li>';
    echo '<li>✅ Security measures in place</li>';
    echo '<li>✅ Configuration validated</li>';
    echo '<li>✅ Performance acceptable</li>';
    echo '</ul>';
    echo '</div>';
} else if ($total_critical == 0) {
    echo '<div class="alert alert-warning">';
    echo '<h5>⚠️ SYSTEM READY WITH WARNINGS</h5>';
    echo '<p><strong>No critical issues found, but there are ' . $total_warnings . ' warnings to review.</strong></p>';
    echo '<ul>';
    foreach ($warnings as $warning) {
        echo '<li>⚠️ ' . $warning . '</li>';
    }
    echo '</ul>';
    echo '</div>';
} else {
    echo '<div class="alert alert-danger">';
    echo '<h5>❌ SYSTEM NOT READY FOR PRODUCTION</h5>';
    echo '<p><strong>' . $total_critical . ' critical issues must be resolved before deployment.</strong></p>';
    echo '<ul>';
    foreach ($critical_issues as $issue) {
        echo '<li>❌ ' . $issue . '</li>';
    }
    echo '</ul>';
    if ($total_warnings > 0) {
        echo '<p><strong>Additional warnings:</strong></p>';
        echo '<ul>';
        foreach ($warnings as $warning) {
            echo '<li>⚠️ ' . $warning . '</li>';
        }
        echo '</ul>';
    }
    echo '</div>';
}

// System statistics
echo '<h4>📈 System Statistics</h4>';
echo '<div class="row text-center">';

$total_keys = $DB->count_records('local_lt_keys');
$total_partners = $DB->count_records('local_lt_partners');
$total_coursetypes = $DB->count_records('local_lt_coursetypes');
$available_keys = $DB->count_records('local_lt_keys', array('status' => 'available'));

echo '<div class="col-md-3">';
echo '<h3 class="text-primary">' . $total_keys . '</h3>';
echo '<small>Total Keys</small>';
echo '</div>';

echo '<div class="col-md-3">';
echo '<h3 class="text-success">' . $available_keys . '</h3>';
echo '<small>Available Keys</small>';
echo '</div>';

echo '<div class="col-md-3">';
echo '<h3 class="text-info">' . $total_partners . '</h3>';
echo '<small>Partners</small>';
echo '</div>';

echo '<div class="col-md-3">';
echo '<h3 class="text-secondary">' . $total_coursetypes . '</h3>';
echo '<small>Course Types</small>';
echo '</div>';

echo '</div>';

echo '</div>';
echo '</div>';

// Action buttons
echo '<div class="mt-4 text-center">';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php" class="btn btn-primary btn-lg">Go to Dashboard</a>';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/test_runner.php" class="btn btn-secondary ml-2">Run Tests</a>';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/system_status.php" class="btn btn-info ml-2">System Status</a>';
echo '</div>';

echo $OUTPUT->footer();
