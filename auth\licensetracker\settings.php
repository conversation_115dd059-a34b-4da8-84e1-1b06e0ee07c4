<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Settings for auth_licensetracker plugin.
 *
 * @package    auth_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

if ($ADMIN->fulltree) {
    
    // Enable/disable the plugin
    $settings->add(new admin_setting_configcheckbox(
        'auth_licensetracker/enabled',
        get_string('enabled', 'auth_licensetracker'),
        get_string('enabled_desc', 'auth_licensetracker'),
        0
    ));

    // Require key for all students
    $settings->add(new admin_setting_configcheckbox(
        'auth_licensetracker/requirekeyforall',
        get_string('requirekeyforall', 'auth_licensetracker'),
        get_string('requirekeyforall_desc', 'auth_licensetracker'),
        1
    ));

    // Local countries (for global vs local student logic)
    $settings->add(new admin_setting_configtextarea(
        'auth_licensetracker/localcountries',
        get_string('localcountries', 'auth_licensetracker'),
        get_string('localcountries_desc', 'auth_licensetracker'),
        '',
        PARAM_TEXT
    ));
}
