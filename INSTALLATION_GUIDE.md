# 🚀 License Tracker - Complete Installation Guide

## ⚠️ CRITICAL WARNING
**This system ENFORCES license requirements!** Once installed and configured, it will **BLOCK ALL STUDENT REGISTRATION** without valid license keys (unless configured for local students). Test thoroughly before production deployment!

## 📋 Prerequisites

### System Requirements
- **Moodle 4.0 or higher** (tested up to 4.4)
- **PHP 7.4 or higher** (PHP 8.1+ recommended)
- **MySQL 5.7+ or PostgreSQL 10+**
- **Web server** (Apache/Nginx)
- **Sufficient disk space** for license key storage

### Permissions Required
- Site administrator access
- File system write permissions
- Database modification permissions

## 📦 Step 1: Download and Extract Files

1. **Download the plugin files** to your Moodle server
2. **Extract to correct locations**:
   ```bash
   # Main plugin
   cp -r local_licensetracker /path/to/moodle/local/
   
   # Authentication plugin
   cp -r auth_licensetracker /path/to/moodle/auth/
   
   # Admin tool plugin
   cp -r tool_licensetrackeradmin /path/to/moodle/admin/tool/
   ```

3. **Set proper permissions**:
   ```bash
   chown -R www-data:www-data /path/to/moodle/local/licensetracker
   chown -R www-data:www-data /path/to/moodle/auth/licensetracker
   chown -R www-data:www-data /path/to/moodle/admin/tool/licensetrackeradmin
   chmod -R 755 /path/to/moodle/local/licensetracker
   chmod -R 755 /path/to/moodle/auth/licensetracker
   chmod -R 755 /path/to/moodle/admin/tool/licensetrackeradmin
   ```

## 🔧 Step 2: Install Plugins

1. **Access Moodle as site administrator**
2. **Navigate to**: Site Administration → Notifications
3. **Install all three plugins** in this order:
   - ✅ local_licensetracker (Local plugin)
   - ✅ auth_licensetracker (Authentication plugin)
   - ✅ tool_licensetrackeradmin (Admin tool)

4. **Verify installation**:
   - Check for any error messages
   - Ensure all database tables are created
   - Confirm capabilities are installed

## ⚙️ Step 3: Configure License Enforcement

### 🚨 CRITICAL CONFIGURATION

1. **Navigate to**: Site Administration → Plugins → Local plugins → License Tracker Settings

2. **Configure these ESSENTIAL settings**:
   - ✅ **Enabled**: `Yes` (Master switch)
   - ✅ **Enforce license for all students**: 
     - `Yes` = ALL students need licenses
     - `No` = Use country-based logic
   - ✅ **Local countries**: Countries that don't need licenses (e.g., "US,CA,GB")
   - ✅ **Key format**: JSON configuration for license keys
   - ✅ **Email notifications**: Enable system notifications

### Example Configuration:
```
Enabled: ✅ Yes
Enforce license for all students: ❌ No
Local countries: US,CA,GB,AU
Key format: {"segments": 4, "segment_length": 4, "separator": "-", "charset": "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"}
Email notifications: ✅ Yes
```

## 🔐 Step 4: Set Up User Roles and Permissions

### Create System Partner Role
1. **Navigate to**: Site Administration → Users → Permissions → Define roles
2. **Create new role**: "System Partner"
3. **Assign capabilities**:
   - ✅ `local/licensetracker:viewpartnerkeys`
   - ✅ `local/licensetracker:usekey`
   - ✅ `local/licensetracker:viewpartnerstats`

### Assign Admin Permissions
1. **Navigate to**: Site Administration → Users → Permissions → Assign system roles
2. **Assign Manager role** to license administrators
3. **Verify capabilities** are properly assigned

## 🚨 Step 5: Override Default Registration (MANDATORY)

**You MUST choose ONE of these options:**

### Option A: Redirect Default Signup (Recommended)
Edit `/login/signup.php` and add after the `require_once` statements:

```php
// CRITICAL: Redirect to license tracker signup if enabled
if (get_config('local_licensetracker', 'enabled')) {
    redirect(new moodle_url('/local/licensetracker/signup_override.php'));
}
```

### Option B: Disable Self-Registration
1. **Navigate to**: Site Administration → Plugins → Authentication → Manage authentication
2. **Disable**: "Email-based self-registration"
3. **Use only**: Partner-initiated registration through dashboards

## 📚 Step 6: Initial System Setup

### Create Course Types
1. **Navigate to**: Site Administration → Local plugins → License Tracker
2. **Go to**: Course Types tab
3. **Create course types** linking to your Moodle courses:
   - Name: "Basic Course"
   - Moodle Course: Select existing course
   - Default Role: Student
   - Enrollment Method: Manual

### Add Partners
1. **Go to**: Partners tab
2. **Add new partner**:
   - Partner Name: "Test Partner"
   - Contact Email: <EMAIL>
   - Moodle User: Link to existing user account
   - Country: Select country
   - Language: Select language

### Generate License Keys
1. **Go to**: Generate Keys tab
2. **Create initial keys**:
   - Partner: Select partner
   - Course Type: Select course type
   - Quantity: 10 (for testing)
   - Valid From: Today
   - Expires On: 1 year from now

## 🧪 Step 7: Testing and Verification

### Test Scenarios
1. **Local Student Registration** (should work without license):
   - Use country from "Local countries" setting
   - Should register successfully without license key

2. **Global Student Registration** (should require license):
   - Use country NOT in "Local countries" setting
   - Should be blocked without valid license key
   - Should work with valid license key

3. **Partner Dashboard Access**:
   - Login as partner user
   - Should see partner dashboard with keys
   - Should be able to register students

4. **Admin Functions**:
   - Test key generation
   - Test partner management
   - Test statistics viewing

### Verification Tools
- **System Status**: `/local/licensetracker/system_status.php`
- **Admin Verification**: `/local/licensetracker/admin_verification.php`
- **Security Audit**: `/local/licensetracker/security_audit.php`
- **Database Performance**: `/local/licensetracker/db_performance.php`

## 🔍 Step 8: System Health Check

Run these verification steps:

1. **Check System Status**:
   ```
   ✅ System Enabled: Yes
   ✅ Auth Plugin Enabled: Yes
   ✅ License Enforcement: Active
   ✅ Database Tables: All present
   ✅ Capabilities: All defined
   ```

2. **Verify Security**:
   - All admin tabs have capability checks
   - SQL queries use parameterized statements
   - Input validation is implemented
   - Session security is active

3. **Test Performance**:
   - Database queries execute quickly
   - Indexes are properly configured
   - No performance bottlenecks

## 🚨 Troubleshooting

### Common Issues

**Issue**: "Incorrect integer value for risk bitmask"
**Solution**: Risk constants fixed - reinstall plugins

**Issue**: Students can still register without licenses
**Solution**: Ensure signup override is implemented (Step 5)

**Issue**: Partners can't access dashboard
**Solution**: Verify partner role has correct capabilities

**Issue**: License keys not working
**Solution**: Check key format configuration and validation logic

### Debug Mode
Enable debugging to see detailed error messages:
```php
// In config.php
$CFG->debug = E_ALL;
$CFG->debugdisplay = 1;
```

## 📞 Support and Maintenance

### Regular Maintenance
- **Weekly**: Check expired keys
- **Monthly**: Review partner statistics
- **Quarterly**: Run security audit
- **Annually**: Database optimization

### Monitoring
- Monitor system status dashboard
- Check database performance regularly
- Review security audit reports
- Monitor license key usage patterns

## ✅ Installation Complete!

Your License Tracker system is now fully operational and will enforce license requirements according to your configuration. 

**Next Steps**:
1. Train your partners on using the system
2. Monitor initial usage patterns
3. Adjust settings based on feedback
4. Set up regular maintenance schedules

**Remember**: This system will BLOCK student registration without valid licenses once enabled. Always test thoroughly!

---

## 🛠️ Quick Setup Script

For advanced users, here's a PHP script to automate initial configuration:

```php
<?php
// Quick setup script - run from Moodle root directory
require_once('config.php');
require_login();
require_capability('moodle/site:config', context_system::instance());

// Set basic configuration
set_config('enabled', 1, 'local_licensetracker');
set_config('enforce_all', 0, 'local_licensetracker');
set_config('local_countries', 'US,CA,GB,AU', 'local_licensetracker');
set_config('email_notifications', 1, 'local_licensetracker');

// Enable auth plugin
set_config('enabled', 1, 'auth_licensetracker');
set_config('requirekeyforall', 0, 'auth_licensetracker');
set_config('localcountries', 'US,CA,GB,AU', 'auth_licensetracker');

echo "✅ License Tracker configured successfully!\n";
echo "⚠️  Remember to override signup.php manually!\n";
?>
```

Save as `setup_licensetracker.php` in your Moodle root and run once.
