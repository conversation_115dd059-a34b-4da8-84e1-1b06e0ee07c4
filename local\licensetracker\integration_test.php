<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Integration test for complete License Tracker workflow.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once('../../config.php');
require_once($CFG->libdir . '/adminlib.php');

admin_externalpage_setup('local_licensetracker_integration_test');

$PAGE->set_title('License Tracker Integration Test');
$PAGE->set_heading('License Tracker Integration Test');

echo $OUTPUT->header();

echo '<div class="container-fluid">';
echo '<h2>License Tracker Integration Test</h2>';
echo '<p class="lead">Complete end-to-end workflow testing</p>';

$test_results = array();
$cleanup_items = array();

try {
    // Step 1: Create test course type
    echo '<div class="card mb-3">';
    echo '<div class="card-header"><h5>Step 1: Create Test Course Type</h5></div>';
    echo '<div class="card-body">';
    
    $test_coursetype = new stdClass();
    $test_coursetype->name = 'Integration Test Course';
    $test_coursetype->description = 'Test course type for integration testing';
    $test_coursetype->licenseprefix = 'ITEST';
    $test_coursetype->defaultprice = 99.99;
    $test_coursetype->timecreated = time();
    $test_coursetype->timemodified = time();
    
    $coursetype_id = $DB->insert_record('local_lt_coursetypes', $test_coursetype);
    $cleanup_items[] = array('table' => 'local_lt_coursetypes', 'id' => $coursetype_id);
    
    echo '<p class="text-success">✓ Created test course type with ID: ' . $coursetype_id . '</p>';
    echo '</div></div>';
    
    // Step 2: Create test partner
    echo '<div class="card mb-3">';
    echo '<div class="card-header"><h5>Step 2: Create Test Partner</h5></div>';
    echo '<div class="card-body">';
    
    $test_partner = new stdClass();
    $test_partner->partnername = 'Integration Test Partner';
    $test_partner->contactemail = '<EMAIL>';
    $test_partner->contactphone = '************';
    $test_partner->address = '123 Test Street';
    $test_partner->city = 'Test City';
    $test_partner->country = 'US';
    $test_partner->timecreated = time();
    $test_partner->timemodified = time();
    
    $partner_id = $DB->insert_record('local_lt_partners', $test_partner);
    $cleanup_items[] = array('table' => 'local_lt_partners', 'id' => $partner_id);
    
    echo '<p class="text-success">✓ Created test partner with ID: ' . $partner_id . '</p>';
    echo '</div></div>';
    
    // Step 3: Assign course type to partner
    echo '<div class="card mb-3">';
    echo '<div class="card-header"><h5>Step 3: Assign Course Type to Partner</h5></div>';
    echo '<div class="card-body">';
    
    $assignment = new stdClass();
    $assignment->partnerid = $partner_id;
    $assignment->coursetypeid = $coursetype_id;
    $assignment->price = 89.99;
    $assignment->timecreated = time();
    
    $assignment_id = $DB->insert_record('local_lt_partner_coursetypes', $assignment);
    $cleanup_items[] = array('table' => 'local_lt_partner_coursetypes', 'id' => $assignment_id);
    
    echo '<p class="text-success">✓ Assigned course type to partner with ID: ' . $assignment_id . '</p>';
    echo '</div></div>';
    
    // Step 4: Generate test license keys
    echo '<div class="card mb-3">';
    echo '<div class="card-header"><h5>Step 4: Generate Test License Keys</h5></div>';
    echo '<div class="card-body">';
    
    $generated_keys = array();
    for ($i = 0; $i < 5; $i++) {
        $keystring = local_licensetracker_generate_unique_key($coursetype_id);
        
        $keydata = new stdClass();
        $keydata->keystring = $keystring;
        $keydata->coursetypeid = $coursetype_id;
        $keydata->partnerid = $partner_id;
        $keydata->status = 'available';
        $keydata->timecreated = time();
        $keydata->timemodified = time();
        
        $key_id = $DB->insert_record('local_lt_keys', $keydata);
        $generated_keys[] = $key_id;
        $cleanup_items[] = array('table' => 'local_lt_keys', 'id' => $key_id);
        
        echo '<p class="text-success">✓ Generated key: ' . $keystring . ' (ID: ' . $key_id . ')</p>';
    }
    
    echo '</div></div>';
    
    // Step 5: Test key validation
    echo '<div class="card mb-3">';
    echo '<div class="card-header"><h5>Step 5: Test Key Validation</h5></div>';
    echo '<div class="card-body">';
    
    $test_key = $DB->get_record('local_lt_keys', array('id' => $generated_keys[0]));
    $validation_result = local_licensetracker_validate_partner_key_usage($partner_id, $test_key->keystring);
    
    if ($validation_result['valid']) {
        echo '<p class="text-success">✓ Key validation passed: ' . $validation_result['message'] . '</p>';
    } else {
        echo '<p class="text-danger">✗ Key validation failed: ' . $validation_result['message'] . '</p>';
    }
    
    echo '</div></div>';
    
    // Step 6: Test security features
    echo '<div class="card mb-3">';
    echo '<div class="card-header"><h5>Step 6: Test Security Features</h5></div>';
    echo '<div class="card-body">';
    
    if (class_exists('local_licensetracker\security\license_security_manager')) {
        $security_manager = new \local_licensetracker\security\license_security_manager();
        
        // Test key format validation
        $format_test = $security_manager::validate_key_format($test_key->keystring);
        if ($format_test['valid']) {
            echo '<p class="text-success">✓ Key format validation passed</p>';
        } else {
            echo '<p class="text-warning">⚠ Key format validation issue: ' . $format_test['reason'] . '</p>';
        }
        
        // Test partner access check
        $access_test = $security_manager::check_partner_key_access($partner_id, $test_key->keystring, $USER->id);
        if ($access_test['allowed']) {
            echo '<p class="text-success">✓ Partner access check passed</p>';
        } else {
            echo '<p class="text-warning">⚠ Partner access check issue: ' . $access_test['reason'] . '</p>';
        }
        
    } else {
        echo '<p class="text-warning">⚠ Security manager not available</p>';
    }
    
    echo '</div></div>';
    
    // Step 7: Test key usage (simulate student registration)
    echo '<div class="card mb-3">';
    echo '<div class="card-header"><h5>Step 7: Test Key Usage</h5></div>';
    echo '<div class="card-body">';
    
    $use_result = local_licensetracker_use_key($test_key->keystring, $USER->id);
    if ($use_result['success']) {
        echo '<p class="text-success">✓ Key usage successful: ' . $use_result['message'] . '</p>';
        
        // Verify key status changed
        $used_key = $DB->get_record('local_lt_keys', array('id' => $test_key->id));
        if ($used_key->status === 'used') {
            echo '<p class="text-success">✓ Key status correctly updated to "used"</p>';
        } else {
            echo '<p class="text-warning">⚠ Key status not updated correctly</p>';
        }
    } else {
        echo '<p class="text-danger">✗ Key usage failed: ' . $use_result['message'] . '</p>';
    }
    
    echo '</div></div>';
    
    // Step 8: Test statistics
    echo '<div class="card mb-3">';
    echo '<div class="card-header"><h5>Step 8: Test Statistics</h5></div>';
    echo '<div class="card-body">';
    
    $stats = local_licensetracker_get_partner_statistics($partner_id);
    if ($stats) {
        echo '<p class="text-success">✓ Partner statistics retrieved:</p>';
        echo '<ul>';
        echo '<li>Total keys: ' . $stats->total_keys . '</li>';
        echo '<li>Available keys: ' . $stats->available_keys . '</li>';
        echo '<li>Used keys: ' . $stats->used_keys . '</li>';
        echo '<li>Revoked keys: ' . $stats->revoked_keys . '</li>';
        echo '</ul>';
    } else {
        echo '<p class="text-warning">⚠ Could not retrieve partner statistics</p>';
    }
    
    echo '</div></div>';
    
    // Success summary
    echo '<div class="alert alert-success">';
    echo '<h4>🎉 Integration Test Completed Successfully!</h4>';
    echo '<p>All core workflows are functioning correctly:</p>';
    echo '<ul>';
    echo '<li>✅ Course type management</li>';
    echo '<li>✅ Partner management</li>';
    echo '<li>✅ License key generation</li>';
    echo '<li>✅ Key validation and security</li>';
    echo '<li>✅ Key usage workflow</li>';
    echo '<li>✅ Statistics and reporting</li>';
    echo '</ul>';
    echo '</div>';
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">';
    echo '<h4>❌ Integration Test Failed</h4>';
    echo '<p>Error: ' . $e->getMessage() . '</p>';
    echo '<p>File: ' . $e->getFile() . ' (Line: ' . $e->getLine() . ')</p>';
    echo '</div>';
}

// Cleanup
echo '<div class="card mt-4">';
echo '<div class="card-header"><h5>Cleanup Test Data</h5></div>';
echo '<div class="card-body">';

foreach (array_reverse($cleanup_items) as $item) {
    try {
        $DB->delete_records($item['table'], array('id' => $item['id']));
        echo '<p class="text-info">🧹 Cleaned up ' . $item['table'] . ' record ID: ' . $item['id'] . '</p>';
    } catch (Exception $e) {
        echo '<p class="text-warning">⚠ Could not clean up ' . $item['table'] . ' record ID: ' . $item['id'] . '</p>';
    }
}

echo '<p class="text-success">✓ Cleanup completed</p>';
echo '</div></div>';

echo '</div>';

echo $OUTPUT->footer();
