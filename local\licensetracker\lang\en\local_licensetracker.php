<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Language strings for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['pluginname'] = 'License Tracker';
$string['licensetracker'] = 'License Tracker';

// Capabilities
$string['licensetracker:manageallkeys'] = 'Manage all license keys';
$string['licensetracker:managecoursetypes'] = 'Manage course types';
$string['licensetracker:managepartners'] = 'Manage partners';
$string['licensetracker:viewpartnerkeys'] = 'View partner keys';
$string['licensetracker:viewpartnerstats'] = 'View partner statistics';
$string['licensetracker:usekey'] = 'Use license keys';

// Navigation
$string['dashboard'] = 'Dashboard';
$string['overview'] = 'Overview';
$string['keys'] = 'Keys';
$string['generatekeys'] = 'Generate Keys';
$string['partners'] = 'Partners';
$string['coursetypes'] = 'Course Types';
$string['statistics'] = 'Statistics';
$string['purchase'] = 'Purchase';
$string['assignedkeys'] = 'Assigned Keys';
$string['registernewstudent'] = 'Register New Student';
$string['bulkregisterstudents'] = 'Bulk Register Students';
$string['registeruser'] = 'Register User';
$string['csvupload'] = 'CSV File Upload';
$string['csvfile'] = 'CSV File';
$string['csvfile_help'] = 'Upload a CSV file containing student information. The file must include columns for username, firstname, lastname, email, password, and licensekey.';
$string['options'] = 'Processing Options';
$string['skipfirsterror'] = 'Continue on errors';
$string['skipfirsterror_desc'] = 'Continue processing even if some registrations fail';
$string['maxprocessing'] = 'Maximum records to process';
$string['maxprocessing_help'] = 'Limit the number of records to process in one batch (1-1000)';
$string['processbulkregistration'] = 'Process Bulk Registration';
$string['noavailablekeys'] = 'You have no available license keys. Please contact your administrator to purchase more licenses.';
$string['keynotforyourorganization'] = 'This license key does not belong to your organization';
$string['nostockforcoursetype'] = 'You have no available license keys for the {$a} course type';
$string['partnernotassignedtocoursetype'] = 'Your organization is not assigned to the {$a} course type. Please contact your administrator.';

// Additional strings
$string['totalkeys'] = 'Total Keys';
$string['availablekeys'] = 'Available Keys';
$string['usedkeys'] = 'Used Keys';
$string['revokedkeys'] = 'Revoked Keys';
$string['studentregistered'] = 'Student registered successfully';
$string['enrollmentsuccess'] = 'Student enrolled in course: {$a}';
$string['coursetypebreakdown'] = 'Course Type Breakdown';
$string['recentactivity'] = 'Recent Activity';
$string['partnerinformation'] = 'Partner Information';
$string['contactemail'] = 'Contact Email';
$string['timecreated'] = 'Created';
$string['timemodified'] = 'Modified';

// Error messages for comprehensive validation
$string['registrationfailed'] = 'Registration failed. Please check your information and try again.';
$string['error:usernameexists'] = 'This username is already taken. Please choose a different username.';
$string['error:emailexists'] = 'This email address is already registered. Please use a different email.';
$string['error:invalidpassword'] = 'Invalid password. Please check the password requirements.';
$string['error:invalidusernameformat'] = 'Username can only contain letters, numbers, dots, underscores, and hyphens.';
$string['error:usernametoshort'] = 'Username must be at least 3 characters long.';
$string['error:invalidemail'] = 'Please enter a valid email address.';
$string['error:passwordtooshort'] = 'Password must be at least 8 characters long.';
$string['error:passwordweak'] = 'Password must contain at least one uppercase letter, one lowercase letter, and one number.';
$string['error:nopartnercontext'] = 'Partner context not found. Please contact your administrator.';
$string['error:keynotforcourse'] = 'This license key is not valid for the selected course type.';
$string['error:coursetypeexists'] = 'Course type name already exists';
$string['error:licenseprefixexists'] = 'License prefix already exists';
$string['error:invalidlicenseprefix'] = 'License prefix must contain only uppercase letters and numbers';
$string['error:negativeprice'] = 'Price cannot be negative';

// Missing strings for coming soon template
$string['comingsoon_desc'] = 'This feature is currently under development and will be available soon.';
$string['error:invalidmaxprocessing'] = 'Maximum processing must be between 1 and 1000';

// Partner-only registration mode strings
$string['registrationdisabled'] = 'Self-Registration Disabled';
$string['partneronlymode'] = 'This system is configured for partner-only registration. Students cannot register directly.';
$string['howtoaccesscourses'] = 'How to Access Courses:';
$string['contactpartner'] = 'Contact your authorized training partner or organization';
$string['partnerregistersyou'] = 'Your partner will register you using a valid license key';
$string['receivecredentials'] = 'You will receive login credentials via email';
$string['backtologin'] = 'Back to Login';
$string['usertype'] = 'User Type';
$string['partnerstudent'] = 'Partner Student';
$string['localstudent'] = 'Local Student';
$string['selectcoursetype'] = 'Select Course Type';
$string['coursetype'] = 'Course Type';
$string['userdetails'] = 'User Details';
$string['userregistered'] = 'User {$a} registered successfully';
$string['partnerdetails'] = 'Partner Details: {$a}';
$string['partnerinformation'] = 'Partner Information';
$string['coursetypebreakdown'] = 'Course Type Breakdown';
$string['recentactivity'] = 'Recent Activity';

// General
$string['name'] = 'Name';
$string['description'] = 'Description';
$string['status'] = 'Status';
$string['partner'] = 'Partner';
$string['coursetype'] = 'Course Type';
$string['price'] = 'Price';
$string['defaultprice'] = 'Default Price';
$string['customprice'] = 'Custom Price';
$string['actions'] = 'Actions';
$string['edit'] = 'Edit';
$string['delete'] = 'Delete';
$string['add'] = 'Add';
$string['save'] = 'Save';
$string['cancel'] = 'Cancel';
$string['confirm'] = 'Confirm';
$string['yes'] = 'Yes';
$string['no'] = 'No';

// Key management
$string['keystring'] = 'Key String';
$string['generatekey'] = 'Generate Key';
$string['numberofkeys'] = 'Number of Keys';
$string['validfrom'] = 'Valid From';
$string['expireson'] = 'Expires On';
$string['dateused'] = 'Date Used';
$string['usedby'] = 'Used By';
$string['ipaddress'] = 'IP Address';
$string['useragent'] = 'User Agent';
$string['available'] = 'Available';
$string['used'] = 'Used';
$string['revoked'] = 'Revoked';
$string['revoke'] = 'Revoke';

// Partner management
$string['partnername'] = 'Partner Name';
$string['contactemail'] = 'Contact Email';
$string['country'] = 'Country';
$string['language'] = 'Language';
$string['moodleuser'] = 'Moodle User';
$string['assigncoursetypes'] = 'Assign Course Types';

// Course type management
$string['coursetypename'] = 'Course Type Name';
$string['moodlecourse'] = 'Moodle Course';
$string['enrolmethod'] = 'Enrollment Method';
$string['defaultrole'] = 'Default Role';

// Student registration
$string['firstname'] = 'First Name';
$string['lastname'] = 'Last Name';
$string['email'] = 'Email';
$string['username'] = 'Username';
$string['password'] = 'Password';
$string['city'] = 'City';
$string['licensekey'] = 'License Key';

// Messages
$string['comingsoon'] = 'Coming Soon';
$string['nopartnerlinked'] = 'You are not linked to any partner. Please contact an administrator.';
$string['keygenerated'] = 'Key generated successfully';
$string['keysgenerated'] = '{$a} keys generated successfully';
$string['keyrevoked'] = 'Key revoked successfully';
$string['partneradded'] = 'Partner added successfully';
$string['partnerupdated'] = 'Partner updated successfully';
$string['partnerdeleted'] = 'Partner deleted successfully';
$string['coursetypeadded'] = 'Course type added successfully';
$string['coursetypeupdated'] = 'Course type updated successfully';
$string['coursetypedeleted'] = 'Course type deleted successfully';
$string['studentregistered'] = 'Student registered successfully';
$string['keysinuse'] = 'Cannot delete partner - keys are currently assigned to this partner';
$string['coursetypeinuse'] = 'Cannot delete course type - it is currently in use';
$string['taskqueued'] = 'Task queued for processing. You will be notified when complete.';

// Errors
$string['error:invalidkey'] = 'Invalid license key';
$string['error:keynotfound'] = 'License key not found';
$string['error:keyalreadyused'] = 'License key has already been used';
$string['error:keyexpired'] = 'License key has expired';
$string['error:keynotactive'] = 'License key is not yet active';
$string['error:keyrevoked'] = 'License key has been revoked';
$string['error:partnernotfound'] = 'Partner not found';
$string['error:coursetypenotfound'] = 'Course type not found';
$string['error:coursenotfound'] = 'Course not found';
$string['error:duplicatekey'] = 'Duplicate key generated, retrying...';
$string['error:cannotdeletepartner'] = 'Cannot delete partner - keys are assigned';
$string['error:cannotdeletecoursetype'] = 'Cannot delete course type - it is in use';
$string['wrongcoursetype'] = 'This license key is for "{$a->keytype}" course but you are trying to register for "{$a->requiredtype}" course';
$string['wrongcourseprefix'] = 'This license key does not start with the required prefix "{$a->prefix}" for {$a->coursename} course';
$string['invalidkey'] = 'Invalid or expired license key';
$string['licenseprefix'] = 'License Prefix';
$string['licenseprefix_help'] = 'Short prefix that will appear at the beginning of all license keys for this course type (e.g., CHCSS, AI, WEB)';
$string['partnerstudentonly'] = 'Partners can only register partner_student users';
$string['staffonlyregistration'] = 'Only staff members can register users';

// Statistics
$string['totalkeys'] = 'Total Keys';
$string['availablekeys'] = 'Available Keys';
$string['usedkeys'] = 'Used Keys';
$string['revokedkeys'] = 'Revoked Keys';
$string['totalpartners'] = 'Total Partners';
$string['totalcoursetypes'] = 'Total Course Types';
$string['partnerbreakdown'] = 'Partner Breakdown';
$string['coursetypebreakdown'] = 'Course Type Breakdown';

// Additional strings
$string['addpartner'] = 'Add Partner';
$string['editpartner'] = 'Edit Partner';
$string['addcoursetype'] = 'Add Course Type';
$string['editcoursetype'] = 'Edit Course Type';
$string['nopartnersavailable'] = 'No partners available';
$string['nocoursetypesavailable'] = 'No course types available';
$string['nokeysavailable'] = 'No keys available';
$string['noavailablekeys'] = 'No available keys for registration';
$string['registerstudent'] = 'Register Student';
$string['studentdetails'] = 'Student Details';
$string['advancedtools'] = 'Advanced Tools';
$string['enabled_desc'] = 'Enable or disable the license tracker plugin';
$string['comingsoon_desc'] = 'This feature will be available in a future update';
$string['confirmrevoke'] = 'Are you sure you want to revoke this key?';
$string['keydeleted'] = 'Key deleted successfully';

// New critical settings
$string['enforce_all'] = 'Enforce license for all students';
$string['enforce_all_desc'] = 'If enabled, ALL students must provide a valid license key during registration';
$string['local_countries'] = 'Local countries (no license required)';
$string['local_countries_desc'] = 'Comma-separated list of country codes that do not require license keys (e.g., US,CA,GB)';
$string['key_format'] = 'License key format configuration';
$string['key_format_desc'] = 'JSON configuration for license key format';
$string['email_notifications'] = 'Enable email notifications';
$string['email_notifications_desc'] = 'Send email notifications for key usage and partner activities';
$string['auto_cleanup'] = 'Auto-cleanup expired keys';
$string['auto_cleanup_desc'] = 'Automatically remove expired keys from the system';

// Error messages
$string['error:invalidkey'] = 'Invalid or unavailable license key';
$string['error:coursetypenotfound'] = 'Course type not found';
$string['error:partnernotfound'] = 'Partner not found';
$string['studentregistered'] = 'Student registered successfully';
$string['coursetypeinuse'] = 'Cannot delete course type - it has associated license keys';
$string['keysinuse'] = 'Cannot delete partner - they have associated license keys';
$string['partneradded'] = 'Partner added successfully';
$string['partnerupdated'] = 'Partner updated successfully';
$string['partnerdeleted'] = 'Partner deleted successfully';
$string['coursetypeadded'] = 'Course type added successfully';
$string['coursetypeupdated'] = 'Course type updated successfully';
$string['coursetypedeleted'] = 'Course type deleted successfully';
$string['keygenerated'] = 'License key generated successfully';
$string['keysgenerated'] = '{$a} license keys generated successfully';
$string['keyrevoked'] = 'License key revoked successfully';
$string['taskqueued'] = 'Task queued for background processing';

// Advanced admin settings
$string['max_batch_keys'] = 'Maximum keys per batch generation';
$string['max_batch_keys_desc'] = 'Maximum number of keys that can be generated in a single batch operation';
$string['expiration_warning_days'] = 'Key expiration warning (days)';
$string['expiration_warning_days_desc'] = 'Number of days before expiration to send warning notifications';
$string['enable_audit_log'] = 'Enable audit logging';
$string['enable_audit_log_desc'] = 'Log all license key activities for security and compliance';
$string['partner_self_registration'] = 'Allow partner self-registration';
$string['partner_self_registration_desc'] = 'Allow partners to register themselves (requires admin approval)';
$string['default_enrol_method'] = 'Default enrollment method';
$string['default_enrol_method_desc'] = 'Default method for enrolling students in courses when using license keys';
$string['maintenance_mode'] = 'Maintenance mode';
$string['maintenance_mode_desc'] = 'Disable license key usage during system maintenance';
$string['admin_verification'] = 'Admin Verification';

// System status messages
$string['system_status'] = 'System Status';
$string['system_operational'] = 'System is operational';
$string['system_maintenance'] = 'System is in maintenance mode';
$string['license_enforcement_active'] = 'License enforcement is ACTIVE';
$string['license_enforcement_disabled'] = 'License enforcement is DISABLED';

// Dashboard enhancements
$string['quick_stats'] = 'Quick Statistics';
$string['recent_activity'] = 'Recent Activity';
$string['system_health'] = 'System Health';
$string['performance_metrics'] = 'Performance Metrics';

// Request more licenses functionality
$string['requestmorelicenses'] = 'Request More Licenses';
$string['requestmorelicenses_desc'] = 'Submit a request to KERNELiOS for additional license keys for your organization';
$string['quantityrequested'] = 'Quantity Requested';
$string['quantityrequested_help'] = 'Number of license keys you need (1-10,000)';
$string['urgency'] = 'Urgency Level';
$string['urgency_help'] = 'How quickly you need these license keys';
$string['urgency_normal'] = 'Normal (5-7 business days)';
$string['urgency_high'] = 'High (2-3 business days)';
$string['urgency_urgent'] = 'Urgent (24-48 hours)';
$string['additionalnotes'] = 'Additional Notes';
$string['additionalnotes_help'] = 'Any additional information about your request (optional)';
$string['submitrequest'] = 'Submit Request';
$string['coursetype_request'] = 'Course Type';
$string['coursetype_request_help'] = 'Select the course type for which you need license keys';
$string['price'] = 'Price';
$string['currentstock'] = 'Current Stock Levels';
$string['nostockdata'] = 'No stock data available for your organization';
$string['recentrequests'] = 'Recent License Requests';
$string['recentrequests_placeholder'] = 'Your recent license requests will appear here once the requests system is fully implemented';
$string['nocoursetypesassigned'] = 'No course types are assigned to your organization. Please contact an administrator.';

// Request validation errors
$string['error:quantitytoosmall'] = 'Quantity must be at least 1';
$string['error:quantitytoolarge'] = 'Quantity cannot exceed 10,000';
$string['error:notestoolong'] = 'Additional notes cannot exceed 1,000 characters';
$string['error:requestfailed'] = 'Failed to submit license request';

// Request success messages
$string['requestsubmitted'] = 'License request submitted successfully! Requested {$a->quantity} keys for {$a->coursetype} (Total cost: ${$a->totalcost}). You will receive a confirmation email shortly.';

// Email templates
$string['licenserequest_subject'] = 'License Request from {$a->partnername}: {$a->quantity} keys for {$a->coursetype}';
$string['licenserequest_message'] = 'A new license request has been submitted:

Partner: {$a->partnername}
Contact Email: {$a->contactemail}
Course Type: {$a->coursetype}
Quantity Requested: {$a->quantity}
Urgency Level: {$a->urgency}
Unit Price: ${$a->unitprice}
Total Cost: ${$a->totalcost}

Additional Notes:
{$a->notes}

Please review this request in the admin dashboard:
{$a->adminurl}

This is an automated message from the KERNELiOS License Management System.';

$string['nonotes'] = 'No additional notes provided';

// Additional email notifications
$string['lowstock_subject'] = 'Low Stock Alert: {$a->partnername}';
$string['lowstock_message'] = 'Partner {$a->partnername} is running low on license keys:

Contact Email: {$a->contactemail}
Low Stock Details: {$a->lowstockdata}

Please review and generate additional keys if needed:
{$a->adminurl}

This is an automated message from the KERNELiOS License Management System.';

$string['keyexpiry_subject'] = 'License Key Expiry Alert: {$a->count} keys expiring soon';
$string['keyexpiry_message'] = '{$a->count} license keys are expiring soon or have already expired:

{$a->expiringkeys}

Please review and take appropriate action:
{$a->adminurl}

This is an automated message from the KERNELiOS License Management System.';

$string['securityalert_subject'] = 'Security Alert: {$a->alerttype}';
$string['securityalert_message'] = 'A security alert has been triggered:

Alert Type: {$a->alerttype}
Details: {$a->alertdata}

Please review the security dashboard:
{$a->adminurl}

This is an automated message from the KERNELiOS License Management System.';

// Manual key addition functionality
$string['manualkeyaddition'] = 'Manual Key Addition';
$string['manualkeyaddition_desc'] = 'Add specific license keys manually to partner accounts. This is useful for adding custom keys or keys from external sources.';
$string['manualkeyaddition_tip1'] = 'Keys must be unique across the entire system';
$string['manualkeyaddition_tip2'] = 'Use uppercase letters, numbers, and hyphens only';
$string['manualkeyaddition_tip3'] = 'Partner must be assigned to the selected course type';
$string['manualkeyaddition_tip4'] = 'Keys are automatically converted to uppercase';
$string['manualkeystring'] = 'License Key String';
$string['manualkeystring_help'] = 'Enter the specific license key you want to add. Must be unique and contain only uppercase letters, numbers, and hyphens.';
$string['addkey'] = 'Add License Key';
$string['addnewkey'] = 'Add New License Key';
$string['recentmanualadditions'] = 'Recent Manual Additions';
$string['norecentmanualadditions'] = 'No recent manual key additions found';
$string['keyaddedsuccessfully'] = 'License key "{$a->keystring}" successfully added to {$a->partner} for {$a->coursetype}';
$string['createdby'] = 'Created By';
$string['keynotes'] = 'Notes';
$string['keynotes_help'] = 'Optional notes about this license key (max 500 characters)';
$string['keystatus'] = 'Key Status';
$string['keystatus_help'] = 'Initial status for this license key';

// Manual key addition errors
$string['error:keyalreadyexists'] = 'This license key already exists in the system';
$string['error:keytooShort'] = 'License key must be at least 5 characters long';
$string['error:invalidkeyformat'] = 'License key can only contain uppercase letters, numbers, and hyphens';
$string['error:partnernotassignedtocoursetype'] = 'Partner "{$a->partner}" is not assigned to course type "{$a->coursetype}"';
$string['error:expirydatebeforevalidfrom'] = 'Expiry date must be after the valid from date';
$string['error:keyadditionfailed'] = 'Failed to add license key';
$string['nopartnersavailable'] = 'No partners available. Please create partners first.';
$string['nocoursetypesavailable'] = 'No course types available. Please create course types first.';

// Enhanced statistics
$string['usagetrends'] = 'Usage Trends (Last 30 Days)';
$string['partnerperformance'] = 'Top Performing Partners';
$string['systemhealth'] = 'System Health Indicators';
$string['usagerate'] = 'Usage Rate';
$string['keysused'] = 'Keys Used';
$string['nousagedata'] = 'No usage data available for the selected period';
$string['nopartnerdata'] = 'No partner performance data available';
$string['lowstockpartners'] = 'Partners with Low Stock';
$string['lowstockpartners_desc'] = 'Partners with 5 or fewer available license keys';
$string['expiredkeys'] = 'Expired Available Keys';
$string['expiredkeys_desc'] = 'Available keys that have passed their expiration date';
$string['unassignedpartners'] = 'Unassigned Partners';
$string['unassignedpartners_desc'] = 'Partners with no course type assignments';

// Security and validation
$string['invalidkeyformat'] = 'Invalid license key format';
$string['ratelimitexceeded'] = 'Too many attempts. Please try again later.';
$string['event:securityviolation'] = 'License security violation';
$string['securityviolation'] = 'Security Violation';
$string['securityaudit'] = 'Security Audit';
$string['suspiciousactivity'] = 'Suspicious Activity Detected';
$string['securitylog'] = 'Security Log';
$string['viewsecuritylog'] = 'View Security Log';
$string['security'] = 'Security';
$string['securityviolations24h'] = 'Security Violations (24h)';
$string['ratelimithits24h'] = 'Rate Limit Hits (24h)';
$string['suspiciouspatterns'] = 'Suspicious Patterns';
$string['securitymonitoring'] = 'Security Monitoring';
$string['active'] = 'Active';
$string['inactive'] = 'Inactive';
$string['recentsecuritylog'] = 'Recent Security Log Entries';
$string['nosecuritylogs'] = 'No security log entries found';
$string['securityconfiguration'] = 'Security Configuration';
$string['ratelimitsettings'] = 'Rate Limit Settings';
$string['maxattemptsperip'] = 'Max attempts per IP';
$string['maxattemptsperuser'] = 'Max attempts per user';
$string['ratelimitwindow'] = 'Rate limit window';
$string['securityfeatures'] = 'Security Features';
$string['keyformatvalidation'] = 'Key format validation';
$string['partneraccesscontrol'] = 'Partner access control';
$string['ratelimiting'] = 'Rate limiting';
$string['auditlogging'] = 'Audit logging';
$string['suspiciouspatterndetection'] = 'Suspicious pattern detection';
$string['runsecurityaudit'] = 'Run Security Audit';
$string['timestamp'] = 'Timestamp';
$string['user'] = 'User';
$string['action'] = 'Action';
$string['ipaddress'] = 'IP Address';
$string['details'] = 'Details';
$string['unknown'] = 'Unknown';

// Scheduled tasks
$string['notificationchecktask'] = 'License Tracker Notification Check';

// Testing functionality
$string['testfunctionality'] = 'Test System Functionality';
$string['testfunctionality_desc'] = 'Comprehensive testing of all License Tracker features and components';
