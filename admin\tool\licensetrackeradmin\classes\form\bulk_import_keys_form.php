<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Bulk import keys form for tool_licensetrackeradmin plugin.
 *
 * @package    tool_licensetrackeradmin
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace tool_licensetrackeradmin\form;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/formslib.php');

/**
 * Form for bulk importing license keys.
 */
class bulk_import_keys_form extends \moodleform {

    /**
     * Define the form.
     */
    public function definition() {
        $mform = $this->_form;

        // CSV file upload
        $mform->addElement('header', 'csvheader', get_string('csvfile', 'tool_licensetrackeradmin'));
        
        $mform->addElement('static', 'csvformat', get_string('csvformat', 'tool_licensetrackeradmin'), 
                          get_string('csvheader_keys', 'tool_licensetrackeradmin'));

        $mform->addElement('filepicker', 'csvfile', get_string('csvfile', 'tool_licensetrackeradmin'), 
                          null, array('accepted_types' => array('.csv')));
        $mform->addRule('csvfile', null, 'required', null, 'client');

        // Options
        $mform->addElement('header', 'optionsheader', get_string('options', 'core'));
        
        $mform->addElement('advcheckbox', 'skipfirstrow', get_string('skipfirstrow', 'core'), 
                          get_string('skipfirstrow_desc', 'core'));
        $mform->setDefault('skipfirstrow', 1);

        $mform->addElement('advcheckbox', 'updateexisting', get_string('updateexisting', 'core'), 
                          get_string('updateexisting_desc', 'core'));
        $mform->setDefault('updateexisting', 0);

        // Action buttons
        $this->add_action_buttons(true, get_string('importkeys', 'tool_licensetrackeradmin'));
    }

    /**
     * Validate the form data.
     *
     * @param array $data Form data
     * @param array $files Form files
     * @return array Validation errors
     */
    public function validation($data, $files) {
        $errors = parent::validation($data, $files);

        // Validate CSV file
        if (empty($data['csvfile'])) {
            $errors['csvfile'] = get_string('required');
        }

        return $errors;
    }
}
