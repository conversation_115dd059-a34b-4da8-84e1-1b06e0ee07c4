<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Admin keys tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

// CRITICAL: Verify admin permissions
$context = context_system::instance();
require_capability('local/licensetracker:manageallkeys', $context);

// Get filters
$partnerfilter = optional_param('partner', 0, PARAM_INT);
$coursetypefilter = optional_param('coursetype', 0, PARAM_INT);
$statusfilter = optional_param('status', '', PARAM_ALPHA);
$searchfilter = optional_param('search', '', PARAM_TEXT);

$filters = array();
if ($partnerfilter) {
    $filters['partner'] = $partnerfilter;
}
if ($coursetypefilter) {
    $filters['coursetype'] = $coursetypefilter;
}
if ($statusfilter) {
    $filters['status'] = $statusfilter;
}
if ($searchfilter) {
    $filters['search'] = $searchfilter;
}

// Get keys
$keys = local_licensetracker_get_keys($filters);

// Build filter form
$filterform = '';
$filterform .= '<div class="card mb-3">';
$filterform .= '<div class="card-header">' . get_string('filters', 'core') . '</div>';
$filterform .= '<div class="card-body">';
$filterform .= '<form method="get" action="' . $CFG->wwwroot . '/local/licensetracker/index.php">';
$filterform .= '<input type="hidden" name="tab" value="keys">';
$filterform .= '<div class="row">';

// Partner filter
$partners = $DB->get_records_menu('local_lt_partners', null, 'partnername', 'id, partnername');
$filterform .= '<div class="col-md-3">';
$filterform .= '<label for="partner">' . get_string('partner', 'local_licensetracker') . '</label>';
$filterform .= '<select name="partner" id="partner" class="form-control">';
$filterform .= '<option value="">' . get_string('all') . '</option>';
foreach ($partners as $id => $name) {
    $selected = ($partnerfilter == $id) ? 'selected' : '';
    $filterform .= '<option value="' . $id . '" ' . $selected . '>' . $name . '</option>';
}
$filterform .= '</select>';
$filterform .= '</div>';

// Course type filter
$coursetypes = $DB->get_records_menu('local_lt_coursetypes', null, 'name', 'id, name');
$filterform .= '<div class="col-md-3">';
$filterform .= '<label for="coursetype">' . get_string('coursetype', 'local_licensetracker') . '</label>';
$filterform .= '<select name="coursetype" id="coursetype" class="form-control">';
$filterform .= '<option value="">' . get_string('all') . '</option>';
foreach ($coursetypes as $id => $name) {
    $selected = ($coursetypefilter == $id) ? 'selected' : '';
    $filterform .= '<option value="' . $id . '" ' . $selected . '>' . $name . '</option>';
}
$filterform .= '</select>';
$filterform .= '</div>';

// Status filter
$statuses = array('available', 'used', 'revoked');
$filterform .= '<div class="col-md-3">';
$filterform .= '<label for="status">' . get_string('status', 'local_licensetracker') . '</label>';
$filterform .= '<select name="status" id="status" class="form-control">';
$filterform .= '<option value="">' . get_string('all') . '</option>';
foreach ($statuses as $status) {
    $selected = ($statusfilter == $status) ? 'selected' : '';
    $filterform .= '<option value="' . $status . '" ' . $selected . '>' . get_string($status, 'local_licensetracker') . '</option>';
}
$filterform .= '</select>';
$filterform .= '</div>';

// Search filter
$filterform .= '<div class="col-md-3">';
$filterform .= '<label for="search">' . get_string('search') . '</label>';
$filterform .= '<input type="text" name="search" id="search" class="form-control" value="' . s($searchfilter) . '" placeholder="' . get_string('keystring', 'local_licensetracker') . '">';
$filterform .= '</div>';

$filterform .= '</div>';
$filterform .= '<div class="mt-3">';
$filterform .= '<button type="submit" class="btn btn-primary">' . get_string('filter', 'core') . '</button>';
$filterform .= '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php?tab=keys" class="btn btn-secondary ml-2">' . get_string('clear', 'core') . '</a>';
$filterform .= '</div>';
$filterform .= '</form>';
$filterform .= '</div>';
$filterform .= '</div>';

// Build keys table
$keytable = new \local_licensetracker\output\key_table($keys, true, $filters);
$keystablehtml = $renderer->render_key_table($keytable);

return $filterform . $keystablehtml;
