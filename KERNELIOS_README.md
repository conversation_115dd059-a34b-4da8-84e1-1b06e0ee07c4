# 🎓 KERNELiOS License Management System for Moodle 4.0

[![Moodle 4.0+](https://img.shields.io/badge/Moodle-4.0%2B-blue.svg)](https://moodle.org/)
[![PHP 7.4+](https://img.shields.io/badge/PHP-7.4%2B-purple.svg)](https://php.net/)
[![License: GPL v3](https://img.shields.io/badge/License-GPLv3-blue.svg)](https://www.gnu.org/licenses/gpl-3.0)
[![Status: Production Ready](https://img.shields.io/badge/Status-Production%20Ready-green.svg)]()

A comprehensive, enterprise-grade license key management system for Moodle 4.0 that enables partner organizations to manage and distribute course access through secure license keys. Built specifically for KERNELiOS educational platform with advanced security, scalability, and partner management features.

## 🚀 Key Features

### 🏢 **Partner Management**
- Create and manage partner organizations with detailed profiles
- Partner-specific dashboards with usage statistics
- Hierarchical partner access control
- Partner-specific pricing and course assignments

### 🔑 **Advanced License Key System**
- Generate unique license keys with course-specific prefixes
- Bulk key generation with background processing
- Key expiration and validity date management
- Comprehensive key status tracking (available, used, revoked, expired)

### 👥 **Student Registration**
- Partners can register students using license keys
- Bulk student import via CSV files
- Automatic course enrollment upon registration
- User type classification (partner_student vs regular students)

### 📊 **Course Type Management**
- Define different course types with custom pricing
- Course-specific license prefixes (e.g., CHCSS-1234-ABCD)
- Partner-specific pricing overrides
- Course enrollment automation

### 📈 **Statistics & Analytics**
- Real-time usage statistics and trends
- Partner performance metrics
- System health monitoring
- Comprehensive reporting dashboards

### 🔒 **Enterprise Security**
- Rate limiting on key usage attempts
- Comprehensive audit logging
- Suspicious activity detection
- Partner access control enforcement
- Security violation monitoring

### 📧 **Automated Notifications**
- License request notifications
- Low stock alerts
- Key expiry warnings
- Security alerts
- Bulk operation completion notifications

### ⚡ **Bulk Operations**
- CSV import/export capabilities
- Background processing for large operations
- API access for external integrations
- Manual key addition for custom scenarios

## 🏗️ System Architecture

The system consists of three integrated Moodle plugins:

### 1. **Local Plugin** (`local/licensetracker`)
- Main dashboard and user interfaces
- Core license management functionality
- Partner and admin dashboards
- Statistics and reporting

### 2. **Authentication Plugin** (`auth/licensetracker`)
- Custom user registration with license keys
- User type management
- Profile field integration
- Automatic course enrollment

### 3. **Admin Tool Plugin** (`admin/tool/licensetrackeradmin`)
- Advanced administrative features
- Bulk operations and imports
- API endpoints for external integration
- System configuration and monitoring

## 📋 Requirements

### **Minimum System Requirements**
- **Moodle**: 4.0 or higher
- **PHP**: 7.4 or higher (8.0+ recommended)
- **Database**: MySQL 5.7+ or PostgreSQL 10.0+
- **Memory**: 512MB+ (2GB+ recommended for production)
- **Storage**: 100MB+ for plugin files

### **Recommended Production Environment**
- **Moodle**: 4.1+ with latest security updates
- **PHP**: 8.1+ with OPcache enabled
- **Database**: MySQL 8.0+ or PostgreSQL 13+
- **Memory**: 4GB+ RAM
- **Storage**: SSD storage for optimal performance

## 🚀 Quick Installation

### **Step 1: Download and Extract**
```bash
# Extract plugin files to your Moodle installation
unzip kernelios-license-plugin.zip
cp -r local/licensetracker /path/to/moodle/local/
cp -r auth/licensetracker /path/to/moodle/auth/
cp -r admin/tool/licensetrackeradmin /path/to/moodle/admin/tool/
```

### **Step 2: Install via Moodle**
1. Visit your Moodle site as an administrator
2. Navigate to **Site Administration > Notifications**
3. Follow the installation prompts
4. Complete the database upgrade process

### **Step 3: Initial Configuration**
1. **Enable Authentication Plugin**: Site Administration > Plugins > Authentication > Manage authentication
2. **Configure Capabilities**: Assign appropriate roles and permissions
3. **Create Course Types**: Define your course offerings
4. **Add Partners**: Create your first partner organization

## 📚 Comprehensive Documentation

### **For Administrators**
- 📖 [**Complete Installation Guide**](COMPLETE_INSTALLATION_GUIDE.md) - Detailed setup instructions
- 🔧 [**Administrator Manual**](ADMINISTRATOR_MANUAL.md) - Complete admin guide
- 🔍 [**System Validation**](local/licensetracker/system_validation.php) - Built-in testing tools

### **For Partners**
- 👥 [**Partner Manual**](PARTNER_MANUAL.md) - Partner user guide
- 📊 Partner Dashboard - Integrated interface for key management

### **For Developers**
- 🔌 [**API Documentation**](API_DOCUMENTATION.md) - Complete API reference
- 🧪 [**Testing Tools**](local/licensetracker/test_functionality.php) - Comprehensive testing suite

## ✅ Quality Assurance

### **Moodle 4.0 Compatibility**
- ✅ Fully tested with Moodle 4.0, 4.1, and 4.2
- ✅ Bootstrap 4+ compatible UI
- ✅ Responsive design for all devices
- ✅ Accessibility standards compliant
- ✅ No deprecated functions used

### **Security Standards**
- ✅ OWASP security guidelines compliance
- ✅ SQL injection prevention
- ✅ XSS protection
- ✅ CSRF protection on all forms
- ✅ Comprehensive input validation

### **Performance Optimization**
- ✅ Database query optimization
- ✅ Proper indexing for large datasets
- ✅ Background processing for heavy operations
- ✅ Caching implementation
- ✅ Memory usage optimization

## 🧪 Testing and Validation

### **Built-in Testing Tools**
```bash
# Run system validation
/local/licensetracker/system_validation.php

# Run functionality tests
/local/licensetracker/test_functionality.php

# Run integration tests
/local/licensetracker/integration_test.php
```

### **Testing Checklist**
- [x] Admin dashboard functionality
- [x] Partner dashboard functionality
- [x] Student registration workflow
- [x] License key generation and validation
- [x] Security features and access controls
- [x] Email notifications
- [x] Bulk operations
- [x] API endpoints

## 🔧 Configuration Highlights

### **Core Settings**
- License key format customization
- Email notification preferences
- Security settings (rate limiting, etc.)
- Performance optimization options

### **Partner Management**
- Partner-specific pricing
- Course type assignments
- Access control levels
- Notification preferences

### **Security Features**
- Rate limiting configuration
- Audit logging levels
- Security monitoring options
- Access control policies

## 🌟 Advanced Features

### **API Integration**
- RESTful API for external systems
- Bulk operations via API
- Authentication and authorization
- Real-time data synchronization

### **Reporting and Analytics**
- Real-time usage dashboards
- Partner performance metrics
- System health monitoring
- Custom report generation

### **Scalability Features**
- Background task processing
- Database optimization
- Caching strategies
- Load balancing support

## 🤝 Support and Community

### **Getting Help**
1. **Documentation**: Check the comprehensive guides
2. **System Validation**: Run the built-in testing tools
3. **Troubleshooting**: Use the system validation features
4. **Community**: KERNELiOS community support

### **Reporting Issues**
- Use the built-in error logging
- Check system validation results
- Provide detailed error messages
- Include system configuration details

## 📄 License

This project is licensed under the GNU General Public License v3.0 or later.

## 🏆 Credits

**Developed by**: KERNELiOS Development Team  
**Version**: **********  
**Last Updated**: January 9, 2025  
**Moodle Compatibility**: 4.0+

---

## 🚀 Ready to Get Started?

1. **📖 Read the [Complete Installation Guide](COMPLETE_INSTALLATION_GUIDE.md)**
2. **⚡ Follow the Quick Installation steps above**
3. **🔧 Configure your first course type and partner**
4. **🎉 Start managing license keys like a pro!**

**Need help?** Check out our comprehensive documentation or run the system validation tools to ensure everything is working correctly.
