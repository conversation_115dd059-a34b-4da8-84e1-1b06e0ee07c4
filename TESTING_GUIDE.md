# 🧪 License Tracker - Comprehensive Testing Guide

## 🎯 Testing Overview

This guide provides comprehensive testing scenarios to ensure the License Tracker system works perfectly in your Moodle 4.0 environment.

## 🚨 Pre-Testing Checklist

Before starting tests, ensure:
- ✅ All three plugins are installed
- ✅ Database tables are created
- ✅ Capabilities are defined
- ✅ Basic configuration is complete
- ✅ Test users and courses are available

## 📋 Test Scenarios

### 🔐 Test Category 1: License Enforcement

#### Test 1.1: Local Student Registration (No License Required)
**Objective**: Verify local students can register without license keys

**Prerequisites**:
- Local countries configured (e.g., "US,CA,GB")
- System enabled but not enforcing for all

**Steps**:
1. Navigate to registration page
2. Fill form with country = "US" (local country)
3. Leave license key field empty
4. Submit registration

**Expected Result**: ✅ Registration successful, user created

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

#### Test 1.2: Global Student Registration Without License (Should Fail)
**Objective**: Verify global students are blocked without license keys

**Prerequisites**:
- User country NOT in local countries list
- System enabled and enforcing

**Steps**:
1. Navigate to registration page
2. Fill form with country = "IN" (global country)
3. Leave license key field empty
4. Submit registration

**Expected Result**: ❌ Registration blocked with error message

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

#### Test 1.3: Global Student Registration With Valid License
**Objective**: Verify global students can register with valid license keys

**Prerequisites**:
- Valid license key available
- Course type configured
- Partner exists

**Steps**:
1. Navigate to registration page
2. Fill form with country = "IN" (global country)
3. Enter valid license key
4. Submit registration

**Expected Result**: ✅ Registration successful, key marked as used, user enrolled in course

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

#### Test 1.4: Invalid License Key Rejection
**Objective**: Verify invalid license keys are rejected

**Steps**:
1. Navigate to registration page
2. Fill form with global country
3. Enter invalid license key "INVALID-KEY-1234"
4. Submit registration

**Expected Result**: ❌ Registration blocked with "Invalid license key" error

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

### 👥 Test Category 2: Partner Management

#### Test 2.1: Partner Dashboard Access
**Objective**: Verify partners can access their dashboard

**Prerequisites**:
- Partner user account exists
- Partner role assigned with correct capabilities

**Steps**:
1. Login as partner user
2. Navigate to License Tracker
3. Verify partner dashboard loads

**Expected Result**: ✅ Partner dashboard displays with partner-specific data

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

#### Test 2.2: Partner Key Viewing (Own Keys Only)
**Objective**: Verify partners can only see their own keys

**Prerequisites**:
- Multiple partners exist
- Keys assigned to different partners

**Steps**:
1. Login as Partner A
2. Navigate to "My Keys" tab
3. Verify only Partner A's keys are visible

**Expected Result**: ✅ Only Partner A's keys displayed

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

#### Test 2.3: Partner Student Registration
**Objective**: Verify partners can register students using their keys

**Prerequisites**:
- Partner has available keys
- Course type configured

**Steps**:
1. Login as partner
2. Navigate to "Register Student" tab
3. Fill student registration form
4. Select available license key
5. Submit registration

**Expected Result**: ✅ Student registered, key marked as used, student enrolled

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

### 🔧 Test Category 3: Admin Functions

#### Test 3.1: License Key Generation
**Objective**: Verify admins can generate license keys

**Prerequisites**:
- Admin user with proper capabilities
- Partner and course type exist

**Steps**:
1. Login as admin
2. Navigate to License Tracker → Generate Keys
3. Select partner and course type
4. Generate 5 keys
5. Verify keys are created

**Expected Result**: ✅ 5 keys generated and assigned to partner

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

#### Test 3.2: Partner Management
**Objective**: Verify admins can manage partners

**Steps**:
1. Login as admin
2. Navigate to Partners tab
3. Create new partner
4. Edit existing partner
5. Verify changes are saved

**Expected Result**: ✅ Partner operations successful

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

#### Test 3.3: Course Type Management
**Objective**: Verify admins can manage course types

**Steps**:
1. Login as admin
2. Navigate to Course Types tab
3. Create new course type
4. Link to Moodle course
5. Set default role and enrollment method

**Expected Result**: ✅ Course type created and configured

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

### 🔒 Test Category 4: Security Tests

#### Test 4.1: Unauthorized Access Prevention
**Objective**: Verify non-admin users cannot access admin functions

**Prerequisites**:
- Regular user account (no admin capabilities)

**Steps**:
1. Login as regular user
2. Try to access `/local/licensetracker/tabs/admin_keys.php`
3. Verify access is denied

**Expected Result**: ❌ Access denied with capability error

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

#### Test 4.2: Partner Data Isolation
**Objective**: Verify partners cannot access other partners' data

**Prerequisites**:
- Two partner accounts with different data

**Steps**:
1. Login as Partner A
2. Try to access Partner B's keys via URL manipulation
3. Verify access is denied

**Expected Result**: ❌ Access denied or no data shown

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

#### Test 4.3: SQL Injection Prevention
**Objective**: Verify system is protected against SQL injection

**Steps**:
1. Navigate to license key validation
2. Enter malicious input: `'; DROP TABLE users; --`
3. Submit form

**Expected Result**: ✅ Input sanitized, no database damage

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

### 📊 Test Category 5: Performance Tests

#### Test 5.1: Large Dataset Performance
**Objective**: Verify system performs well with large datasets

**Prerequisites**:
- Generate 1000+ license keys
- Create multiple partners and course types

**Steps**:
1. Navigate to admin dashboard
2. Load key management page
3. Measure page load time
4. Perform key search/filter operations

**Expected Result**: ✅ Page loads in <3 seconds, operations responsive

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

#### Test 5.2: Concurrent User Access
**Objective**: Verify system handles multiple simultaneous users

**Steps**:
1. Have multiple users access system simultaneously
2. Perform various operations (key generation, registration)
3. Monitor for conflicts or errors

**Expected Result**: ✅ No conflicts, all operations successful

**Actual Result**: [ ] Pass [ ] Fail
**Notes**: ________________________________

## 🔍 System Health Verification

### Database Integrity Check
```sql
-- Run these queries to verify data integrity

-- Check for orphaned keys
SELECT COUNT(*) FROM mdl_local_lt_keys k 
LEFT JOIN mdl_local_lt_partners p ON k.partnerid = p.id 
WHERE p.id IS NULL;

-- Check for invalid key statuses
SELECT COUNT(*) FROM mdl_local_lt_keys 
WHERE status NOT IN ('available', 'used', 'revoked', 'expired');

-- Check for duplicate key strings
SELECT keystring, COUNT(*) FROM mdl_local_lt_keys 
GROUP BY keystring HAVING COUNT(*) > 1;
```

### Configuration Verification
```php
// Run this PHP code to verify configuration
$config = get_config('local_licensetracker');
$authconfig = get_config('auth_licensetracker');

echo "System Enabled: " . ($config->enabled ? "Yes" : "No") . "\n";
echo "Auth Enabled: " . ($authconfig->enabled ? "Yes" : "No") . "\n";
echo "Enforce All: " . ($config->enforce_all ? "Yes" : "No") . "\n";
echo "Local Countries: " . ($config->local_countries ?: "None") . "\n";
```

## 📈 Performance Benchmarks

### Expected Performance Metrics
- **Page Load Time**: < 2 seconds
- **Key Validation**: < 100ms
- **Database Queries**: < 50ms average
- **Key Generation**: < 5 seconds for 100 keys

### Load Testing Recommendations
- Test with 100+ concurrent users
- Generate 10,000+ license keys
- Simulate high-frequency key validation
- Monitor database performance under load

## 🚨 Critical Test Failures

If any of these tests fail, **DO NOT deploy to production**:

1. ❌ Global students can register without licenses
2. ❌ Partners can access other partners' data
3. ❌ Admin functions accessible to non-admins
4. ❌ SQL injection vulnerabilities exist
5. ❌ License keys not properly validated

## ✅ Test Completion Checklist

- [ ] All license enforcement tests pass
- [ ] All partner management tests pass
- [ ] All admin function tests pass
- [ ] All security tests pass
- [ ] Performance meets benchmarks
- [ ] Database integrity verified
- [ ] Configuration validated
- [ ] Documentation reviewed

## 📞 Support

If tests fail or you encounter issues:
1. Check the troubleshooting section in INSTALLATION_GUIDE.md
2. Review system logs for error messages
3. Verify configuration settings
4. Run security audit and system status checks
5. Contact support with detailed test results

**Remember**: Thorough testing is essential before production deployment!
