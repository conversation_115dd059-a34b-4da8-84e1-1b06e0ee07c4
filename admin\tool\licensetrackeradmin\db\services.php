<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Web service definitions for tool_licensetrackeradmin plugin.
 *
 * @package    tool_licensetrackeradmin
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$functions = array(

    'tool_licensetrackeradmin_generate_keys' => array(
        'classname'   => 'tool_licensetrackeradmin\external\licensetracker_api',
        'methodname'  => 'generate_keys',
        'classpath'   => '',
        'description' => 'Generate license keys',
        'type'        => 'write',
        'capabilities' => 'local/licensetracker:manageallkeys',
        'services'    => array(MOODLE_OFFICIAL_MOBILE_SERVICE),
    ),

    'tool_licensetrackeradmin_check_key_status' => array(
        'classname'   => 'tool_licensetrackeradmin\external\licensetracker_api',
        'methodname'  => 'check_key_status',
        'classpath'   => '',
        'description' => 'Check license key status',
        'type'        => 'read',
        'capabilities' => '',
        'services'    => array(MOODLE_OFFICIAL_MOBILE_SERVICE),
    ),

    'tool_licensetrackeradmin_use_key' => array(
        'classname'   => 'tool_licensetrackeradmin\external\licensetracker_api',
        'methodname'  => 'use_key',
        'classpath'   => '',
        'description' => 'Use a license key for registration',
        'type'        => 'write',
        'capabilities' => '',
        'services'    => array(MOODLE_OFFICIAL_MOBILE_SERVICE),
    ),

);

$services = array(
    'License Tracker API' => array(
        'functions' => array(
            'tool_licensetrackeradmin_generate_keys',
            'tool_licensetrackeradmin_check_key_status',
            'tool_licensetrackeradmin_use_key'
        ),
        'restrictedusers' => 0,
        'enabled' => 1,
        'shortname' => 'licensetracker_api',
        'downloadfiles' => 0,
        'uploadfiles' => 0
    )
);
