<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Notification check task for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\task;

defined('MOODLE_INTERNAL') || die();

/**
 * Scheduled task to check for low stock and expiring keys.
 */
class notification_check_task extends \core\task\scheduled_task {

    /**
     * Get task name.
     *
     * @return string
     */
    public function get_name() {
        return get_string('notificationchecktask', 'local_licensetracker');
    }

    /**
     * Execute the task.
     */
    public function execute() {
        global $DB;

        mtrace('Starting license tracker notification checks...');

        // Check if notifications are enabled
        if (!get_config('local_licensetracker', 'email_notifications')) {
            mtrace('Email notifications are disabled. Skipping checks.');
            return;
        }

        $this->check_low_stock();
        $this->check_expiring_keys();
        $this->check_security_issues();

        mtrace('License tracker notification checks completed.');
    }

    /**
     * Check for partners with low stock.
     */
    private function check_low_stock() {
        global $DB;

        mtrace('Checking for low stock partners...');

        $low_stock_threshold = get_config('local_licensetracker', 'low_stock_threshold') ?: 10;

        $sql = "SELECT p.id, p.partnername, p.contactemail,
                       COUNT(CASE WHEN k.status = 'available' THEN 1 END) as available_count,
                       GROUP_CONCAT(CONCAT(ct.name, ': ', COUNT(CASE WHEN k.status = 'available' THEN 1 END)) SEPARATOR ', ') as stock_details
                FROM {local_lt_partners} p
                LEFT JOIN {local_lt_keys} k ON k.partnerid = p.id
                LEFT JOIN {local_lt_coursetypes} ct ON k.coursetypeid = ct.id
                GROUP BY p.id, p.partnername, p.contactemail
                HAVING available_count <= ? AND available_count > 0";

        $low_stock_partners = $DB->get_records_sql($sql, array($low_stock_threshold));

        foreach ($low_stock_partners as $partner) {
            // Check if we've already sent a notification recently (within 24 hours)
            $recent_notification = $DB->get_record_sql(
                "SELECT * FROM {local_lt_notifications_sent} 
                 WHERE partnerid = ? AND notification_type = 'low_stock' AND timesent > ?",
                array($partner->id, time() - 86400)
            );

            if (!$recent_notification) {
                mtrace("Sending low stock notification for partner: {$partner->partnername}");
                
                $success = local_licensetracker_send_low_stock_notification($partner, $partner->stock_details);
                
                if ($success) {
                    // Record that we sent the notification
                    $notification_record = new \stdClass();
                    $notification_record->partnerid = $partner->id;
                    $notification_record->notification_type = 'low_stock';
                    $notification_record->timesent = time();
                    
                    try {
                        $DB->insert_record('local_lt_notifications_sent', $notification_record);
                    } catch (\Exception $e) {
                        // Table might not exist, create it
                        $this->ensure_notifications_table();
                        $DB->insert_record('local_lt_notifications_sent', $notification_record);
                    }
                }
            }
        }

        mtrace('Low stock check completed. Found ' . count($low_stock_partners) . ' partners with low stock.');
    }

    /**
     * Check for expiring keys.
     */
    private function check_expiring_keys() {
        global $DB;

        mtrace('Checking for expiring keys...');

        $expiry_warning_days = get_config('local_licensetracker', 'expiry_warning_days') ?: 7;
        $warning_threshold = time() + ($expiry_warning_days * 86400);

        $sql = "SELECT k.*, p.partnername, ct.name as coursetypename
                FROM {local_lt_keys} k
                JOIN {local_lt_partners} p ON k.partnerid = p.id
                JOIN {local_lt_coursetypes} ct ON k.coursetypeid = ct.id
                WHERE k.status = 'available' 
                AND k.expireson IS NOT NULL 
                AND k.expireson <= ?
                ORDER BY k.expireson ASC";

        $expiring_keys = $DB->get_records_sql($sql, array($warning_threshold));

        if (!empty($expiring_keys)) {
            // Check if we've already sent a notification recently (within 24 hours)
            $recent_notification = $DB->get_record_sql(
                "SELECT * FROM {local_lt_notifications_sent} 
                 WHERE notification_type = 'key_expiry' AND timesent > ?",
                array(time() - 86400)
            );

            if (!$recent_notification) {
                mtrace("Sending key expiry notification for " . count($expiring_keys) . " keys");
                
                $expiry_details = '';
                foreach ($expiring_keys as $key) {
                    $expiry_date = userdate($key->expireson, '%d %b %Y');
                    $expiry_details .= "- {$key->keystring} ({$key->partnername} - {$key->coursetypename}) expires {$expiry_date}\n";
                }
                
                $success = local_licensetracker_send_key_expiry_notification($expiry_details);
                
                if ($success) {
                    // Record that we sent the notification
                    $notification_record = new \stdClass();
                    $notification_record->partnerid = 0; // System-wide notification
                    $notification_record->notification_type = 'key_expiry';
                    $notification_record->timesent = time();
                    
                    try {
                        $DB->insert_record('local_lt_notifications_sent', $notification_record);
                    } catch (\Exception $e) {
                        $this->ensure_notifications_table();
                        $DB->insert_record('local_lt_notifications_sent', $notification_record);
                    }
                }
            }
        }

        mtrace('Key expiry check completed. Found ' . count($expiring_keys) . ' expiring keys.');
    }

    /**
     * Check for security issues.
     */
    private function check_security_issues() {
        mtrace('Checking for security issues...');

        $security_manager = new \local_licensetracker\security\license_security_manager();
        $suspicious_patterns = $security_manager::detect_suspicious_patterns();

        if (!empty($suspicious_patterns)) {
            // Check if we've already sent a notification recently (within 1 hour)
            global $DB;
            $recent_notification = $DB->get_record_sql(
                "SELECT * FROM {local_lt_notifications_sent} 
                 WHERE notification_type = 'security_alert' AND timesent > ?",
                array(time() - 3600)
            );

            if (!$recent_notification) {
                mtrace("Sending security alert for " . count($suspicious_patterns) . " issues");
                
                $alert_details = '';
                foreach ($suspicious_patterns as $pattern) {
                    $alert_details .= "- {$pattern['description']} (Severity: {$pattern['severity']})\n";
                }
                
                $success = local_licensetracker_send_security_alert('Suspicious Activity Detected', $alert_details);
                
                if ($success) {
                    // Record that we sent the notification
                    $notification_record = new \stdClass();
                    $notification_record->partnerid = 0; // System-wide notification
                    $notification_record->notification_type = 'security_alert';
                    $notification_record->timesent = time();
                    
                    try {
                        $DB->insert_record('local_lt_notifications_sent', $notification_record);
                    } catch (\Exception $e) {
                        $this->ensure_notifications_table();
                        $DB->insert_record('local_lt_notifications_sent', $notification_record);
                    }
                }
            }
        }

        mtrace('Security check completed. Found ' . count($suspicious_patterns) . ' security issues.');
    }

    /**
     * Ensure the notifications sent table exists.
     */
    private function ensure_notifications_table() {
        global $DB;

        $dbman = $DB->get_manager();
        $table = new \xmldb_table('local_lt_notifications_sent');

        if (!$dbman->table_exists($table)) {
            $table->add_field('id', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, XMLDB_SEQUENCE, null);
            $table->add_field('partnerid', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);
            $table->add_field('notification_type', XMLDB_TYPE_CHAR, '50', null, XMLDB_NOTNULL, null, null);
            $table->add_field('timesent', XMLDB_TYPE_INTEGER, '10', null, XMLDB_NOTNULL, null, null);

            $table->add_key('primary', XMLDB_KEY_PRIMARY, array('id'));
            $table->add_key('partnerid', XMLDB_KEY_FOREIGN, array('partnerid'), 'local_lt_partners', array('id'));

            $table->add_index('notification_type', XMLDB_INDEX_NOTUNIQUE, array('notification_type'));
            $table->add_index('timesent', XMLDB_INDEX_NOTUNIQUE, array('timesent'));

            $dbman->create_table($table);
        }
    }
}
