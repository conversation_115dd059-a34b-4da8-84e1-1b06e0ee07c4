<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON>od<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Main dashboard for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once(__DIR__ . '/../../config.php');
require_once($CFG->dirroot . '/local/licensetracker/lib.php');

require_login();

$tab = optional_param('tab', 'keys', PARAM_ALPHA);
$action = optional_param('action', '', PARAM_ALPHA);
$id = optional_param('id', 0, PARAM_INT);

$context = context_system::instance();
$PAGE->set_context($context);
$PAGE->set_url('/local/licensetracker/index.php', array('tab' => $tab));
$PAGE->set_title(get_string('licensetracker', 'local_licensetracker'));
$PAGE->set_heading(get_string('licensetracker', 'local_licensetracker'));

// Check user permissions
$isadmin = local_licensetracker_is_admin();
$ispartner = local_licensetracker_is_partner();

if (!$isadmin && !$ispartner) {
    throw new moodle_exception('nopermissions', 'error', '', 'access license tracker');
}

// Get partner information if user is a partner
$partner = null;
if ($ispartner) {
    $partner = local_licensetracker_get_partner_for_user();
    if (!$partner && !$isadmin) {
        $PAGE->set_pagelayout('standard');
        echo $OUTPUT->header();
        echo $OUTPUT->notification(get_string('nopartnerlinked', 'local_licensetracker'), 'error');
        echo $OUTPUT->footer();
        exit;
    }
}

// Handle actions
if ($action && $isadmin) {
    require_sesskey();
    
    switch ($action) {
        case 'revokekey':
            if ($id) {
                local_licensetracker_update_key_status($id, 'revoked');
                redirect($PAGE->url, get_string('keyrevoked', 'local_licensetracker'), null, \core\output\notification::NOTIFY_SUCCESS);
            }
            break;
            
        case 'deletekey':
            if ($id) {
                $DB->delete_records('local_licensetracker_keys', array('id' => $id));
                redirect($PAGE->url, get_string('keydeleted', 'local_licensetracker'), null, \core\output\notification::NOTIFY_SUCCESS);
            }
            break;
    }
}

// Set up page layout
$PAGE->set_pagelayout('admin');

// Initialize renderer
$renderer = $PAGE->get_renderer('local_licensetracker');

echo $OUTPUT->header();

// Determine which dashboard to show
if ($isadmin) {
    // Admin dashboard
    $content = '';
    
    switch ($tab) {
        case 'overview':
            $content = include(__DIR__ . '/tabs/admin_overview.php');
            break;

        case 'keys':
            $content = include(__DIR__ . '/tabs/admin_keys.php');
            break;
            
        case 'generatekeys':
            $content = include(__DIR__ . '/tabs/admin_generatekeys.php');
            break;

        case 'manualkeyaddition':
            $content = include(__DIR__ . '/tabs/admin_manual_key_addition.php');
            break;

        case 'registeruser':
            $content = include(__DIR__ . '/tabs/admin_registeruser.php');
            break;

        case 'partners':
            $content = include(__DIR__ . '/tabs/admin_partners.php');
            break;
            
        case 'coursetypes':
            $content = include(__DIR__ . '/tabs/admin_coursetypes.php');
            break;
            
        case 'statistics':
            $content = include(__DIR__ . '/tabs/admin_statistics.php');
            break;

        case 'security':
            $content = include(__DIR__ . '/tabs/admin_security.php');
            break;

        case 'purchase':
            $content = $renderer->render_coming_soon();
            break;
            
        default:
            $tab = 'overview';
            $content = include(__DIR__ . '/tabs/admin_overview.php');
    }
    
    $dashboard = new \local_licensetracker\output\admin_dashboard($tab, $content);
    echo $renderer->render_admin_dashboard($dashboard);
    
} else if ($partner) {
    // Partner dashboard
    $content = '';
    
    switch ($tab) {
        case 'assignedkeys':
            $content = include(__DIR__ . '/tabs/partner_keys.php');
            break;
            
        case 'statistics':
            $content = include(__DIR__ . '/tabs/partner_statistics.php');
            break;
            
        case 'register':
            $content = include(__DIR__ . '/tabs/partner_register.php');
            break;

        case 'bulkregister':
            $content = include(__DIR__ . '/tabs/partner_bulk_register.php');
            break;

        case 'requestlicenses':
            $content = include(__DIR__ . '/tabs/partner_request_licenses.php');
            break;
            
        default:
            $tab = 'assignedkeys';
            $content = include(__DIR__ . '/tabs/partner_keys.php');
    }
    
    $dashboard = new \local_licensetracker\output\partner_dashboard($tab, $content, $partner);
    echo $renderer->render_partner_dashboard($dashboard);
}

echo $OUTPUT->footer();
