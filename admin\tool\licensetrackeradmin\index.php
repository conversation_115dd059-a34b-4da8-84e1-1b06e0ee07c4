<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Main interface for tool_licensetrackeradmin plugin.
 *
 * @package    tool_licensetrackeradmin
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once(__DIR__ . '/../../../config.php');
require_once($CFG->libdir . '/adminlib.php');

admin_externalpage_setup('tool_licensetrackeradmin');

$tab = optional_param('tab', 'bulkimportkeys', PARAM_ALPHA);
$action = optional_param('action', '', PARAM_ALPHA);

$context = context_system::instance();
require_capability('tool/licensetrackeradmin:manage', $context);

$PAGE->set_url('/admin/tool/licensetrackeradmin/index.php', array('tab' => $tab));
$PAGE->set_title(get_string('pluginname', 'tool_licensetrackeradmin'));
$PAGE->set_heading(get_string('pluginname', 'tool_licensetrackeradmin'));

echo $OUTPUT->header();

// Tab navigation
$tabs = array(
    'bulkimportkeys' => array(
        'name' => get_string('bulkimportkeys', 'tool_licensetrackeradmin'),
        'url' => new moodle_url('/admin/tool/licensetrackeradmin/index.php', array('tab' => 'bulkimportkeys'))
    ),
    'bulkimportpartners' => array(
        'name' => get_string('bulkimportpartners', 'tool_licensetrackeradmin'),
        'url' => new moodle_url('/admin/tool/licensetrackeradmin/index.php', array('tab' => 'bulkimportpartners'))
    ),
    'bulkexportkeys' => array(
        'name' => get_string('bulkexportkeys', 'tool_licensetrackeradmin'),
        'url' => new moodle_url('/admin/tool/licensetrackeradmin/index.php', array('tab' => 'bulkexportkeys'))
    ),
    'bulkexportpartners' => array(
        'name' => get_string('bulkexportpartners', 'tool_licensetrackeradmin'),
        'url' => new moodle_url('/admin/tool/licensetrackeradmin/index.php', array('tab' => 'bulkexportpartners'))
    ),
    'activitylogs' => array(
        'name' => get_string('activitylogs', 'tool_licensetrackeradmin'),
        'url' => new moodle_url('/admin/tool/licensetrackeradmin/index.php', array('tab' => 'activitylogs'))
    ),
    'settings' => array(
        'name' => get_string('settings', 'tool_licensetrackeradmin'),
        'url' => new moodle_url('/admin/settings.php', array('section' => 'tool_licensetrackeradmin_settings'))
    )
);

// Render tabs
echo '<ul class="nav nav-tabs mb-4">';
foreach ($tabs as $key => $tabdata) {
    $active = ($key === $tab) ? 'active' : '';
    echo '<li class="nav-item">';
    echo '<a class="nav-link ' . $active . '" href="' . $tabdata['url'] . '">' . $tabdata['name'] . '</a>';
    echo '</li>';
}
echo '</ul>';

// Tab content
switch ($tab) {
    case 'bulkimportkeys':
        include(__DIR__ . '/tabs/bulk_import_keys.php');
        break;
        
    case 'bulkimportpartners':
        include(__DIR__ . '/tabs/bulk_import_partners.php');
        break;
        
    case 'bulkexportkeys':
        include(__DIR__ . '/tabs/bulk_export_keys.php');
        break;
        
    case 'bulkexportpartners':
        include(__DIR__ . '/tabs/bulk_export_partners.php');
        break;
        
    case 'activitylogs':
        include(__DIR__ . '/tabs/activity_logs.php');
        break;
        
    default:
        include(__DIR__ . '/tabs/bulk_import_keys.php');
}

echo $OUTPUT->footer();
