<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Database upgrade script for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Upgrade function for local_licensetracker plugin.
 *
 * @param int $oldversion The old version of the plugin
 * @return bool True on success
 */
function xmldb_local_licensetracker_upgrade($oldversion) {
    global $DB;

    $dbman = $DB->get_manager();

    if ($oldversion < **********) {
        // Add licenseprefix field to local_lt_coursetypes table.
        $table = new xmldb_table('local_lt_coursetypes');
        $field = new xmldb_field('licenseprefix', XMLDB_TYPE_CHAR, '10', null, XMLDB_NOTNULL, null, null, 'defaultprice');

        if (!$dbman->field_exists($table, $field)) {
            $dbman->add_field($table, $field);
        }

        // Add unique index for licenseprefix.
        $index = new xmldb_index('licenseprefix', XMLDB_INDEX_UNIQUE, array('licenseprefix'));
        if (!$dbman->index_exists($table, $index)) {
            $dbman->add_index($table, $index);
        }

        // Set default license prefixes for existing course types.
        $coursetypes = $DB->get_records('local_lt_coursetypes');
        foreach ($coursetypes as $coursetype) {
            if (empty($coursetype->licenseprefix)) {
                // Generate a default prefix from the course name.
                $prefix = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $coursetype->name), 0, 5));
                if (empty($prefix) || strlen($prefix) < 2) {
                    $prefix = 'COURSE';
                }

                // Ensure uniqueness.
                $counter = 1;
                $originalprefix = $prefix;
                while ($DB->record_exists('local_lt_coursetypes', array('licenseprefix' => $prefix))) {
                    if ($counter > 99) {
                        // Fallback to random if too many conflicts
                        $prefix = 'CT' . random_int(100, 999);
                        break;
                    }
                    $prefix = $originalprefix . $counter;
                    $counter++;
                }

                $DB->set_field('local_lt_coursetypes', 'licenseprefix', $prefix, array('id' => $coursetype->id));
            }
        }

        upgrade_plugin_savepoint(true, **********, 'local', 'licensetracker');
    }

    return true;
}
