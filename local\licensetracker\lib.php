<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Library functions for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Check if user is an administrator with license tracker permissions.
 *
 * @param int $userid User ID (optional, defaults to current user)
 * @return bool
 */
function local_licensetracker_is_admin($userid = null) {
    global $USER;
    
    if ($userid === null) {
        $userid = $USER->id;
    }
    
    $context = context_system::instance();
    return has_capability('local/licensetracker:manageallkeys', $context, $userid);
}

/**
 * Check if user is a partner.
 *
 * @param int $userid User ID (optional, defaults to current user)
 * @return bool
 */
function local_licensetracker_is_partner($userid = null) {
    global $USER;
    
    if ($userid === null) {
        $userid = $USER->id;
    }
    
    $context = context_system::instance();
    return has_capability('local/licensetracker:viewpartnerkeys', $context, $userid);
}

/**
 * Get partner information for a given Moodle user.
 *
 * @param int $userid User ID (optional, defaults to current user)
 * @return object|false Partner object or false if not found
 */
function local_licensetracker_get_partner_for_user($userid = null) {
    global $DB, $USER;
    
    if ($userid === null) {
        $userid = $USER->id;
    }
    
    return $DB->get_record('local_lt_partners', array('moodleuserid' => $userid));
}

/**
 * Generate a unique license key with course-specific prefix.
 *
 * @param int $coursetypeid Course type ID to get the prefix from
 * @return string Unique license key with course prefix
 */
function local_licensetracker_generate_unique_key($coursetypeid) {
    global $DB;

    // Get course type to get the license prefix
    $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $coursetypeid));
    if (!$coursetype) {
        throw new moodle_exception('error:coursenotfound', 'local_licensetracker');
    }

    $prefix = $coursetype->licenseprefix;

    // Get key format from admin tool settings (default format if not set)
    $keyformat = get_config('tool_licensetrackeradmin', 'keyformat');
    if (empty($keyformat)) {
        $keyformat = json_encode([
            'segments' => 3, // Reduced to 3 since first segment is the prefix
            'segment_length' => 4,
            'separator' => '-',
            'charset' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
        ]);
    }

    $format = json_decode($keyformat, true);
    $charset = $format['charset'];
    $segments = $format['segments'];
    $segmentlength = $format['segment_length'];
    $separator = $format['separator'];

    $maxattempts = 100;
    $attempts = 0;

    do {
        // Start with the course prefix
        $key = $prefix;

        // Add the remaining segments
        for ($i = 0; $i < $segments; $i++) {
            $key .= $separator;
            for ($j = 0; $j < $segmentlength; $j++) {
                $key .= $charset[random_int(0, strlen($charset) - 1)];
            }
        }

        $exists = $DB->record_exists('local_lt_keys', array('keystring' => $key));
        $attempts++;

    } while ($exists && $attempts < $maxattempts);

    if ($exists) {
        throw new moodle_exception('error:duplicatekey', 'local_licensetracker');
    }

    return $key;
}

/**
 * Get all license keys with optional filtering.
 *
 * @param array $filters Optional filters (partner, coursetype, status, search)
 * @param int $limitfrom Starting record
 * @param int $limitnum Number of records
 * @return array Array of key records
 */
function local_licensetracker_get_keys($filters = array(), $limitfrom = 0, $limitnum = 0) {
    global $DB;
    
    $sql = "SELECT k.*, p.partnername, ct.name as coursetypename, u.firstname, u.lastname
            FROM {local_lt_keys} k
            LEFT JOIN {local_lt_partners} p ON k.partnerid = p.id
            LEFT JOIN {local_lt_coursetypes} ct ON k.coursetypeid = ct.id
            LEFT JOIN {user} u ON k.usedbyuserid = u.id
            WHERE 1=1";
    
    $params = array();
    
    if (!empty($filters['partner'])) {
        $sql .= " AND k.partnerid = :partnerid";
        $params['partnerid'] = $filters['partner'];
    }
    
    if (!empty($filters['coursetype'])) {
        $sql .= " AND k.coursetypeid = :coursetypeid";
        $params['coursetypeid'] = $filters['coursetype'];
    }
    
    if (!empty($filters['status'])) {
        $sql .= " AND k.status = :status";
        $params['status'] = $filters['status'];
    }
    
    if (!empty($filters['search'])) {
        $sql .= " AND k.keystring LIKE :search";
        $params['search'] = '%' . $filters['search'] . '%';
    }
    
    $sql .= " ORDER BY k.timecreated DESC";
    
    return $DB->get_records_sql($sql, $params, $limitfrom, $limitnum);
}

/**
 * Update license key status.
 *
 * @param int $keyid Key ID
 * @param string $status New status
 * @param int $userid User ID who used the key (optional)
 * @param string $ipaddress IP address (optional)
 * @param string $useragent User agent (optional)
 * @return bool Success
 */
function local_licensetracker_update_key_status($keyid, $status, $userid = null, $ipaddress = null, $useragent = null) {
    global $DB;
    
    $updatedata = array(
        'id' => $keyid,
        'status' => $status,
        'timemodified' => time()
    );
    
    if ($status === 'used' && $userid) {
        $updatedata['usedbyuserid'] = $userid;
        $updatedata['dateused'] = time();
        $updatedata['ipaddressused'] = $ipaddress;
        $updatedata['useragent'] = $useragent;
    }
    
    return $DB->update_record('local_lt_keys', $updatedata);
}

/**
 * Check if partner has available license keys for a specific course type.
 *
 * @param int $partnerid Partner ID
 * @param int $coursetypeid Course type ID (optional)
 * @return array Array with available key counts by course type
 */
function local_licensetracker_get_partner_stock($partnerid, $coursetypeid = null) {
    global $DB;

    $conditions = array('partnerid' => $partnerid, 'status' => 'available');

    if ($coursetypeid) {
        $conditions['coursetypeid'] = $coursetypeid;
        return $DB->count_records('local_lt_keys', $conditions);
    }

    // Get stock by course type - ONLY for course types assigned to this partner
    $sql = "SELECT ct.id, ct.name, ct.licenseprefix,
                   COUNT(k.id) as available_count
            FROM {local_lt_coursetypes} ct
            JOIN {local_lt_partner_coursetypes} pct ON pct.coursetypeid = ct.id
            LEFT JOIN {local_lt_keys} k ON k.coursetypeid = ct.id
                                        AND k.partnerid = ?
                                        AND k.status = 'available'
            WHERE pct.partnerid = ?
            GROUP BY ct.id, ct.name, ct.licenseprefix
            ORDER BY ct.name";

    return $DB->get_records_sql($sql, array($partnerid, $partnerid));
}

/**
 * Check if partner is assigned to a specific course type.
 *
 * @param int $partnerid Partner ID
 * @param int $coursetypeid Course type ID
 * @return bool True if partner is assigned to course type
 */
function local_licensetracker_partner_has_course_type($partnerid, $coursetypeid) {
    global $DB;

    return $DB->record_exists('local_lt_partner_coursetypes',
                             array('partnerid' => $partnerid, 'coursetypeid' => $coursetypeid));
}

/**
 * Get course types assigned to a partner.
 *
 * @param int $partnerid Partner ID
 * @return array Array of course type records
 */
function local_licensetracker_get_partner_course_types($partnerid) {
    global $DB;

    $sql = "SELECT ct.*, pct.price as partner_price
            FROM {local_lt_coursetypes} ct
            JOIN {local_lt_partner_coursetypes} pct ON pct.coursetypeid = ct.id
            WHERE pct.partnerid = ?
            ORDER BY ct.name";

    return $DB->get_records_sql($sql, array($partnerid));
}

/**
 * Validate that partner can register a user with the given license key.
 *
 * @param int $partnerid Partner ID
 * @param string $keystring License key string
 * @param int $userid User ID attempting to use the key (optional)
 * @return array Array with 'valid' boolean and 'message' string
 */
function local_licensetracker_validate_partner_key_usage($partnerid, $keystring, $userid = null) {
    global $DB, $USER;

    if (!$userid) {
        $userid = $USER->id;
    }

    // Enhanced security checks
    $security_manager = new \local_licensetracker\security\license_security_manager();

    // Check key format first
    $format_check = $security_manager::validate_key_format($keystring);
    if (!$format_check['valid']) {
        $security_manager::log_security_violation($userid, 'invalid_key_format', array(
            'keystring' => $keystring,
            'reason' => $format_check['reason']
        ));
        return array('valid' => false, 'message' => get_string('invalidkeyformat', 'local_licensetracker'));
    }

    // Check rate limiting
    $rate_check = $security_manager::check_rate_limiting($userid, getremoteaddr());
    if (!$rate_check['allowed']) {
        return array('valid' => false, 'message' => get_string('ratelimitexceeded', 'local_licensetracker'));
    }

    // Check partner access to key
    $access_check = $security_manager::check_partner_key_access($partnerid, $keystring, $userid);
    if (!$access_check['allowed']) {
        return array('valid' => false, 'message' => $access_check['reason']);
    }

    // Validate the key exists and is available
    $key = local_licensetracker_validate_key($keystring);

    if (!$key) {
        $security_manager::log_security_violation($userid, 'key_not_found', array(
            'keystring' => $keystring,
            'partnerid' => $partnerid
        ));
        return array('valid' => false, 'message' => get_string('invalidkey', 'local_licensetracker'));
    }

    // Check if key belongs to this partner (redundant but kept for backward compatibility)
    if ($key->partnerid != $partnerid) {
        $security_manager::log_security_violation($userid, 'unauthorized_key_access', array(
            'keystring' => $keystring,
            'attempted_partner' => $partnerid,
            'actual_partner' => $key->partnerid
        ));
        return array('valid' => false, 'message' => get_string('keynotforyourorganization', 'local_licensetracker'));
    }

    // CRITICAL: Check if partner is assigned to this course type
    if (!local_licensetracker_partner_has_course_type($partnerid, $key->coursetypeid)) {
        $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $key->coursetypeid));
        $message = get_string('partnernotassignedtocoursetype', 'local_licensetracker', $coursetype->name);
        return array('valid' => false, 'message' => $message);
    }

    // Check if partner has stock for this course type
    $stock = local_licensetracker_get_partner_stock($partnerid, $key->coursetypeid);
    if ($stock == 0) {
        $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $key->coursetypeid));
        $message = get_string('nostockforcoursetype', 'local_licensetracker', $coursetype->name);
        return array('valid' => false, 'message' => $message);
    }

    return array('valid' => true, 'message' => '', 'key' => $key);
}

/**
 * Set user type profile field for a user.
 *
 * @param int $userid User ID
 * @param string $usertype User type ('partner_student' or 'local_student')
 */
function local_licensetracker_set_user_type($userid, $usertype) {
    global $DB;

    // Get the usertype profile field
    $field = $DB->get_record('user_info_field', array('shortname' => 'usertype'));
    if (!$field) {
        // Create the field if it doesn't exist
        $field = new stdClass();
        $field->shortname = 'usertype';
        $field->name = 'User Type';
        $field->datatype = 'menu';
        $field->description = 'Indicates whether user is a partner student or local student';
        $field->descriptionformat = FORMAT_HTML;
        $field->categoryid = 1;
        $field->sortorder = 1;
        $field->required = 0;
        $field->locked = 1;
        $field->visible = 2;
        $field->forceunique = 0;
        $field->signup = 0;
        $field->defaultdata = 'local_student';
        $field->param1 = "local_student\npartner_student";
        $field->param2 = 'local_student';

        $field->id = $DB->insert_record('user_info_field', $field);
    }

    // Set or update the user's profile field value
    $data = $DB->get_record('user_info_data', array('userid' => $userid, 'fieldid' => $field->id));
    if ($data) {
        $data->data = $usertype;
        $DB->update_record('user_info_data', $data);
    } else {
        $data = new stdClass();
        $data->userid = $userid;
        $data->fieldid = $field->id;
        $data->data = $usertype;
        $DB->insert_record('user_info_data', $data);
    }
}

/**
 * Get user type for a user.
 *
 * @param int $userid User ID
 * @return string User type or 'local_student' as default
 */
function local_licensetracker_get_user_type($userid) {
    global $DB;

    $field = $DB->get_record('user_info_field', array('shortname' => 'usertype'));
    if (!$field) {
        return 'local_student';
    }

    $data = $DB->get_record('user_info_data', array('userid' => $userid, 'fieldid' => $field->id));
    return $data ? $data->data : 'local_student';
}

/**
 * Ensure all course types have valid license prefixes.
 * This function should be called during installation/upgrade.
 */
function local_licensetracker_ensure_course_prefixes() {
    global $DB;

    $coursetypes = $DB->get_records('local_lt_coursetypes');
    foreach ($coursetypes as $coursetype) {
        if (empty($coursetype->licenseprefix)) {
            // Generate a default prefix from the course name.
            $prefix = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $coursetype->name), 0, 5));
            if (empty($prefix) || strlen($prefix) < 2) {
                $prefix = 'COURSE';
            }

            // Ensure uniqueness.
            $counter = 1;
            $originalprefix = $prefix;
            while ($DB->record_exists('local_lt_coursetypes', array('licenseprefix' => $prefix))) {
                if ($counter > 99) {
                    // Fallback to random if too many conflicts
                    $prefix = 'CT' . random_int(100, 999);
                    break;
                }
                $prefix = $originalprefix . $counter;
                $counter++;
            }

            $DB->set_field('local_lt_coursetypes', 'licenseprefix', $prefix, array('id' => $coursetype->id));
        }
    }
}

/**
 * Get partner statistics.
 *
 * @param int $partnerid Partner ID (optional, for specific partner stats)
 * @return object Statistics object
 */
function local_licensetracker_get_partner_stats($partnerid = null) {
    global $DB;
    
    $sql = "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available,
                SUM(CASE WHEN status = 'used' THEN 1 ELSE 0 END) as used,
                SUM(CASE WHEN status = 'revoked' THEN 1 ELSE 0 END) as revoked
            FROM {local_lt_keys}";
    
    $params = array();
    
    if ($partnerid) {
        $sql .= " WHERE partnerid = :partnerid";
        $params['partnerid'] = $partnerid;
    }
    
    return $DB->get_record_sql($sql, $params);
}

/**
 * Validate a license key.
 *
 * @param string $keystring The license key string
 * @param int $coursetypeid Optional course type ID to validate against
 * @return object|false Key record if valid, false otherwise
 */
function local_licensetracker_validate_key($keystring, $coursetypeid = null) {
    global $DB;

    // Check if key exists and is available
    $key = $DB->get_record('local_lt_keys', array('keystring' => $keystring, 'status' => 'available'));

    if (!$key) {
        return false;
    }

    // If course type is specified, validate that the key is for the correct course type
    if ($coursetypeid !== null && $key->coursetypeid != $coursetypeid) {
        return false;
    }

    $now = time();

    // Check validfrom date
    if ($key->validfrom && $now < $key->validfrom) {
        return false;
    }

    // Check expireson date
    if ($key->expireson && $now > $key->expireson) {
        return false;
    }

    return $key;
}

/**
 * Validate that a license key can be used for a specific course type.
 *
 * @param string $keystring The license key string
 * @param int $coursetypeid The course type ID
 * @return array Array with 'valid' boolean and 'message' string
 */
function local_licensetracker_validate_key_for_course($keystring, $coursetypeid) {
    global $DB;

    // Get the course type to check the prefix
    $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $coursetypeid));
    if (!$coursetype) {
        return array('valid' => false, 'message' => get_string('error:coursetypenotfound', 'local_licensetracker'));
    }

    // Check if the key starts with the correct course prefix
    if (!empty($coursetype->licenseprefix)) {
        $expectedprefix = $coursetype->licenseprefix . '-';
        if (strpos($keystring, $expectedprefix) !== 0) {
            $message = get_string('wrongcourseprefix', 'local_licensetracker',
                array('prefix' => $coursetype->licenseprefix, 'coursename' => $coursetype->name));
            return array('valid' => false, 'message' => $message);
        }
    }

    $key = local_licensetracker_validate_key($keystring);

    if (!$key) {
        return array('valid' => false, 'message' => get_string('invalidkey', 'local_licensetracker'));
    }

    if ($key->coursetypeid != $coursetypeid) {
        $keycourse = $DB->get_record('local_lt_coursetypes', array('id' => $key->coursetypeid));

        $message = get_string('wrongcoursetype', 'local_licensetracker',
            array('keytype' => $keycourse->name, 'requiredtype' => $coursetype->name));

        return array('valid' => false, 'message' => $message);
    }

    return array('valid' => true, 'message' => '');
}

/**
 * Get all partners.
 *
 * @return array Array of partner records
 */
function local_licensetracker_get_partners() {
    global $DB;
    
    $sql = "SELECT p.*, u.firstname, u.lastname, u.email as useremail
            FROM {local_lt_partners} p
            LEFT JOIN {user} u ON p.moodleuserid = u.id
            ORDER BY p.partnername";
    
    return $DB->get_records_sql($sql);
}

/**
 * Get all course types.
 *
 * @return array Array of course type records
 */
function local_licensetracker_get_coursetypes() {
    global $DB;
    
    $sql = "SELECT ct.*, c.fullname as coursename, r.shortname as rolename
            FROM {local_lt_coursetypes} ct
            LEFT JOIN {course} c ON ct.moodlecourseid = c.id
            LEFT JOIN {role} r ON ct.defaultroleid = r.id
            ORDER BY ct.name";
    
    return $DB->get_records_sql($sql);
}

/**
 * Classify user type for license requirements.
 *
 * @param object $user User object
 * @return string 'local', 'global', or 'admin'
 */
function local_licensetracker_classify_user_type($user) {
    global $DB;

    // Check if user is admin/staff
    if (is_siteadmin($user->id)) {
        return 'admin';
    }

    // Check if user has manager role in system context
    $systemcontext = context_system::instance();
    if (has_capability('moodle/site:config', $systemcontext, $user->id)) {
        return 'admin';
    }

    $config = get_config('auth_licensetracker');

    // If require key for all is enabled, all non-admins are global
    if (!empty($config->requirekeyforall)) {
        return 'global';
    }

    // Check country-based classification
    if (!empty($config->localcountries)) {
        $localcountries = array_map('trim', explode(',', strtoupper($config->localcountries)));
        $usercountry = strtoupper($user->country);

        if (in_array($usercountry, $localcountries)) {
            return 'local';
        }
    }

    return 'global';
}

/**
 * Get user type statistics.
 *
 * @return object Statistics object
 */
function local_licensetracker_get_user_type_stats() {
    global $DB;

    $sql = "SELECT
                COUNT(*) as total_users,
                SUM(CASE WHEN auth = 'licensetracker' THEN 1 ELSE 0 END) as license_users,
                SUM(CASE WHEN auth = 'manual' THEN 1 ELSE 0 END) as manual_users,
                SUM(CASE WHEN deleted = 1 THEN 1 ELSE 0 END) as deleted_users
            FROM {user}
            WHERE id > 1"; // Exclude guest user

    return $DB->get_record_sql($sql);
}

/**
 * Send license request email to administrators.
 *
 * @param object $partner Partner object
 * @param object $coursetype Course type object
 * @param object $requestdata Request form data
 * @return bool Success
 */
function local_licensetracker_send_license_request_email($partner, $coursetype, $requestdata) {
    global $DB, $CFG;

    // Get all administrators with license management capabilities
    $context = context_system::instance();
    $admins = get_users_by_capability($context, 'local/licensetracker:manageallkeys', 'u.*', 'u.lastname, u.firstname');

    if (empty($admins)) {
        // Fallback to site administrators
        $admins = get_admins();
    }

    // Calculate total cost
    $partnerprice = $DB->get_field_sql(
        "SELECT COALESCE(pct.price, ct.defaultprice)
         FROM {local_lt_coursetypes} ct
         LEFT JOIN {local_lt_partner_coursetypes} pct ON ct.id = pct.coursetypeid AND pct.partnerid = ?
         WHERE ct.id = ?",
        array($partner->id, $requestdata->coursetypeid)
    );
    $totalcost = $partnerprice * $requestdata->quantity;

    // Prepare email content
    $subject = get_string('licenserequest_subject', 'local_licensetracker', array(
        'partnername' => $partner->partnername,
        'quantity' => $requestdata->quantity,
        'coursetype' => $coursetype->name
    ));

    $message = get_string('licenserequest_message', 'local_licensetracker', array(
        'partnername' => $partner->partnername,
        'contactemail' => $partner->contactemail,
        'coursetype' => $coursetype->name,
        'quantity' => $requestdata->quantity,
        'urgency' => get_string('urgency_' . $requestdata->urgency, 'local_licensetracker'),
        'unitprice' => number_format($partnerprice, 2),
        'totalcost' => number_format($totalcost, 2),
        'notes' => $requestdata->notes ?: get_string('nonotes', 'local_licensetracker'),
        'adminurl' => $CFG->wwwroot . '/local/licensetracker/index.php?tab=partners'
    ));

    $success = true;
    foreach ($admins as $admin) {
        try {
            $messagedata = new \core\message\message();
            $messagedata->component = 'local_licensetracker';
            $messagedata->name = 'licenserequest';
            $messagedata->userfrom = \core_user::get_noreply_user();
            $messagedata->userto = $admin;
            $messagedata->subject = $subject;
            $messagedata->fullmessage = $message;
            $messagedata->fullmessageformat = FORMAT_PLAIN;
            $messagedata->fullmessagehtml = nl2br($message);
            $messagedata->smallmessage = $subject;

            message_send($messagedata);
        } catch (Exception $e) {
            $success = false;
            debugging('Failed to send license request email to admin ' . $admin->id . ': ' . $e->getMessage());
        }
    }

    return $success;
}

/**
 * Send low stock notification to administrators.
 *
 * @param object $partner Partner object
 * @param array $lowstockdata Low stock data
 * @return bool Success
 */
function local_licensetracker_send_low_stock_notification($partner, $lowstockdata) {
    global $DB, $CFG;

    // Check if notifications are enabled
    if (!get_config('local_licensetracker', 'email_notifications')) {
        return true;
    }

    // Get all administrators with license management capabilities
    $context = context_system::instance();
    $admins = get_users_by_capability($context, 'local/licensetracker:manageallkeys', 'u.*', 'u.lastname, u.firstname');

    if (empty($admins)) {
        $admins = get_admins();
    }

    $subject = get_string('lowstock_subject', 'local_licensetracker', array(
        'partnername' => $partner->partnername
    ));

    $message = get_string('lowstock_message', 'local_licensetracker', array(
        'partnername' => $partner->partnername,
        'contactemail' => $partner->contactemail,
        'lowstockdata' => $lowstockdata,
        'adminurl' => $CFG->wwwroot . '/local/licensetracker/index.php?tab=partners'
    ));

    $success = true;
    foreach ($admins as $admin) {
        try {
            $messagedata = new \core\message\message();
            $messagedata->component = 'local_licensetracker';
            $messagedata->name = 'lowstock';
            $messagedata->userfrom = \core_user::get_noreply_user();
            $messagedata->userto = $admin;
            $messagedata->subject = $subject;
            $messagedata->fullmessage = $message;
            $messagedata->fullmessageformat = FORMAT_PLAIN;
            $messagedata->fullmessagehtml = nl2br($message);
            $messagedata->smallmessage = $subject;

            message_send($messagedata);
        } catch (Exception $e) {
            $success = false;
            debugging('Failed to send low stock notification to admin ' . $admin->id . ': ' . $e->getMessage());
        }
    }

    return $success;
}

/**
 * Send key expiry notification to administrators.
 *
 * @param array $expiringkeys Expiring keys data
 * @return bool Success
 */
function local_licensetracker_send_key_expiry_notification($expiringkeys) {
    global $DB, $CFG;

    // Check if notifications are enabled
    if (!get_config('local_licensetracker', 'email_notifications')) {
        return true;
    }

    // Get all administrators with license management capabilities
    $context = context_system::instance();
    $admins = get_users_by_capability($context, 'local/licensetracker:manageallkeys', 'u.*', 'u.lastname, u.firstname');

    if (empty($admins)) {
        $admins = get_admins();
    }

    $subject = get_string('keyexpiry_subject', 'local_licensetracker', array(
        'count' => count($expiringkeys)
    ));

    $message = get_string('keyexpiry_message', 'local_licensetracker', array(
        'count' => count($expiringkeys),
        'expiringkeys' => $expiringkeys,
        'adminurl' => $CFG->wwwroot . '/local/licensetracker/index.php?tab=keys'
    ));

    $success = true;
    foreach ($admins as $admin) {
        try {
            $messagedata = new \core\message\message();
            $messagedata->component = 'local_licensetracker';
            $messagedata->name = 'keyexpiry';
            $messagedata->userfrom = \core_user::get_noreply_user();
            $messagedata->userto = $admin;
            $messagedata->subject = $subject;
            $messagedata->fullmessage = $message;
            $messagedata->fullmessageformat = FORMAT_PLAIN;
            $messagedata->fullmessagehtml = nl2br($message);
            $messagedata->smallmessage = $subject;

            message_send($messagedata);
        } catch (Exception $e) {
            $success = false;
            debugging('Failed to send key expiry notification to admin ' . $admin->id . ': ' . $e->getMessage());
        }
    }

    return $success;
}

/**
 * Send security alert notification to administrators.
 *
 * @param string $alerttype Type of security alert
 * @param array $alertdata Alert data
 * @return bool Success
 */
function local_licensetracker_send_security_alert($alerttype, $alertdata) {
    global $DB, $CFG;

    // Check if notifications are enabled
    if (!get_config('local_licensetracker', 'email_notifications')) {
        return true;
    }

    // Get all administrators with license management capabilities
    $context = context_system::instance();
    $admins = get_users_by_capability($context, 'local/licensetracker:manageallkeys', 'u.*', 'u.lastname, u.firstname');

    if (empty($admins)) {
        $admins = get_admins();
    }

    $subject = get_string('securityalert_subject', 'local_licensetracker', array(
        'alerttype' => $alerttype
    ));

    $message = get_string('securityalert_message', 'local_licensetracker', array(
        'alerttype' => $alerttype,
        'alertdata' => $alertdata,
        'adminurl' => $CFG->wwwroot . '/local/licensetracker/index.php?tab=security'
    ));

    $success = true;
    foreach ($admins as $admin) {
        try {
            $messagedata = new \core\message\message();
            $messagedata->component = 'local_licensetracker';
            $messagedata->name = 'securityalert';
            $messagedata->userfrom = \core_user::get_noreply_user();
            $messagedata->userto = $admin;
            $messagedata->subject = $subject;
            $messagedata->fullmessage = $message;
            $messagedata->fullmessageformat = FORMAT_PLAIN;
            $messagedata->fullmessagehtml = nl2br($message);
            $messagedata->smallmessage = $subject;

            message_send($messagedata);
        } catch (Exception $e) {
            $success = false;
            debugging('Failed to send security alert to admin ' . $admin->id . ': ' . $e->getMessage());
        }
    }

    return $success;
}
