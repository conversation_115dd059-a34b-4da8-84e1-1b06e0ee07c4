<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Authentication plugin for license tracker.
 *
 * @package    auth_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir.'/authlib.php');
require_once($CFG->dirroot.'/local/licensetracker/lib.php');

/**
 * License tracker authentication plugin.
 */
class auth_plugin_licensetracker extends auth_plugin_base {

    /**
     * Constructor.
     */
    public function __construct() {
        $this->authtype = 'licensetracker';
        $this->config = get_config('auth_licensetracker');
    }



    /**
     * Returns true if the username and password work and false if they are
     * wrong or don't exist.
     *
     * @param string $username The username
     * @param string $password The password
     * @return bool Authentication success or failure.
     */
    public function user_login($username, $password) {
        global $DB;

        // For users created through license tracker, use manual auth for login
        $user = $DB->get_record('user', array('username' => $username, 'deleted' => 0));
        if ($user && $user->auth === 'licensetracker') {
            // Change auth to manual after first successful registration
            $user->auth = 'manual';
            $DB->update_record('user', $user);
        }

        return false; // Let manual auth handle the actual login
    }

    /**
     * Returns true if this authentication plugin is 'internal'.
     *
     * @return bool
     */
    public function is_internal() {
        return true; // Changed to true to handle internal registration
    }

    /**
     * Returns true if this authentication plugin can change the user's
     * password.
     *
     * @return bool
     */
    public function can_change_password() {
        return false;
    }

    /**
     * Returns the URL for changing the user's pw, or empty if the default can
     * be used.
     *
     * @return moodle_url
     */
    public function change_password_url() {
        return null;
    }

    /**
     * Returns true if plugin allows resetting of internal password.
     *
     * @return bool
     */
    public function can_reset_password() {
        return false;
    }

    /**
     * Returns true if plugin can be manually set.
     *
     * @return bool
     */
    public function can_be_manually_set() {
        return false;
    }

    /**
     * Returns true if plugin allows signup.
     * For staff-only registration, this should always return false.
     *
     * @return bool
     */
    public function can_signup() {
        // Staff-only registration - no self-signup allowed
        return false;
    }

    /**
     * Prints a form for configuring this authentication plugin.
     *
     * This function is called from admin/auth.php, and outputs a full page with
     * a form for configuring this plugin.
     *
     * @param array $page An object containing all the data for this page.
     */
    public function config_form($config, $err, $user_fields) {
        include 'config.html';
    }

    /**
     * Processes and stores configuration data for this authentication plugin.
     */
    public function process_config($config) {
        // Set to defaults if undefined
        if (!isset($config->enabled)) {
            $config->enabled = 0;
        }
        if (!isset($config->requirekeyforall)) {
            $config->requirekeyforall = 1;
        }
        if (!isset($config->localcountries)) {
            $config->localcountries = '';
        }

        // Save settings
        set_config('enabled', $config->enabled, 'auth_licensetracker');
        set_config('requirekeyforall', $config->requirekeyforall, 'auth_licensetracker');
        set_config('localcountries', $config->localcountries, 'auth_licensetracker');

        return true;
    }

    /**
     * Add license key field to signup form.
     *
     * @param moodleform $mform
     */
    public function signup_form(&$mform) {
        global $CFG;

        // Always add the field, but make it conditional based on country selection
        $mform->addElement('text', 'licensekey', get_string('licensekey', 'auth_licensetracker'));
        $mform->setType('licensekey', PARAM_TEXT);
        $mform->addHelpButton('licensekey', 'licensekey', 'auth_licensetracker');

        // Add JavaScript to show/hide license key field based on country selection
        $this->add_license_key_javascript($mform);
    }

    /**
     * Validate signup form data.
     *
     * @param array $data
     * @param array $files
     * @return array
     */
    public function signup_form_validation($data, $files) {
        global $SESSION;

        $errors = array();

        // Check if license key is required
        if (!$this->is_license_key_required($data)) {
            return $errors;
        }

        // Validate license key
        if (empty($data['licensekey'])) {
            $errors['licensekey'] = get_string('licensekey_required', 'auth_licensetracker');
            return $errors;
        }

        // Validate key format and availability
        $key = local_licensetracker_validate_key($data['licensekey']);
        if (!$key) {
            $errors['licensekey'] = get_string('invalidkey', 'auth_licensetracker');
        } else {
            // Store validated key in session for use in observer
            $SESSION->licensetracker_validated_key = $key->id;
        }

        return $errors;
    }

    /**
     * Handle user signup.
     *
     * @param object $user
     * @param boolean $notify
     * @return boolean
     */
    public function user_signup($user, $notify = true) {
        global $CFG, $DB;

        // Check if license key is required
        if (!$this->is_license_key_required()) {
            return true;
        }

        // Validate license key again
        if (empty($user->licensekey)) {
            throw new auth_signup_exception(get_string('registrationrequireskey', 'auth_licensetracker'));
        }

        $key = local_licensetracker_validate_key($user->licensekey);
        if (!$key) {
            throw new auth_signup_exception(get_string('invalidkey', 'auth_licensetracker'));
        }

        // Create the user account first
        $user->confirmed = 1;
        $user->auth = 'manual'; // Set to manual auth after registration
        $user->mnethostid = $CFG->mnet_localhost_id;
        
        $userid = user_create_user($user, false, false);
        
        if (!$userid) {
            throw new auth_signup_exception('Failed to create user account');
        }

        // Update license key status
        $ipaddress = getremoteaddr();
        $useragent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        local_licensetracker_update_key_status($key->id, 'used', $userid, $ipaddress, $useragent);

        // Get course and enrollment information
        $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $key->coursetypeid));
        if (!$coursetype) {
            throw new auth_signup_exception(get_string('error:coursenotfound', 'auth_licensetracker'));
        }

        // Enroll user in course
        $this->enroll_user_in_course($userid, $coursetype);

        // Set partner language preference if available
        $this->set_partner_language_preference($userid, $key->partnerid);

        return true;
    }

    /**
     * Check if license key is required for registration.
     *
     * @param array $data Form data (optional)
     * @return bool
     */
    private function is_license_key_required($data = null) {
        // Check if auth plugin is enabled
        if (empty($this->config->enabled)) {
            return false;
        }

        // CRITICAL: Check if system is in partner-only mode
        if (get_config('local_licensetracker', 'partner_only_registration')) {
            return true; // All registration requires license keys
        }

        // If require key for all is enabled, always require key
        if (!empty($this->config->requirekeyforall)) {
            return true;
        }

        // Check if user's country requires key (local vs global logic)
        if (!empty($this->config->localcountries) && $data && !empty($data['country'])) {
            $localcountries = array_map('trim', explode(',', $this->config->localcountries));
            if (in_array($data['country'], $localcountries)) {
                return false; // Local student, no key required
            }
        }

        return true; // Global student, key required by default
    }

    /**
     * Check if self-registration should be completely disabled.
     *
     * @return bool True if self-registration should be disabled
     */
    public function is_self_registration_disabled() {
        // If partner-only mode is enabled, disable self-registration
        return get_config('local_licensetracker', 'partner_only_registration');
    }

    /**
     * Add JavaScript to dynamically show/hide license key field.
     *
     * @param moodleform $mform
     */
    private function add_license_key_javascript(&$mform) {
        global $PAGE;

        // Only add if not requiring key for all students
        if (!empty($this->config->requirekeyforall)) {
            // Always required, make it required field
            $mform->addRule('licensekey', get_string('licensekey_required', 'auth_licensetracker'), 'required', null, 'client');
            return;
        }

        $localcountries = array();
        if (!empty($this->config->localcountries)) {
            $localcountries = array_map('trim', explode(',', $this->config->localcountries));
        }

        $js = "
        require(['jquery'], function($) {
            function toggleLicenseKey() {
                var country = $('#id_country').val();
                var localCountries = " . json_encode($localcountries) . ";
                var licenseRow = $('#fitem_id_licensekey');

                if (localCountries.indexOf(country) !== -1) {
                    // Local student - hide license key field
                    licenseRow.hide();
                    $('#id_licensekey').removeAttr('required');
                } else {
                    // Global student - show and require license key field
                    licenseRow.show();
                    $('#id_licensekey').attr('required', 'required');
                }
            }

            // Run on page load
            $(document).ready(function() {
                toggleLicenseKey();
                $('#id_country').change(toggleLicenseKey);
            });
        });
        ";

        $PAGE->requires->js_init_code($js);
    }

    /**
     * Enroll user in course based on course type settings.
     *
     * @param int $userid
     * @param object $coursetype
     */
    private function enroll_user_in_course($userid, $coursetype) {
        global $DB;

        $course = $DB->get_record('course', array('id' => $coursetype->moodlecourseid));
        if (!$course) {
            throw new auth_signup_exception(get_string('error:coursenotfound', 'auth_licensetracker'));
        }

        $enrolmethod = $coursetype->enrolmethod ?: 'manual';
        $roleid = $coursetype->defaultroleid ?: 5; // Default to student role

        // Get enrollment plugin
        $enrol = enrol_get_plugin($enrolmethod);
        if (!$enrol) {
            $enrol = enrol_get_plugin('manual'); // Fallback to manual
            $enrolmethod = 'manual';
        }

        // Get or create enrollment instance
        $instance = $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => $enrolmethod));
        if (!$instance) {
            // Create new instance if it doesn't exist
            $instanceid = $enrol->add_instance($course);
            $instance = $DB->get_record('enrol', array('id' => $instanceid));
        }

        // Enroll the user
        $enrol->enrol_user($instance, $userid, $roleid, time(), 0, ENROL_USER_ACTIVE);

        // Add success message
        $coursename = format_string($course->fullname);
        \core\notification::success(get_string('enrollmentsuccess', 'auth_licensetracker', $coursename));
    }

    /**
     * Set partner language preference for new user.
     *
     * @param int $userid
     * @param int $partnerid
     */
    private function set_partner_language_preference($userid, $partnerid) {
        global $DB;

        try {
            $partner = $DB->get_record('local_lt_partners', array('id' => $partnerid));
            if ($partner && !empty($partner->lang)) {
                $user = new stdClass();
                $user->id = $userid;
                $user->lang = $partner->lang;
                $DB->update_record('user', $user);
            }
        } catch (Exception $e) {
            // Log error but don't fail registration
            debugging('Failed to set partner language: ' . $e->getMessage());
        }
    }
}
