<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Partner form for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\form;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/formslib.php');

/**
 * Form for adding/editing partners.
 */
class partner_form extends \moodleform {

    /**
     * Define the form.
     */
    public function definition() {
        global $DB;

        $mform = $this->_form;

        // Hidden ID field for editing
        $mform->addElement('hidden', 'id');
        $mform->setType('id', PARAM_INT);

        // Partner name
        $mform->addElement('text', 'partnername', get_string('partnername', 'local_licensetracker'));
        $mform->setType('partnername', PARAM_TEXT);
        $mform->addRule('partnername', null, 'required', null, 'client');
        $mform->addRule('partnername', null, 'maxlength', 255, 'client');

        // Contact email
        $mform->addElement('text', 'contactemail', get_string('contactemail', 'local_licensetracker'));
        $mform->setType('contactemail', PARAM_EMAIL);
        $mform->addRule('contactemail', null, 'required', null, 'client');
        $mform->addRule('contactemail', null, 'email', null, 'client');

        // Country (optional)
        $countries = get_string_manager()->get_list_of_countries();
        $countries = array('' => get_string('selectacountry')) + $countries;
        $mform->addElement('select', 'country', get_string('country', 'local_licensetracker'), $countries);

        // Language (optional)
        $languages = get_string_manager()->get_list_of_translations();
        $languages = array('' => get_string('default')) + $languages;
        $mform->addElement('select', 'lang', get_string('language', 'local_licensetracker'), $languages);

        // Moodle user selection
        $sql = "SELECT u.id, CONCAT(u.firstname, ' ', u.lastname, ' (', u.email, ')') as fullname
                FROM {user} u
                WHERE u.deleted = 0 AND u.confirmed = 1 AND u.id > 1
                ORDER BY u.lastname, u.firstname";
        $users = $DB->get_records_sql_menu($sql);
        $users = array('' => get_string('selectauser', 'core')) + $users;
        $mform->addElement('select', 'moodleuserid', get_string('moodleuser', 'local_licensetracker'), $users);

        // Action buttons
        $this->add_action_buttons(true, get_string('save', 'local_licensetracker'));
    }

    /**
     * Validate the form data.
     *
     * @param array $data Form data
     * @param array $files Form files
     * @return array Validation errors
     */
    public function validation($data, $files) {
        global $DB;
        
        $errors = parent::validation($data, $files);

        // Check for duplicate partner name
        if (!empty($data['partnername'])) {
            $conditions = array('partnername' => $data['partnername']);
            if (!empty($data['id'])) {
                $sql = "SELECT id FROM {local_lt_partners} WHERE partnername = ? AND id != ?";
                $params = array($data['partnername'], $data['id']);
                $exists = $DB->get_record_sql($sql, $params);
            } else {
                $exists = $DB->record_exists('local_lt_partners', $conditions);
            }
            
            if ($exists) {
                $errors['partnername'] = 'Partner name already exists';
            }
        }

        // Check if Moodle user is already assigned to another partner
        if (!empty($data['moodleuserid'])) {
            $conditions = array('moodleuserid' => $data['moodleuserid']);
            if (!empty($data['id'])) {
                $sql = "SELECT id FROM {local_lt_partners} WHERE moodleuserid = ? AND id != ?";
                $params = array($data['moodleuserid'], $data['id']);
                $exists = $DB->get_record_sql($sql, $params);
            } else {
                $exists = $DB->record_exists('local_lt_partners', $conditions);
            }
            
            if ($exists) {
                $errors['moodleuserid'] = 'This user is already assigned to another partner';
            }
        }

        return $errors;
    }
}
