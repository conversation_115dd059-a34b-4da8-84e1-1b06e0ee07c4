<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Partner dashboard renderable for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\output;

defined('MOODLE_INTERNAL') || die();

use renderable;
use renderer_base;
use templatable;

/**
 * Partner dashboard renderable class.
 */
class partner_dashboard implements renderable, templatable {

    /** @var string $activetab Currently active tab */
    protected $activetab;

    /** @var array $content Tab content */
    protected $content;

    /** @var object $partner Partner information */
    protected $partner;

    /**
     * Constructor.
     *
     * @param string $activetab Currently active tab
     * @param array $content Tab content
     * @param object $partner Partner information
     */
    public function __construct($activetab = 'assignedkeys', $content = array(), $partner = null) {
        $this->activetab = $activetab;
        $this->content = $content;
        $this->partner = $partner;
    }

    /**
     * Export data for template.
     *
     * @param renderer_base $output
     * @return array
     */
    public function export_for_template(renderer_base $output) {
        global $CFG;

        $tabs = array(
            'assignedkeys' => array(
                'name' => get_string('assignedkeys', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'assignedkeys')),
                'active' => ($this->activetab === 'assignedkeys')
            ),
            'statistics' => array(
                'name' => get_string('statistics', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'statistics')),
                'active' => ($this->activetab === 'statistics')
            ),
            'register' => array(
                'name' => get_string('registernewstudent', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'register')),
                'active' => ($this->activetab === 'register')
            ),
            'bulkregister' => array(
                'name' => get_string('bulkregisterstudents', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'bulkregister')),
                'active' => ($this->activetab === 'bulkregister')
            ),
            'purchase' => array(
                'name' => get_string('purchase', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'purchase')),
                'active' => ($this->activetab === 'purchase')
            )
        );

        $data = array(
            'title' => get_string('dashboard', 'local_licensetracker'),
            'partnername' => $this->partner ? $this->partner->partnername : '',
            'tabs' => array_values($tabs),
            'activetab' => $this->activetab,
            'content' => $this->content,
            'wwwroot' => $CFG->wwwroot
        );

        return $data;
    }
}
