<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Admin overview tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

// CRITICAL: Verify admin permissions
$context = context_system::instance();
require_capability('local/licensetracker:manageallkeys', $context);

$output = '';

// System Health Summary
$output .= '<div class="alert alert-info mb-4">';
$output .= '<h4><i class="fa fa-dashboard"></i> System Health Summary</h4>';
$output .= '<div class="row">';

// Check for critical issues
$criticalissues = 0;
$warnings = 0;

// Check for partners with no stock
$nostockpartners = $DB->count_records_sql(
    "SELECT COUNT(DISTINCT p.id)
     FROM {local_lt_partners} p
     LEFT JOIN {local_lt_keys} k ON k.partnerid = p.id AND k.status = 'available'
     GROUP BY p.id
     HAVING COUNT(k.id) = 0"
);

// Check for course types without prefixes
$noprefixcourses = $DB->count_records('local_lt_coursetypes', array('licenseprefix' => ''));

if ($nostockpartners > 0) {
    $criticalissues++;
}
if ($noprefixcourses > 0) {
    $warnings++;
}

$output .= '<div class="col-md-4">';
if ($criticalissues == 0) {
    $output .= '<div class="text-success"><i class="fa fa-check-circle"></i> <strong>System Healthy</strong></div>';
} else {
    $output .= '<div class="text-danger"><i class="fa fa-exclamation-triangle"></i> <strong>' . $criticalissues . ' Critical Issues</strong></div>';
}
$output .= '</div>';

$output .= '<div class="col-md-4">';
if ($warnings == 0) {
    $output .= '<div class="text-success"><i class="fa fa-info-circle"></i> No Warnings</div>';
} else {
    $output .= '<div class="text-warning"><i class="fa fa-warning"></i> ' . $warnings . ' Warnings</div>';
}
$output .= '</div>';

$output .= '<div class="col-md-4">';
$output .= '<a href="' . $CFG->wwwroot . '/local/licensetracker/system_validation.php" class="btn btn-sm btn-secondary">';
$output .= '<i class="fa fa-cog"></i> Run System Validation';
$output .= '</a>';
$output .= '</div>';

$output .= '</div>';
$output .= '</div>';

// System Overview Cards
$output .= '<div class="row mb-4">';

// Total Partners
$totalpartners = $DB->count_records('local_lt_partners');
$output .= '<div class="col-md-3">';
$output .= '<div class="card bg-primary text-white">';
$output .= '<div class="card-body">';
$output .= '<h4>' . $totalpartners . '</h4>';
$output .= '<p>Total Partners</p>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

// Total License Keys
$totalkeys = $DB->count_records('local_lt_keys');
$output .= '<div class="col-md-3">';
$output .= '<div class="card bg-success text-white">';
$output .= '<div class="card-body">';
$output .= '<h4>' . $totalkeys . '</h4>';
$output .= '<p>Total License Keys</p>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

// Used Keys
$usedkeys = $DB->count_records('local_lt_keys', array('status' => 'used'));
$output .= '<div class="col-md-3">';
$output .= '<div class="card bg-warning text-white">';
$output .= '<div class="card-body">';
$output .= '<h4>' . $usedkeys . '</h4>';
$output .= '<p>Used Keys</p>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

// Available Keys
$availablekeys = $DB->count_records('local_lt_keys', array('status' => 'available'));
$output .= '<div class="col-md-3">';
$output .= '<div class="card bg-info text-white">';
$output .= '<div class="card-body">';
$output .= '<h4>' . $availablekeys . '</h4>';
$output .= '<p>Available Keys</p>';
$output .= '</div>';
$output .= '</div>';
$output .= '</div>';

$output .= '</div>';

// Partner Overview Table
$output .= '<div class="card">';
$output .= '<div class="card-header">';
$output .= '<h4>Partner Overview</h4>';
$output .= '</div>';
$output .= '<div class="card-body">';

// Get comprehensive partner statistics
$sql = "SELECT p.id, p.name, p.contactemail, p.timecreated,
               COUNT(DISTINCT k.id) as total_keys,
               COUNT(DISTINCT CASE WHEN k.status = 'available' THEN k.id END) as available_keys,
               COUNT(DISTINCT CASE WHEN k.status = 'used' THEN k.id END) as used_keys,
               COUNT(DISTINCT CASE WHEN k.status = 'revoked' THEN k.id END) as revoked_keys,
               COUNT(DISTINCT u.id) as registered_students,
               COUNT(DISTINCT ct.id) as course_types_assigned,
               MAX(k.timeused) as last_activity
        FROM {local_lt_partners} p
        LEFT JOIN {local_lt_keys} k ON k.partnerid = p.id
        LEFT JOIN {user} u ON u.id = k.usedby AND u.deleted = 0
        LEFT JOIN {local_lt_coursetypes} ct ON ct.id = k.coursetypeid
        GROUP BY p.id, p.name, p.contactemail, p.timecreated
        ORDER BY p.name";

$partners = $DB->get_records_sql($sql);

if ($partners) {
    $output .= '<div class="table-responsive">';
    $output .= '<table class="table table-striped">';
    $output .= '<thead>';
    $output .= '<tr>';
    $output .= '<th>Partner Name</th>';
    $output .= '<th>Contact Email</th>';
    $output .= '<th>Course Types</th>';
    $output .= '<th>Total Keys</th>';
    $output .= '<th>Available</th>';
    $output .= '<th>Used</th>';
    $output .= '<th>Students</th>';
    $output .= '<th>Last Activity</th>';
    $output .= '<th>Actions</th>';
    $output .= '</tr>';
    $output .= '</thead>';
    $output .= '<tbody>';
    
    foreach ($partners as $partner) {
        // Calculate usage percentage
        $usagerate = $partner->total_keys > 0 ?
            round(($partner->used_keys / $partner->total_keys) * 100, 1) : 0;

        // Determine status color based on activity
        $statusclass = 'text-success';
        $statustext = 'Active';

        if ($partner->available_keys == 0 && $partner->total_keys > 0) {
            $statusclass = 'text-danger';
            $statustext = 'Out of Stock';
        } else if ($partner->available_keys <= 5 && $partner->available_keys > 0) {
            $statusclass = 'text-warning';
            $statustext = 'Low Stock';
        } else if ($partner->total_keys == 0) {
            $statusclass = 'text-muted';
            $statustext = 'No Keys';
        }

        $output .= '<tr>';
        $output .= '<td>';
        $output .= '<strong>' . $partner->name . '</strong><br>';
        $output .= '<small class="' . $statusclass . '">' . $statustext . '</small>';
        $output .= '</td>';
        $output .= '<td>' . $partner->contactemail . '</td>';
        $output .= '<td><span class="badge badge-primary">' . $partner->course_types_assigned . '</span></td>';
        $output .= '<td><span class="badge badge-secondary">' . $partner->total_keys . '</span></td>';
        $output .= '<td><span class="badge badge-success">' . $partner->available_keys . '</span></td>';
        $output .= '<td>';
        $output .= '<span class="badge badge-warning">' . $partner->used_keys . '</span>';
        if ($partner->total_keys > 0) {
            $output .= '<br><small class="text-muted">' . $usagerate . '% used</small>';
        }
        $output .= '</td>';
        $output .= '<td><span class="badge badge-info">' . $partner->registered_students . '</span></td>';
        $output .= '<td>';
        if ($partner->last_activity) {
            $output .= '<small>' . userdate($partner->last_activity, '%d %b %Y') . '</small>';
        } else {
            $output .= '<small class="text-muted">No activity</small>';
        }
        $output .= '</td>';
        $output .= '<td>';
        $output .= '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php?tab=partners&action=edit&id=' . $partner->id . '" class="btn btn-sm btn-primary">Edit</a><br>';
        $output .= '<a href="' . $CFG->wwwroot . '/local/licensetracker/partner_details.php?id=' . $partner->id . '" class="btn btn-sm btn-info mt-1">Details</a>';
        $output .= '</td>';
        $output .= '</tr>';
    }
    
    $output .= '</tbody>';
    $output .= '</table>';
    $output .= '</div>';
} else {
    $output .= '<p class="text-muted">No partners found.</p>';
}

$output .= '</div>';
$output .= '</div>';

// Course Type Usage Statistics
$output .= '<div class="card mt-4">';
$output .= '<div class="card-header">';
$output .= '<h4>Course Type Usage</h4>';
$output .= '</div>';
$output .= '<div class="card-body">';

$sql = "SELECT ct.name, ct.licenseprefix,
               COUNT(DISTINCT k.id) as total_keys,
               COUNT(DISTINCT CASE WHEN k.status = 'available' THEN k.id END) as available_keys,
               COUNT(DISTINCT CASE WHEN k.status = 'used' THEN k.id END) as used_keys
        FROM {local_lt_coursetypes} ct
        LEFT JOIN {local_lt_keys} k ON k.coursetypeid = ct.id
        GROUP BY ct.id, ct.name, ct.licenseprefix
        ORDER BY ct.name";

$coursetypes = $DB->get_records_sql($sql);

if ($coursetypes) {
    $output .= '<div class="table-responsive">';
    $output .= '<table class="table table-striped">';
    $output .= '<thead>';
    $output .= '<tr>';
    $output .= '<th>Course Type</th>';
    $output .= '<th>License Prefix</th>';
    $output .= '<th>Total Keys</th>';
    $output .= '<th>Available</th>';
    $output .= '<th>Used</th>';
    $output .= '<th>Usage Rate</th>';
    $output .= '</tr>';
    $output .= '</thead>';
    $output .= '<tbody>';
    
    foreach ($coursetypes as $coursetype) {
        $usagerate = $coursetype->total_keys > 0 ? 
            round(($coursetype->used_keys / $coursetype->total_keys) * 100, 1) : 0;
        
        $output .= '<tr>';
        $output .= '<td><strong>' . $coursetype->name . '</strong></td>';
        $output .= '<td><span class="badge badge-primary">' . $coursetype->licenseprefix . '</span></td>';
        $output .= '<td><span class="badge badge-secondary">' . $coursetype->total_keys . '</span></td>';
        $output .= '<td><span class="badge badge-success">' . $coursetype->available_keys . '</span></td>';
        $output .= '<td><span class="badge badge-warning">' . $coursetype->used_keys . '</span></td>';
        $output .= '<td>';
        $output .= '<div class="progress" style="width: 100px;">';
        $output .= '<div class="progress-bar" role="progressbar" style="width: ' . $usagerate . '%">';
        $output .= $usagerate . '%';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</td>';
        $output .= '</tr>';
    }
    
    $output .= '</tbody>';
    $output .= '</table>';
    $output .= '</div>';
} else {
    $output .= '<p class="text-muted">No course types found.</p>';
}

$output .= '</div>';
$output .= '</div>';

return $output;
