<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Admin dashboard renderable for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\output;

defined('MOODLE_INTERNAL') || die();

use renderable;
use renderer_base;
use templatable;

/**
 * Admin dashboard renderable class.
 */
class admin_dashboard implements renderable, templatable {

    /** @var string $activetab Currently active tab */
    protected $activetab;

    /** @var array $content Tab content */
    protected $content;

    /**
     * Constructor.
     *
     * @param string $activetab Currently active tab
     * @param array $content Tab content
     */
    public function __construct($activetab = 'keys', $content = array()) {
        $this->activetab = $activetab;
        $this->content = $content;
    }

    /**
     * Export data for template.
     *
     * @param renderer_base $output
     * @return array
     */
    public function export_for_template(renderer_base $output) {
        global $CFG;

        $tabs = array(
            'overview' => array(
                'name' => get_string('overview', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'overview')),
                'active' => ($this->activetab === 'overview')
            ),
            'keys' => array(
                'name' => get_string('keys', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'keys')),
                'active' => ($this->activetab === 'keys')
            ),
            'generatekeys' => array(
                'name' => get_string('generatekeys', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'generatekeys')),
                'active' => ($this->activetab === 'generatekeys')
            ),
            'registeruser' => array(
                'name' => get_string('registeruser', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'registeruser')),
                'active' => ($this->activetab === 'registeruser')
            ),
            'partners' => array(
                'name' => get_string('partners', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'partners')),
                'active' => ($this->activetab === 'partners')
            ),
            'coursetypes' => array(
                'name' => get_string('coursetypes', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'coursetypes')),
                'active' => ($this->activetab === 'coursetypes')
            ),
            'statistics' => array(
                'name' => get_string('statistics', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'statistics')),
                'active' => ($this->activetab === 'statistics')
            ),
            'purchase' => array(
                'name' => get_string('purchase', 'local_licensetracker'),
                'url' => new \moodle_url('/local/licensetracker/index.php', array('tab' => 'purchase')),
                'active' => ($this->activetab === 'purchase')
            )
        );

        $data = array(
            'title' => get_string('dashboard', 'local_licensetracker'),
            'tabs' => array_values($tabs),
            'activetab' => $this->activetab,
            'content' => $this->content,
            'wwwroot' => $CFG->wwwroot
        );

        return $data;
    }
}
