<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Key table renderable for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\output;

defined('MOODLE_INTERNAL') || die();

use renderable;
use renderer_base;
use templatable;

/**
 * Key table renderable class.
 */
class key_table implements renderable, templatable {

    /** @var array $keys Array of key records */
    protected $keys;

    /** @var bool $isadmin Whether user is admin */
    protected $isadmin;

    /** @var array $filters Current filters */
    protected $filters;

    /**
     * Constructor.
     *
     * @param array $keys Array of key records
     * @param bool $isadmin Whether user is admin
     * @param array $filters Current filters
     */
    public function __construct($keys, $isadmin = false, $filters = array()) {
        $this->keys = $keys;
        $this->isadmin = $isadmin;
        $this->filters = $filters;
    }

    /**
     * Export data for template.
     *
     * @param renderer_base $output
     * @return array
     */
    public function export_for_template(renderer_base $output) {
        global $CFG;

        $data = array(
            'keys' => array(),
            'isadmin' => $this->isadmin,
            'haskeys' => !empty($this->keys),
            'wwwroot' => $CFG->wwwroot
        );

        foreach ($this->keys as $key) {
            $keydata = array(
                'id' => $key->id,
                'keystring' => $key->keystring,
                'partnername' => $key->partnername,
                'coursetypename' => $key->coursetypename,
                'status' => $key->status,
                'statusclass' => $this->get_status_class($key->status),
                'timecreated' => userdate($key->timecreated),
                'timemodified' => userdate($key->timemodified),
                'canrevoke' => ($this->isadmin && $key->status === 'available'),
                'candelete' => ($this->isadmin && $key->status !== 'used')
            );

            // Add usage information if key is used
            if ($key->status === 'used') {
                $keydata['usedby'] = $key->firstname . ' ' . $key->lastname;
                $keydata['dateused'] = $key->dateused ? userdate($key->dateused) : '';
                $keydata['ipaddressused'] = $key->ipaddressused;
                $keydata['useragent'] = $key->useragent;
            }

            // Add validity dates if set
            if ($key->validfrom) {
                $keydata['validfrom'] = userdate($key->validfrom);
            }
            if ($key->expireson) {
                $keydata['expireson'] = userdate($key->expireson);
            }

            $data['keys'][] = $keydata;
        }

        return $data;
    }

    /**
     * Get CSS class for status.
     *
     * @param string $status
     * @return string
     */
    private function get_status_class($status) {
        switch ($status) {
            case 'available':
                return 'badge-success';
            case 'used':
                return 'badge-info';
            case 'revoked':
                return 'badge-danger';
            default:
                return 'badge-secondary';
        }
    }
}
