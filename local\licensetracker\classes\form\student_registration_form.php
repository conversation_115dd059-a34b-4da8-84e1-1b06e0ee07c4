<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Student registration form for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\form;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/formslib.php');
require_once($CFG->dirroot . '/local/licensetracker/lib.php');

/**
 * Form for partner-initiated student registration.
 */
class student_registration_form extends \moodleform {

    /**
     * Define the form.
     */
    public function definition() {
        global $DB, $USER;

        $mform = $this->_form;

        // Get partner information for current user
        $partner = local_licensetracker_get_partner_for_user();
        if (!$partner) {
            $mform->addElement('static', 'error', '', get_string('nopartnerlinked', 'local_licensetracker'));
            return;
        }

        // License key
        $mform->addElement('text', 'licensekey', get_string('licensekey', 'local_licensetracker'));
        $mform->setType('licensekey', PARAM_TEXT);
        $mform->addRule('licensekey', null, 'required', null, 'client');

        // Student details
        $mform->addElement('header', 'studentdetails', get_string('studentdetails', 'core'));

        $mform->addElement('text', 'username', get_string('username', 'local_licensetracker'));
        $mform->setType('username', PARAM_USERNAME);
        $mform->addRule('username', null, 'required', null, 'client');

        $mform->addElement('text', 'firstname', get_string('firstname', 'local_licensetracker'));
        $mform->setType('firstname', PARAM_TEXT);
        $mform->addRule('firstname', null, 'required', null, 'client');

        $mform->addElement('text', 'lastname', get_string('lastname', 'local_licensetracker'));
        $mform->setType('lastname', PARAM_TEXT);
        $mform->addRule('lastname', null, 'required', null, 'client');

        $mform->addElement('text', 'email', get_string('email', 'local_licensetracker'));
        $mform->setType('email', PARAM_EMAIL);
        $mform->addRule('email', null, 'required', null, 'client');
        $mform->addRule('email', null, 'email', null, 'client');

        $mform->addElement('passwordunmask', 'password', get_string('password', 'local_licensetracker'));
        $mform->setType('password', PARAM_RAW);
        $mform->addRule('password', null, 'required', null, 'client');
        $mform->addRule('password', null, 'minlength', 8, 'client');

        $mform->addElement('text', 'city', get_string('city', 'local_licensetracker'));
        $mform->setType('city', PARAM_TEXT);

        // Country selection
        $countries = get_string_manager()->get_list_of_countries();
        $countries = array('' => get_string('selectacountry')) + $countries;
        $mform->addElement('select', 'country', get_string('country', 'local_licensetracker'), $countries);
        
        // Set default country from partner if available
        if (!empty($partner->country)) {
            $mform->setDefault('country', $partner->country);
        }

        // Course selection based on available course types for this partner
        $sql = "SELECT DISTINCT ct.id, ct.name, c.fullname
                FROM {local_lt_coursetypes} ct
                JOIN {course} c ON ct.moodlecourseid = c.id
                JOIN {local_lt_keys} k ON k.coursetypeid = ct.id
                WHERE k.partnerid = ? AND k.status = 'available'
                ORDER BY ct.name";
        
        $coursetypes = $DB->get_records_sql($sql, array($partner->id));
        $courseoptions = array();
        foreach ($coursetypes as $ct) {
            $courseoptions[$ct->id] = $ct->name . ' (' . $ct->fullname . ')';
        }
        
        if (empty($courseoptions)) {
            $mform->addElement('static', 'nocourses', '', 'No available courses for this partner');
        } else {
            $mform->addElement('select', 'coursetypeid', get_string('coursetype', 'local_licensetracker'), $courseoptions);
            $mform->addRule('coursetypeid', null, 'required', null, 'client');
        }

        // Action buttons
        $this->add_action_buttons(true, get_string('registerstudent', 'local_licensetracker'));
    }

    /**
     * Validate the form data.
     *
     * @param array $data Form data
     * @param array $files Form files
     * @return array Validation errors
     */
    public function validation($data, $files) {
        global $DB, $USER;

        $errors = parent::validation($data, $files);

        // Validate username format and uniqueness
        if (!empty($data['username'])) {
            // Check format
            if (!preg_match('/^[a-zA-Z0-9._-]+$/', $data['username'])) {
                $errors['username'] = get_string('error:invalidusernameformat', 'local_licensetracker');
            } else if (strlen($data['username']) < 3) {
                $errors['username'] = get_string('error:usernametoshort', 'local_licensetracker');
            } else if ($DB->record_exists('user', array('username' => $data['username'], 'deleted' => 0))) {
                $errors['username'] = get_string('error:usernameexists', 'local_licensetracker');
            }
        }

        // Validate email format and uniqueness
        if (!empty($data['email'])) {
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = get_string('error:invalidemail', 'local_licensetracker');
            } else if ($DB->record_exists('user', array('email' => $data['email'], 'deleted' => 0))) {
                $errors['email'] = get_string('error:emailexists', 'local_licensetracker');
            }
        }

        // Validate password strength
        if (!empty($data['password'])) {
            if (strlen($data['password']) < 8) {
                $errors['password'] = get_string('error:passwordtooshort', 'local_licensetracker');
            }
        }

        // Validate license key with comprehensive partner validation
        if (!empty($data['licensekey'])) {
            $partner = local_licensetracker_get_partner_for_user();
            if ($partner) {
                $validation = local_licensetracker_validate_partner_key_usage($partner->id, $data['licensekey']);
                if (!$validation['valid']) {
                    $errors['licensekey'] = $validation['message'];
                } else {
                    // Check if course type matches
                    if (!empty($data['coursetypeid']) && $validation['key']->coursetypeid != $data['coursetypeid']) {
                        $errors['licensekey'] = get_string('error:keynotforcourse', 'local_licensetracker');
                    }
                }
            } else {
                $errors['licensekey'] = get_string('error:nopartnercontext', 'local_licensetracker');
            }
        }

        // Check for duplicate username
        if (!empty($data['username'])) {
            if ($DB->record_exists('user', array('username' => $data['username'], 'deleted' => 0))) {
                $errors['username'] = 'Username already exists';
            }
        }

        // Check for duplicate email
        if (!empty($data['email'])) {
            if ($DB->record_exists('user', array('email' => $data['email'], 'deleted' => 0))) {
                $errors['email'] = 'Email address already exists';
            }
        }

        return $errors;
    }
}
