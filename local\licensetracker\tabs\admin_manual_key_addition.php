<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Admin manual key addition tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

// CRITICAL: Verify admin permissions
$context = context_system::instance();
require_capability('local/licensetracker:manageallkeys', $context);

$form = new \local_licensetracker\form\manual_key_addition_form();

if ($form->is_cancelled()) {
    redirect(new moodle_url('/local/licensetracker/index.php', array('tab' => 'generatekeys')));
} else if ($data = $form->get_data()) {
    
    try {
        // Prepare key data
        $keydata = new stdClass();
        $keydata->keystring = trim(strtoupper($data->keystring));
        $keydata->coursetypeid = $data->coursetypeid;
        $keydata->partnerid = $data->partnerid;
        $keydata->status = $data->status;
        $keydata->timecreated = time();
        $keydata->timemodified = time();
        $keydata->createdby = $USER->id;
        
        if (!empty($data->validfrom)) {
            $keydata->validfrom = $data->validfrom;
        }
        
        if (!empty($data->expireson)) {
            $keydata->expireson = $data->expireson;
        }

        if (!empty($data->notes)) {
            $keydata->notes = $data->notes;
        }

        // Insert the key
        $keyid = $DB->insert_record('local_lt_keys', $keydata);
        
        // Get partner and course type names for success message
        $partner = $DB->get_record('local_lt_partners', array('id' => $data->partnerid));
        $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $data->coursetypeid));
        
        // Log the action
        $logdata = array(
            'context' => $context,
            'objectid' => $keyid,
            'other' => array(
                'keystring' => $keydata->keystring,
                'partnerid' => $data->partnerid,
                'partnername' => $partner->partnername,
                'coursetypeid' => $data->coursetypeid,
                'coursetypename' => $coursetype->name,
                'method' => 'manual_addition'
            )
        );
        
        $event = \local_licensetracker\event\key_created::create($logdata);
        $event->trigger();
        
        // Success message
        $successmsg = get_string('keyaddedsuccessfully', 'local_licensetracker', array(
            'keystring' => $keydata->keystring,
            'partner' => $partner->partnername,
            'coursetype' => $coursetype->name
        ));
        
        redirect(new moodle_url('/local/licensetracker/index.php', array('tab' => 'keys')), 
                $successmsg, null, \core\output\notification::NOTIFY_SUCCESS);
        
    } catch (Exception $e) {
        redirect($PAGE->url, get_string('error:keyadditionfailed', 'local_licensetracker') . ': ' . $e->getMessage(), 
                null, \core\output\notification::NOTIFY_ERROR);
    }
}

// Display information about manual key addition
echo '<div class="alert alert-info mb-4">';
echo '<h5>' . get_string('manualkeyaddition', 'local_licensetracker') . '</h5>';
echo '<p>' . get_string('manualkeyaddition_desc', 'local_licensetracker') . '</p>';
echo '<ul>';
echo '<li>' . get_string('manualkeyaddition_tip1', 'local_licensetracker') . '</li>';
echo '<li>' . get_string('manualkeyaddition_tip2', 'local_licensetracker') . '</li>';
echo '<li>' . get_string('manualkeyaddition_tip3', 'local_licensetracker') . '</li>';
echo '<li>' . get_string('manualkeyaddition_tip4', 'local_licensetracker') . '</li>';
echo '</ul>';
echo '</div>';

// Display recent manual additions
echo '<div class="card mb-4">';
echo '<div class="card-header">';
echo '<h5>' . get_string('recentmanualadditions', 'local_licensetracker') . '</h5>';
echo '</div>';
echo '<div class="card-body">';

$sql = "SELECT k.*, p.partnername, ct.name as coursetypename, u.firstname, u.lastname
        FROM {local_lt_keys} k
        JOIN {local_lt_partners} p ON k.partnerid = p.id
        JOIN {local_lt_coursetypes} ct ON k.coursetypeid = ct.id
        LEFT JOIN {user} u ON k.createdby = u.id
        WHERE k.createdby IS NOT NULL
        ORDER BY k.timecreated DESC
        LIMIT 10";

$recentkeys = $DB->get_records_sql($sql);

if (!empty($recentkeys)) {
    echo '<div class="table-responsive">';
    echo '<table class="table table-sm">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>' . get_string('keystring', 'local_licensetracker') . '</th>';
    echo '<th>' . get_string('partner', 'local_licensetracker') . '</th>';
    echo '<th>' . get_string('coursetype', 'local_licensetracker') . '</th>';
    echo '<th>' . get_string('status', 'local_licensetracker') . '</th>';
    echo '<th>' . get_string('createdby', 'local_licensetracker') . '</th>';
    echo '<th>' . get_string('timecreated', 'local_licensetracker') . '</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($recentkeys as $key) {
        $statusclass = $key->status === 'available' ? 'badge-success' : 
                      ($key->status === 'used' ? 'badge-primary' : 'badge-danger');
        
        echo '<tr>';
        echo '<td><code>' . $key->keystring . '</code></td>';
        echo '<td>' . $key->partnername . '</td>';
        echo '<td>' . $key->coursetypename . '</td>';
        echo '<td><span class="badge ' . $statusclass . '">' . get_string($key->status, 'local_licensetracker') . '</span></td>';
        echo '<td>' . ($key->firstname ? $key->firstname . ' ' . $key->lastname : get_string('unknown', 'local_licensetracker')) . '</td>';
        echo '<td>' . userdate($key->timecreated, get_string('strftimedatetimeshort', 'core_langconfig')) . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
} else {
    echo '<p class="text-muted">' . get_string('norecentmanualadditions', 'local_licensetracker') . '</p>';
}

echo '</div>';
echo '</div>';

// Display the form
echo '<div class="card">';
echo '<div class="card-header">';
echo '<h4>' . get_string('addnewkey', 'local_licensetracker') . '</h4>';
echo '</div>';
echo '<div class="card-body">';
$form->display();
echo '</div>';
echo '</div>';
