Moodle Plugin Development: License Key Tracker (local_licensetracker), Authentication Plugin (auth_licensetracker), and Administrative Tool (tool_licensetrackeradmin) - Comprehensive Text-Based Instructions for AI Coder (UPDATED)

This document outlines a complete, detailed plan for an AI coder to develop three interconnected Moodle plugins: a core license key management plugin (local_licensetracker), an associated authentication plugin (auth_licensetracker), and a dedicated administrative tool plugin (tool_licensetrackeradmin). The instructions encompass all specified requirements, including the recent improvement suggestions, ensuring the AI coder has all necessary conceptual information and functional requirements to proceed with implementation.

I. Project Overview and Core Objectives
The overarching goal is to establish a robust system within Moodle for managing and validating license keys during student self-registration and for administrative oversight. This system needs to support a full key lifecycle, administrative oversight, partner-specific views, and seamless integration with Moodle's user creation process, while providing advanced tools for data management.

The AI coder must develop the following components:

local_licensetracker Plugin: This will be the central hub for core license key data management. It handles key generation (queuing for large batches), status tracking, partner management, course type definitions, and provides the main administrative and partner-specific user interfaces for day-to-day operations.

auth_licensetracker Plugin: This will extend Moodle's authentication capabilities to enforce license key validation during self-registration, update key statuses, and automatically enroll new users into courses with specific roles and enrollment methods based on the key's attributes.

tool_licensetrackeradmin Plugin: This optional administrative tool plugin will handle advanced data management at scale, such as bulk import/export of keys and partners via CSV, and provide enhanced logging views for auditing.

II. Functional Requirements and User Stories
A. License Key Management (local_licensetracker) - Core Data & UI
Key Lifecycle:

Keys must exist in one of three states: 'available', 'used', or 'revoked'.

A newly generated key will always be 'available'.

Once a key is successfully used for student registration, its status must automatically change to 'used'.

Keys marked as 'used' or 'revoked' are permanently non-reusable for new registrations.

Administrators must have the ability to manually change a key's status to 'revoked'.

New: Add validfrom (timestamp) and expireson (timestamp) fields to the license key. These fields are optional. If validfrom is set, the key is only usable after this date. If expireson is set, the key is only usable before this date. Validation must enforce this.

Key Generation (Administrator Functionality):

Administrators need an interface within the local_licensetracker plugin to generate new license keys.

When generating keys, the administrator must select an associated 'course type' and 'partner' for the keys.

New: The key format (e.g., xxxx-xxxx-xxxx-xxxx) must be configurable via an admin setting (tool_licensetrackeradmin settings page). This setting should allow defining the length of segments, number of segments, and character set (alphanumeric, etc.). The default should be xxxx-xxxx-xxxx-xxxx with alphanumeric characters.

Each generated key must be globally unique within the system. The system must retry key generation if a duplicate is accidentally produced.

Administrators should be able to specify the number of keys to generate in a single batch.

New: For large batches (e.g., >100 keys), the generation process should trigger an asynchronous Moodle queued task. The UI should provide immediate feedback that the task has been queued and provide a link to a log/task status page (possibly within tool_licensetrackeradmin).

Data Management (Administrator Functionality):

Partners:

Administrators must be able to add, edit, and delete partner entities.

Each partner must have a name, a contact email, and be linked to a specific Moodle user account. This Moodle user will be the designated "System Partner" for that entity.

Partners can optionally have a country associated.

New: Partners can optionally have a lang (language) setting, which, if set, will default the language for users registering via this partner's keys.

Deletion of a partner should be prevented if any keys are currently assigned to that partner. A clear error message should inform the administrator.

Course Types:

Administrators must be able to add, edit, and delete predefined 'course types'.

Each course type needs a name, a description, and a default price.

New: Extend local_licensetracker_coursetypes to include not only moodlecourseid but also an enrolmethod (e.g., 'self', 'manual') and a defaultroleid (Moodle role ID) for students enrolling via this course type.

Deletion of a course type should be prevented if any keys or partner assignments are currently linked to it. A clear error message should inform the administrator.

Partner-Course Type Assignments:

Administrators must be able to assign specific 'course types' to individual partners. This allows for customized offerings.

For each assigned course type, a partner-specific price can be set, overriding the course type's default price.

User Interface (UI) and Permissions (local_licensetracker):

Administrator Dashboard:

A central administrative page for the local_licensetracker plugin.

This page must provide distinct tabs for:

"Keys": Displays a comprehensive table of all license keys in the system (available, used, revoked). This table must be filterable by partner, course type, status, and search by key string. It should include options for bulk deletion of keys and individual key revocation/editing. Each row should display the key string, status, associated partner, associated course type, user who used it (if used), date of usage (if used), IP address of usage, validfrom, and expireson.

"Generate Keys": The form for generating new keys (number of keys, partner, course type, optional validfrom, expireson). For large batches, a message indicating the task is queued.

"Partners": A table listing all registered partners, with options to add, edit, or delete partners. Editing a partner should allow associating a Moodle user, assigning specific course types with custom prices, and setting the optional default lang.

"Course Types": A table listing all predefined course types, with options to add, edit, or delete course types. Editing should allow setting the moodlecourseid, enrolmethod, and defaultroleid.

"Statistics": Displays aggregated data on key usage across the entire system (e.g., total available, total used, breakdown by course type and partner).

"Purchase": A placeholder tab initially displaying a "Coming Soon" message.

Administrators must have full permissions (local/licensetracker:manageallkeys, local/licensetracker:managecoursetypes, local/licensetracker:managepartners).

Partner Dashboard:

A dedicated view for users with the "System Partner" role.

This view must dynamically adjust, only showing data relevant to their partner entity.

Tabs for partners should include:

"Assigned Keys": A table displaying only the keys assigned to their partner (available, used, revoked). Filtering should be by course type and status. No bulk deletion or key generation options.

"Statistics": Displays aggregated data for their specific partner (e.g., total available keys for their partner, total used keys for their partner, breakdown by course type for their partner).

"Register New Student": A form for partners to initiate student registration using a key (this will integrate with the auth_licensetracker plugin's logic for key validation and enrollment). This form should include fields for student details (username, first name, last name, email, password, city, country) and a field for the license key. The form must also allow the partner to select the Moodle course the student should be enrolled into based on the key's associated course type. The lang field from the partner's profile should be considered for the new user's default language.

"Purchase": A placeholder tab initially displaying a "Coming Soon" message.

Partners must have restricted permissions (local/licensetracker:viewpartnerkeys, local/licensetracker:viewpartnerstats, local/licensetracker:usekey).

If a Moodle user with partner capabilities is not linked to a partner record, they should see a message indicating this and directing them to contact an administrator.

General UI Considerations:

All tables should be paginated and sortable.

Confirmation dialogs for deletion actions (keys, partners, course types).

Use Moodle's standard forms library for all data entry and editing.

Utilize Moodle's output rendering system with Mustache templates for consistent UI.

Ensure compatibility with the "Edumy" Moodle theme (implying Boost-based styling compatibility).

B. Student Self-Registration and Key Validation (auth_licensetracker)
Integration Point: The auth_licensetracker plugin must intercept Moodle's standard self-registration process (specifically, the part where a new user account is created).

License Key Requirement:

During self-registration, a new field must be added to the registration form: "License Key".

This field is mandatory for "global" students. For the initial implementation, assume all self-registering students require a key. (The "global" vs. "local" student logic can be omitted for now if simpler, or implemented as a configurable country list in auth_licensetracker settings).

If a student attempts to register without providing a key, or provides an invalid/used/expired/not-yet-active key, the registration must fail with an appropriate error message (invalidkey, registrationrequireskey, keynotactive, keyexpired).

Key Validation Logic:

The provided license key must be validated against the local_licensetracker_keys table.

A key is valid only if:

It exists in the database.

Its status is 'available'.

It matches the configurable key format defined in tool_licensetrackeradmin.

The current date is within its validfrom and expireson window (if these fields are set).

Key Status Update:

Upon successful student registration using a valid key, the key's status in the local_licensetracker_keys table must automatically change from 'available' to 'used'.

The usedbyuserid field must be populated with the newly created Moodle user's ID.

The dateused field must be populated with the current timestamp.

The ipaddressused field must capture the IP address of the user who registered.

New: Record useragent string (from $_SERVER['HTTP_USER_AGENT']) alongside the IP address for more robust logging.

Automatic Course Enrollment:

After successful key validation and user creation, the new student must be automatically enrolled into a Moodle course.

The specific Moodle course, enrollment method, and default role for enrollment will be determined by the moodlecourseid, enrolmethod, and defaultroleid fields associated with the license key's coursetypeid in the local_licensetracker_coursetypes table.

The enrollment should use standard Moodle enrollment methods (enrol_plugin->enrol_user()). For 'self' enrollment method, ensure an active self-enrollment instance exists for the course; if not, fall back to 'manual' enrollment. If 'manual' enrollment is used, ensure the enrollment is marked as active.

Add a success message to the user about their enrollment.

New: If the partner who owns the key has a lang setting, the new Moodle user's preferred language should be set to this.

C. Advanced Admin Tools (tool_licensetrackeradmin)
Plugin Purpose: This plugin is dedicated solely to administrative utilities that operate on the local_licensetracker data at scale.

Permissions: Only users with tool/licensetrackeradmin:manage capability (typically administrators) should access this plugin.

Features:

CSV Bulk Upload for Keys:

Provide an interface to upload a CSV file containing new license keys.

The CSV format should allow specifying keystring, coursetypename (to lookup coursetypeid), partnername (to lookup partnerid), validfrom (optional, date format), expireson (optional, date format).

The system should validate each row and process the keys, handling duplicates or invalid data gracefully (e.g., skipping invalid rows and providing a summary report).

Processing should leverage the asynchronous task API for large files.

CSV Bulk Upload for Partners:

Provide an interface to upload a CSV file containing new partner information.

The CSV format should allow specifying partnername, contactemail, country (optional), moodleusername (to lookup moodleuserid), and a list of coursetypes with their customprice (e.g., CourseTypeName1:Price1;CourseTypeName2:Price2).

Validation and asynchronous processing are required.

CSV Bulk Export for Keys and Partners:

Allow administrators to export all license keys to a CSV file, with options to filter by status, partner, or course type.

Allow administrators to export all partner data to a CSV file, including their assigned course types and custom prices.

Enhanced Logging View:

Display a detailed log of all license key-related events (creation, usage, revocation, manual admin changes, import/export operations).

This log should include timestamps, event type, actor (Moodle user ID/name if available, or system if automated), key ID, associated partner/course type, IP address, and user agent.

Filtering and pagination for the log view are essential.

Plugin Settings:

Manage the configurable key format (length of segments, number of segments, character set) here.

Define the list of "local" countries if the "global student" logic is implemented, otherwise a simple toggle for "all students require key".

III. Detailed Implementation Steps for AI Coder
The AI coder must adhere to Moodle's plugin development guidelines and security best practices.

A. local_licensetracker Plugin Implementation
Plugin Setup:

Create the core plugin directory: local/licensetracker/.

Set up standard Moodle plugin files: version.php, settings.php (for local plugin specific settings, likely minimal, maybe a general enable/disable), lang/en/local_licensetracker.php.

The version.php must include standard plugin metadata (component, version, requires, maturity, release).

lang/en/local_licensetracker.php must contain all the language strings for local_licensetracker, ensuring every label, message, and form element has a corresponding string, including new ones for validfrom, expireson, partner lang, and the new course type fields.

Define plugin capabilities in db/access.php for local_licensetracker specific actions (viewing/managing some keys, managing some partners/coursetypes) as detailed previously. Capabilities related to manageallkeys, bulk import/export, and advanced logging will move to tool_licensetrackeradmin.

Database Schema Definition (db/install.xml):

Define the following tables as per the schema provided previously, with the additions:

local_licensetracker_coursetypes: Stores course type definitions (id, name, description, defaultprice, moodlecourseid, enrolmethod (varchar), defaultroleid (int), timecreated, timemodified). moodlecourseid will be integer, NOTNULL, UNSIGNED, DEFAULT 0. enrolmethod and defaultroleid will be NULLABLE initially, but forms will make them mandatory.

local_licensetracker_partners: Stores partner information (id, moodleuserid, partnername, contactemail, country, lang (varchar, 15 chars, nullable), timecreated, timemodified).

local_licensetracker_partner_coursetypes: A junction table for partner-specific course type prices (id, partnerid, coursetypeid, price, timecreated, timemodified).

local_licensetracker_keys: Stores license key details (id, keystring, coursetypeid, partnerid, status, usedbyuserid, timecreated, timemodified, dateused, ipaddressused, validfrom (timestamp, nullable), expireson (timestamp, nullable), useragent (text, nullable)).

Ensure all necessary primary keys, unique keys, foreign keys (with ONDELETE and ONUPDATE rules as specified), and indexes are correctly defined.

Core Plugin Logic (lib.php and classes/):

Create lib.php for essential helper functions, including:

Functions for checking user roles (is_admin, is_partner).

A function to retrieve partner information for a given Moodle user.

A function local_licensetracker_generate_unique_key() that produces a unique license key in the specified format (now retrieved from tool_licensetrackeradmin config), ensuring uniqueness against existing keys in the database.

Functions for interacting with the database tables (e.g., local_licensetracker_get_keys, local_licensetracker_update_key_status, local_licensetracker_get_partner_stats, etc.). These should encapsulate all database operations.

New: Functions to interact with the new validfrom, expireson, enrolmethod, defaultroleid, lang fields.

Classes Directory (classes/):

Create classes/form/ for all Moodle forms.

classes/form/key_generation_form.php: For administrator key generation, including validfrom and expireson fields.

classes/form/partner_form.php: For adding/editing partners, including the lang field.

classes/form/coursetype_form.php: For adding/editing course types, including moodlecourseid, enrolmethod, defaultroleid.

classes/form/assign_coursetypes_form.php: For assigning course types to partners.

classes/form/student_registration_form.php: For partner-initiated student registration.

Create classes/output/ for Mustache output renderers.

classes/output/renderer.php: Custom renderer for the plugin's pages.

classes/output/key_table.php: A renderable class for displaying key tables, including validfrom, expireson, useragent, ipaddressused.

classes/output/partner_table.php: A renderable class for displaying partner tables, including lang.

classes/output/coursetype_table.php: A renderable class for displaying course type tables, including enrolmethod, defaultroleid.

Create classes/task/ for asynchronous tasks.

classes/task/generate_keys_task.php: A Moodle adhoc task implementation for generating large batches of keys. This task will receive parameters like number of keys, partner ID, course type ID, validfrom, expireson. It will use mtrace() for logging its progress.

Create classes/local/ for specific local helper classes.

classes/local/ajax.php: For handling AJAX requests, if any are needed for dynamic updates or validation.

Admin and Partner Interfaces (index.php, keys.php, partners.php, coursetypes.php, statistics.php, register.php, purchase.php):

Main Entry Point (index.php): This file will serve as the central router for the plugin's interface. It should determine the user's role (admin or partner) and display the appropriate dashboard and tabs.

Tabbed Navigation: Implement Moodle's admin_navigation or custom navigation to create dynamic tabs based on user roles.

Each tab will likely correspond to a separate PHP file in the local/licensetracker/ directory.

These PHP files will:

Perform capability checks.

Instantiate forms and handle their submission (validation, database operations, queueing tasks for key generation).

Retrieve data from the database.

Pass data to Mustache templates for rendering.

DataTables and Filtering: Implement Moodle's standard tables or a custom solution that supports pagination, sorting, and filtering.

Bulk Operations: Implement logic for bulk deletion of keys, requiring user confirmation.

Mustache Templates (templates/):

Create .mustache files for each major view or component that requires rendering:

templates/admin_dashboard.mustache: Overall admin dashboard layout with tabs.

templates/partner_dashboard.mustache: Overall partner dashboard layout with tabs.

templates/key_list.mustache: Table structure for displaying license keys.

templates/generate_keys_form.mustache: For the key generation form, including new date fields and a message for queued tasks.

templates/partner_list.mustache: Table structure for displaying partners.

templates/partner_form.mustache: For add/edit partner form, including the lang field.

templates/coursetype_list.mustache: Table structure for displaying course types.

templates/coursetype_form.mustache: For add/edit course type form, including enrolmethod, defaultroleid.

templates/assign_coursetypes_form.mustache: For assigning course types to partners.

templates/statistics_view.mustache: For displaying statistics.

templates/register_student_form.mustache: For the partner-initiated student registration form.

templates/coming_soon.mustache: For the purchase tab placeholder.

B. auth_licensetracker Plugin Implementation
This plugin will reside in your_moodle_root/auth/licensetracker/.

Plugin Setup:

Create the core plugin directory: auth/licensetracker/.

Set up standard Moodle plugin files: version.php, settings.php (for auth plugin-specific settings, e.g., enabling/disabling the auth method, configurable list of "local" countries if implemented), lang/en/auth_licensetracker.php, auth.php.

version.php must include standard plugin metadata.

lang/en/auth_licensetracker.php must contain necessary language strings for the authentication process, including messages for invalid keys, expired keys, not-yet-active keys, and successful enrollment.

Authentication Logic (auth.php):

Extend the auth_plugin_base class.

Implement the user_signup_form() method to add the "License Key" field to the self-registration form. This field must be mandatory and validated.

Implement the user_signup() method to intercept the form submission.

Retrieve the submitted license key.

Call the necessary functions from local_licensetracker/lib.php to validate the key:

Check if the key exists and its status is 'available'.

Check if the key matches the configurable format from tool_licensetrackeradmin settings.

Check validfrom and expireson dates.

If invalid/used/expired/not-yet-active, throw a Moodle auth_exception with the appropriate language string.

If the key is valid, allow Moodle's user creation process to continue.

After successful user creation, retrieve the newly created user's ID.

Update the license key's status to 'used' in the local_licensetracker_keys table, populating usedbyuserid, dateused, ipaddressused, and useragent.

Determine the target Moodle course, enrollment method, and default role for enrollment using the moodlecourseid, enrolmethod, and defaultroleid from the local_licensetracker_coursetypes table based on the key's coursetypeid.

Enroll the newly created user into the determined Moodle course using the specified enrolmethod and defaultroleid. Prioritize using enrol_self_instance if enrolmethod is 'self' and such an instance exists; otherwise, use enrol_manual_add_instance or similar.

New: If the partner associated with the used key has a lang setting, update the newly created Moodle user's preferred language to this value.

Add a success message to the user about their enrollment.

C. tool_licensetrackeradmin Plugin Implementation
This plugin will reside in your_moodle_root/admin/tool/licensetrackeradmin/.

Plugin Setup:

Create the plugin directory: admin/tool/licensetrackeradmin/.

Set up standard Moodle plugin files: version.php, settings.php, lang/en/tool_licensetrackeradmin.php, db/access.php.

version.php must include standard plugin metadata.

settings.php will define all configuration settings for this tool, including the configurable key format (segment length, count, character set, stored as a JSON string or similar). It might also contain settings for the default "local" countries for the "global student" logic.

lang/en/tool_licensetrackeradmin.php must contain all language strings specific to this admin tool.

Define plugin capabilities in db/access.php for tool/licensetrackeradmin:manage (full access to all tool features), tool/licensetrackeradmin:bulkimport, tool/licensetrackeradmin:bulkexport, tool/licensetrackeradmin:viewlogs. These capabilities will be assigned to administrators.

Admin Interface and Logic:

Create a main entry point index.php for the tool, accessible via Moodle's Site Administration menu (admin tree).

This page should provide a tabbed interface for:

"Bulk Import Keys": Contains a Moodle form for uploading CSV files. The form should specify expected CSV columns. Logic for processing the CSV and queueing an asynchronous task (tool_licensetrackeradmin_import_keys_task.php) for large imports.

"Bulk Import Partners": Similar CSV upload form and asynchronous task queueing (tool_licensetrackeradmin_import_partners_task.php).

"Bulk Export Keys": Form for selecting export filters (partner, course type, status) and triggering a CSV download.

"Bulk Export Partners": Form for triggering a CSV download of all partner data.

"Activity Logs": Displays the detailed license key event log, with filters and pagination. This view will query the local_licensetracker_keys table for its enhanced log data.

"Settings": The settings defined in settings.php for the configurable key format and any other global tool settings.

Classes Directory (classes/):

classes/form/bulk_import_keys_form.php

classes/form/bulk_import_partners_form.php

classes/form/bulk_export_keys_form.php

classes/form/bulk_export_partners_form.php

classes/task/import_keys_task.php: Moodle adhoc task for bulk key import.

classes/task/import_partners_task.php: Moodle adhoc task for bulk partner import.

classes/output/log_table.php: Renderable class for displaying activity logs.

classes/external/ (for API):

classes/external/licensetracker_api.php: This class will contain static methods that implement the external functions for API access.

Web Service Definition (db/services.php):

Define external functions for API access within tool_licensetrackeradmin.

Example functions: tool_licensetrackeradmin_generate_keys, tool_licensetrackeradmin_check_key_status, tool_licensetrackeradmin_use_key.

Each function will require appropriate capabilities. The tool_licensetrackeradmin_use_key function will replicate the logic used by the auth_licensetracker for key validation and status update, to allow external systems to register users via a license key.

Mustache Templates (templates/):

templates/admin_tool_dashboard.mustache: Overall dashboard layout with tabs.

templates/bulk_import_keys_form.mustache

templates/bulk_import_partners_form.mustache

templates/bulk_export_keys_form.mustache

templates/bulk_export_partners_form.mustache

templates/activity_log_table.mustache

D. Role Assignment (Custom Role)
Manual Step (Admin): The AI coder should note that after plugin installation, an administrator will manually create a custom Moodle role named "System Partner".

Capability Assignment: The "System Partner" role will then be assigned the following capabilities from local/licensetracker/db/access.php:

local/licensetracker:viewpartnerkeys

local/licensetracker:usekey

local/licensetracker:viewpartnerstats

The local_licensetracker_partners table records will then link Moodle users to these partners, allowing the local_licensetracker_is_partner() function to correctly identify and restrict views for these users.

IV. General Moodle Development Best Practices for AI Coder
Security: Always use Moodle's database abstraction layer ($DB) and parameterized queries to prevent SQL injection. Escape all user input before displaying it to prevent XSS. Implement proper capability checks (require_capability, has_capability) for all actions.

Error Handling: Implement robust error handling and user-friendly messages for all operations (e.g., key generation failures, invalid deletions, database errors, import validation failures).

Performance: Optimize database queries, especially for large datasets. Use Moodle's Task API for all long-running operations. Consider caching where appropriate.

Maintainability: Write clear, well-commented PHP code. Adhere to Moodle's coding style guidelines. Use Moodle's namespacing for classes.

Upgradeability: Ensure db/upgrade.php logic is correctly handled if schema changes occur in future versions. For this initial version, install.xml is the primary focus, but the AI should implicitly understand to create an upgrade.php if future schema alterations are needed.

Debugging: Utilize Moodle's debugging settings ($CFG->debug) for development. Use mtrace() within tasks for logging.