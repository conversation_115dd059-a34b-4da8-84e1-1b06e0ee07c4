<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Post installation and migration code for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Post installation procedure.
 */
function xmldb_local_licensetracker_install() {
    global $DB;

    // Create custom user profile field for user type
    $field = new stdClass();
    $field->shortname = 'usertype';
    $field->name = 'User Type';
    $field->datatype = 'menu';
    $field->description = 'Indicates whether user is a partner student or local student';
    $field->descriptionformat = FORMAT_HTML;
    $field->categoryid = 1; // Default category
    $field->sortorder = 1;
    $field->required = 0;
    $field->locked = 1; // Locked so users can't change it
    $field->visible = 2; // Visible to everyone
    $field->forceunique = 0;
    $field->signup = 0;
    $field->defaultdata = 'local_student';
    $field->param1 = "local_student\npartner_student"; // Menu options
    $field->param2 = 'local_student'; // Default value

    // Check if field already exists
    if (!$DB->record_exists('user_info_field', array('shortname' => 'usertype'))) {
        $DB->insert_record('user_info_field', $field);
    }

    // Ensure all course types have license prefixes
    local_licensetracker_ensure_course_prefixes();

    return true;
}
