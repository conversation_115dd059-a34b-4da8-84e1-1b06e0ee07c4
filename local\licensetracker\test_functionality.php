<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Comprehensive functionality testing for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once('../../config.php');
require_once($CFG->libdir . '/adminlib.php');

admin_externalpage_setup('local_licensetracker_test');

$PAGE->set_title(get_string('testfunctionality', 'local_licensetracker'));
$PAGE->set_heading(get_string('testfunctionality', 'local_licensetracker'));

echo $OUTPUT->header();

echo '<div class="container-fluid">';
echo '<h2>' . get_string('testfunctionality', 'local_licensetracker') . '</h2>';
echo '<p class="lead">' . get_string('testfunctionality_desc', 'local_licensetracker') . '</p>';

$tests = array();
$passed = 0;
$failed = 0;
$warnings = 0;

// Test 1: Database tables exist
echo '<div class="card mb-4">';
echo '<div class="card-header"><h4>Database Structure Tests</h4></div>';
echo '<div class="card-body">';

$required_tables = array(
    'local_lt_partners',
    'local_lt_coursetypes', 
    'local_lt_partner_coursetypes',
    'local_lt_keys'
);

foreach ($required_tables as $table) {
    if ($DB->get_manager()->table_exists($table)) {
        echo '<p class="text-success">✓ Table ' . $table . ' exists</p>';
        $passed++;
    } else {
        echo '<p class="text-danger">✗ Table ' . $table . ' missing</p>';
        $failed++;
    }
}

echo '</div></div>';

// Test 2: Plugin capabilities
echo '<div class="card mb-4">';
echo '<div class="card-header"><h4>Capability Tests</h4></div>';
echo '<div class="card-body">';

$required_capabilities = array(
    'local/licensetracker:manageallkeys',
    'local/licensetracker:viewpartnerkeys',
    'local/licensetracker:usekey'
);

$context = context_system::instance();
foreach ($required_capabilities as $capability) {
    $roles_with_cap = get_roles_with_capability($capability, CAP_ALLOW, $context);
    if (!empty($roles_with_cap)) {
        echo '<p class="text-success">✓ Capability ' . $capability . ' assigned to roles</p>';
        $passed++;
    } else {
        echo '<p class="text-warning">⚠ Capability ' . $capability . ' not assigned to any roles</p>';
        $warnings++;
    }
}

echo '</div></div>';

// Test 3: Course types and prefixes
echo '<div class="card mb-4">';
echo '<div class="card-header"><h4>Course Type Tests</h4></div>';
echo '<div class="card-body">';

$coursetypes = $DB->get_records('local_lt_coursetypes');
if (!empty($coursetypes)) {
    echo '<p class="text-success">✓ Found ' . count($coursetypes) . ' course types</p>';
    $passed++;
    
    $prefix_issues = 0;
    foreach ($coursetypes as $ct) {
        if (empty($ct->licenseprefix)) {
            echo '<p class="text-warning">⚠ Course type "' . $ct->name . '" has no license prefix</p>';
            $prefix_issues++;
        } else if (!preg_match('/^[A-Z0-9]{2,10}$/', $ct->licenseprefix)) {
            echo '<p class="text-warning">⚠ Course type "' . $ct->name . '" has invalid prefix format: ' . $ct->licenseprefix . '</p>';
            $prefix_issues++;
        }
    }
    
    if ($prefix_issues == 0) {
        echo '<p class="text-success">✓ All course types have valid license prefixes</p>';
        $passed++;
    } else {
        $warnings += $prefix_issues;
    }
} else {
    echo '<p class="text-warning">⚠ No course types found</p>';
    $warnings++;
}

echo '</div></div>';

// Test 4: Key generation
echo '<div class="card mb-4">';
echo '<div class="card-header"><h4>Key Generation Tests</h4></div>';
echo '<div class="card-body">';

if (!empty($coursetypes)) {
    $test_coursetype = reset($coursetypes);
    try {
        $test_key = local_licensetracker_generate_unique_key($test_coursetype->id);
        if (!empty($test_coursetype->licenseprefix) && strpos($test_key, $test_coursetype->licenseprefix) === 0) {
            echo '<p class="text-success">✓ Key generation works with prefix: ' . $test_key . '</p>';
            $passed++;
            
            // Clean up test key
            $DB->delete_records('local_lt_keys', array('keystring' => $test_key));
        } else {
            echo '<p class="text-warning">⚠ Generated key may not have correct prefix: ' . $test_key . '</p>';
            $warnings++;
        }
    } catch (Exception $e) {
        echo '<p class="text-danger">✗ Key generation failed: ' . $e->getMessage() . '</p>';
        $failed++;
    }
} else {
    echo '<p class="text-warning">⚠ Cannot test key generation without course types</p>';
    $warnings++;
}

echo '</div></div>';

// Test 5: Partner functionality
echo '<div class="card mb-4">';
echo '<div class="card-header"><h4>Partner Tests</h4></div>';
echo '<div class="card-body">';

$partners = $DB->get_records('local_lt_partners');
if (!empty($partners)) {
    echo '<p class="text-success">✓ Found ' . count($partners) . ' partners</p>';
    $passed++;
    
    // Check partner-course type assignments
    $assignments = $DB->count_records('local_lt_partner_coursetypes');
    if ($assignments > 0) {
        echo '<p class="text-success">✓ Found ' . $assignments . ' partner-course type assignments</p>';
        $passed++;
    } else {
        echo '<p class="text-warning">⚠ No partner-course type assignments found</p>';
        $warnings++;
    }
} else {
    echo '<p class="text-warning">⚠ No partners found</p>';
    $warnings++;
}

echo '</div></div>';

// Test 6: Authentication plugin
echo '<div class="card mb-4">';
echo '<div class="card-header"><h4>Authentication Plugin Tests</h4></div>';
echo '<div class="card-body">';

if (is_enabled_auth('licensetracker')) {
    echo '<p class="text-success">✓ License tracker authentication plugin is enabled</p>';
    $passed++;
} else {
    echo '<p class="text-warning">⚠ License tracker authentication plugin is not enabled</p>';
    $warnings++;
}

// Check if auth plugin files exist
$auth_files = array(
    'auth.php',
    'config.html',
    'lang/en/auth_licensetracker.php'
);

foreach ($auth_files as $file) {
    if (file_exists($CFG->dirroot . '/auth/licensetracker/' . $file)) {
        echo '<p class="text-success">✓ Auth plugin file exists: ' . $file . '</p>';
        $passed++;
    } else {
        echo '<p class="text-danger">✗ Auth plugin file missing: ' . $file . '</p>';
        $failed++;
    }
}

echo '</div></div>';

// Test 7: Admin tool plugin
echo '<div class="card mb-4">';
echo '<div class="card-header"><h4>Admin Tool Plugin Tests</h4></div>';
echo '<div class="card-body">';

$admin_tool_files = array(
    'version.php',
    'classes/external/licensetracker_api.php',
    'lang/en/tool_licensetrackeradmin.php'
);

foreach ($admin_tool_files as $file) {
    if (file_exists($CFG->dirroot . '/admin/tool/licensetrackeradmin/' . $file)) {
        echo '<p class="text-success">✓ Admin tool file exists: ' . $file . '</p>';
        $passed++;
    } else {
        echo '<p class="text-danger">✗ Admin tool file missing: ' . $file . '</p>';
        $failed++;
    }
}

echo '</div></div>';

// Test 8: Security features
echo '<div class="card mb-4">';
echo '<div class="card-header"><h4>Security Tests</h4></div>';
echo '<div class="card-body">';

// Check if security manager class exists
if (class_exists('local_licensetracker\security\license_security_manager')) {
    echo '<p class="text-success">✓ Security manager class exists</p>';
    $passed++;
    
    // Test security validation
    $security_manager = new \local_licensetracker\security\license_security_manager();
    $format_test = $security_manager::validate_key_format('TEST-1234-ABCD');
    if ($format_test['valid']) {
        echo '<p class="text-success">✓ Key format validation works</p>';
        $passed++;
    } else {
        echo '<p class="text-warning">⚠ Key format validation issue: ' . $format_test['reason'] . '</p>';
        $warnings++;
    }
} else {
    echo '<p class="text-danger">✗ Security manager class missing</p>';
    $failed++;
}

echo '</div></div>';

// Test 9: Message providers
echo '<div class="card mb-4">';
echo '<div class="card-header"><h4>Message Provider Tests</h4></div>';
echo '<div class="card-body">';

$message_providers = get_message_providers_for_component('local_licensetracker');
if (!empty($message_providers)) {
    echo '<p class="text-success">✓ Found ' . count($message_providers) . ' message providers</p>';
    $passed++;
    
    $expected_providers = array('keygeneration', 'licenserequest', 'lowstock', 'keyexpiry', 'securityalert');
    foreach ($expected_providers as $provider) {
        if (isset($message_providers[$provider])) {
            echo '<p class="text-success">✓ Message provider exists: ' . $provider . '</p>';
            $passed++;
        } else {
            echo '<p class="text-warning">⚠ Message provider missing: ' . $provider . '</p>';
            $warnings++;
        }
    }
} else {
    echo '<p class="text-warning">⚠ No message providers found</p>';
    $warnings++;
}

echo '</div></div>';

// Summary
echo '<div class="card border-primary">';
echo '<div class="card-header bg-primary text-white"><h4>Test Summary</h4></div>';
echo '<div class="card-body">';

$total = $passed + $failed + $warnings;
echo '<div class="row">';
echo '<div class="col-md-4 text-center">';
echo '<h3 class="text-success">' . $passed . '</h3>';
echo '<p>Passed</p>';
echo '</div>';
echo '<div class="col-md-4 text-center">';
echo '<h3 class="text-warning">' . $warnings . '</h3>';
echo '<p>Warnings</p>';
echo '</div>';
echo '<div class="col-md-4 text-center">';
echo '<h3 class="text-danger">' . $failed . '</h3>';
echo '<p>Failed</p>';
echo '</div>';
echo '</div>';

if ($failed == 0) {
    if ($warnings == 0) {
        echo '<div class="alert alert-success">';
        echo '<h5>🎉 All tests passed!</h5>';
        echo '<p>The License Tracker system is fully functional and ready for production use.</p>';
        echo '</div>';
    } else {
        echo '<div class="alert alert-warning">';
        echo '<h5>⚠️ Tests passed with warnings</h5>';
        echo '<p>The system is functional but some optional features may need attention.</p>';
        echo '</div>';
    }
} else {
    echo '<div class="alert alert-danger">';
    echo '<h5>❌ Some tests failed</h5>';
    echo '<p>Please address the failed tests before using the system in production.</p>';
    echo '</div>';
}

echo '</div></div>';

echo '</div>';

echo $OUTPUT->footer();
