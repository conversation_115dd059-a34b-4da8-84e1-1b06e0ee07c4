<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Bulk import partners tab for tool_licensetrackeradmin plugin.
 *
 * @package    tool_licensetrackeradmin
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

require_capability('tool/licensetrackeradmin:bulkimport', $context);

echo '<div class="card">';
echo '<div class="card-header">';
echo '<h3>' . get_string('bulkimportpartners', 'tool_licensetrackeradmin') . '</h3>';
echo '</div>';
echo '<div class="card-body">';

echo '<div class="alert alert-info">';
echo '<h5>' . get_string('csvformat', 'tool_licensetrackeradmin') . '</h5>';
echo '<p>' . get_string('csvheader_partners', 'tool_licensetrackeradmin') . '</p>';
echo '<ul>';
echo '<li><strong>partnername</strong>: Name of the partner (required)</li>';
echo '<li><strong>contactemail</strong>: Contact email address (required)</li>';
echo '<li><strong>country</strong>: Country code (optional)</li>';
echo '<li><strong>moodleusername</strong>: Moodle username to link (optional)</li>';
echo '<li><strong>coursetypes</strong>: Course types with prices in format "Type1:Price1;Type2:Price2" (optional)</li>';
echo '</ul>';
echo '</div>';

echo '<div class="alert alert-warning">';
echo '<p><strong>Note:</strong> This feature is available but requires additional implementation for partner import processing.</p>';
echo '</div>';

echo '</div>';
echo '</div>';
