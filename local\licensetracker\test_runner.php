<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Automated test runner for License Tracker system.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once(__DIR__ . '/../../config.php');
require_once($CFG->dirroot . '/local/licensetracker/lib.php');

require_login();

$context = context_system::instance();
$PAGE->set_context($context);
$PAGE->set_url('/local/licensetracker/test_runner.php');
$PAGE->set_title('License Tracker - Automated Tests');
$PAGE->set_heading('License Tracker - Automated Tests');

// Require admin permissions
require_capability('moodle/site:config', $context);

$PAGE->set_pagelayout('admin');

// Test runner class
class LicenseTrackerTestRunner {
    private $results = array();
    private $db;
    
    public function __construct() {
        global $DB;
        $this->db = $DB;
    }
    
    public function runAllTests() {
        echo '<div class="card">';
        echo '<div class="card-header bg-primary text-white">';
        echo '<h3><i class="fa fa-flask"></i> Automated Test Results</h3>';
        echo '</div>';
        echo '<div class="card-body">';
        
        $this->testDatabaseIntegrity();
        $this->testConfigurationValidity();
        $this->testCapabilityDefinitions();
        $this->testSecurityMeasures();
        $this->testPerformance();
        
        $this->displaySummary();
        
        echo '</div>';
        echo '</div>';
    }
    
    private function testDatabaseIntegrity() {
        echo '<h4>🗄️ Database Integrity Tests</h4>';
        
        // Test 1: All required tables exist
        $tables = array('local_lt_keys', 'local_lt_partners', 'local_lt_coursetypes', 'local_lt_partner_coursetypes');
        $missing_tables = array();
        
        foreach ($tables as $table) {
            if (!$this->db->get_manager()->table_exists($table)) {
                $missing_tables[] = $table;
            }
        }
        
        $this->recordTest('Required tables exist', empty($missing_tables), 
                         empty($missing_tables) ? 'All tables present' : 'Missing: ' . implode(', ', $missing_tables));
        
        // Test 2: Check for orphaned records
        $orphaned_keys = $this->db->count_records_sql("
            SELECT COUNT(*) FROM {local_lt_keys} k 
            LEFT JOIN {local_lt_partners} p ON k.partnerid = p.id 
            WHERE p.id IS NULL
        ");
        
        $this->recordTest('No orphaned keys', $orphaned_keys == 0, 
                         $orphaned_keys == 0 ? 'No orphaned records' : $orphaned_keys . ' orphaned keys found');
        
        // Test 3: Valid key statuses
        $invalid_statuses = $this->db->count_records_sql("
            SELECT COUNT(*) FROM {local_lt_keys} 
            WHERE status NOT IN ('available', 'used', 'revoked', 'expired')
        ");
        
        $this->recordTest('Valid key statuses', $invalid_statuses == 0,
                         $invalid_statuses == 0 ? 'All statuses valid' : $invalid_statuses . ' invalid statuses');
        
        // Test 4: Unique key strings
        $duplicate_keys = $this->db->get_records_sql("
            SELECT keystring, COUNT(*) as count FROM {local_lt_keys} 
            GROUP BY keystring HAVING COUNT(*) > 1
        ");
        
        $this->recordTest('Unique key strings', empty($duplicate_keys),
                         empty($duplicate_keys) ? 'All keys unique' : count($duplicate_keys) . ' duplicate keys');
    }
    
    private function testConfigurationValidity() {
        echo '<h4>⚙️ Configuration Tests</h4>';
        
        $config = get_config('local_licensetracker');
        $authconfig = get_config('auth_licensetracker');
        
        // Test 1: Main plugin enabled
        $this->recordTest('Main plugin enabled', !empty($config->enabled),
                         !empty($config->enabled) ? 'Plugin enabled' : 'Plugin disabled');
        
        // Test 2: Auth plugin configured
        $this->recordTest('Auth plugin configured', !empty($authconfig->enabled),
                         !empty($authconfig->enabled) ? 'Auth enabled' : 'Auth disabled');
        
        // Test 3: Key format valid JSON
        $key_format_valid = true;
        if (!empty($config->key_format)) {
            $decoded = json_decode($config->key_format, true);
            $key_format_valid = ($decoded !== null);
        }
        
        $this->recordTest('Key format valid JSON', $key_format_valid,
                         $key_format_valid ? 'Valid JSON format' : 'Invalid JSON format');
        
        // Test 4: Local countries format
        $countries_valid = true;
        if (!empty($config->local_countries)) {
            $countries = explode(',', $config->local_countries);
            foreach ($countries as $country) {
                if (strlen(trim($country)) != 2) {
                    $countries_valid = false;
                    break;
                }
            }
        }
        
        $this->recordTest('Local countries format', $countries_valid,
                         $countries_valid ? 'Valid country codes' : 'Invalid country code format');
    }
    
    private function testCapabilityDefinitions() {
        echo '<h4>🔐 Capability Tests</h4>';
        
        $required_caps = array(
            'local/licensetracker:manageallkeys',
            'local/licensetracker:managecoursetypes',
            'local/licensetracker:managepartners',
            'local/licensetracker:viewpartnerkeys',
            'local/licensetracker:viewpartnerstats',
            'local/licensetracker:usekey'
        );
        
        $missing_caps = array();
        foreach ($required_caps as $cap) {
            if (!$this->db->record_exists('capabilities', array('name' => $cap))) {
                $missing_caps[] = $cap;
            }
        }
        
        $this->recordTest('All capabilities defined', empty($missing_caps),
                         empty($missing_caps) ? 'All capabilities present' : 'Missing: ' . count($missing_caps) . ' capabilities');
        
        // Test role assignments
        $manager_roles = $this->db->get_records('role', array('archetype' => 'manager'));
        $has_manager_roles = !empty($manager_roles);
        
        $this->recordTest('Manager roles exist', $has_manager_roles,
                         $has_manager_roles ? count($manager_roles) . ' manager roles found' : 'No manager roles');
    }
    
    private function testSecurityMeasures() {
        echo '<h4>🛡️ Security Tests</h4>';
        
        // Test 1: Admin files have capability checks
        $admin_files = array(
            'tabs/admin_keys.php',
            'tabs/admin_generatekeys.php',
            'tabs/admin_partners.php',
            'tabs/admin_coursetypes.php'
        );
        
        $unprotected_files = array();
        foreach ($admin_files as $file) {
            $filepath = $CFG->dirroot . '/local/licensetracker/' . $file;
            if (file_exists($filepath)) {
                $content = file_get_contents($filepath);
                if (strpos($content, 'require_capability') === false) {
                    $unprotected_files[] = $file;
                }
            }
        }
        
        $this->recordTest('Admin files protected', empty($unprotected_files),
                         empty($unprotected_files) ? 'All admin files protected' : count($unprotected_files) . ' unprotected files');
        
        // Test 2: Partner files have capability checks
        $partner_files = array(
            'tabs/partner_keys.php',
            'tabs/partner_statistics.php',
            'tabs/partner_register.php'
        );
        
        $unprotected_partner_files = array();
        foreach ($partner_files as $file) {
            $filepath = $CFG->dirroot . '/local/licensetracker/' . $file;
            if (file_exists($filepath)) {
                $content = file_get_contents($filepath);
                if (strpos($content, 'require_capability') === false) {
                    $unprotected_partner_files[] = $file;
                }
            }
        }
        
        $this->recordTest('Partner files protected', empty($unprotected_partner_files),
                         empty($unprotected_partner_files) ? 'All partner files protected' : count($unprotected_partner_files) . ' unprotected files');
    }
    
    private function testPerformance() {
        echo '<h4>⚡ Performance Tests</h4>';
        
        // Test 1: Key validation performance
        $start_time = microtime(true);
        $this->db->get_record('local_lt_keys', array('keystring' => 'TEST-PERFORMANCE-KEY', 'status' => 'available'));
        $key_validation_time = (microtime(true) - $start_time) * 1000;
        
        $this->recordTest('Key validation performance', $key_validation_time < 100,
                         sprintf('%.2f ms (target: <100ms)', $key_validation_time));
        
        // Test 2: Partner stats query performance
        $start_time = microtime(true);
        local_licensetracker_get_partner_stats();
        $stats_time = (microtime(true) - $start_time) * 1000;
        
        $this->recordTest('Statistics query performance', $stats_time < 200,
                         sprintf('%.2f ms (target: <200ms)', $stats_time));
        
        // Test 3: Database connection
        $start_time = microtime(true);
        $this->db->count_records('local_lt_keys');
        $db_time = (microtime(true) - $start_time) * 1000;
        
        $this->recordTest('Database connection performance', $db_time < 50,
                         sprintf('%.2f ms (target: <50ms)', $db_time));
    }
    
    private function recordTest($name, $passed, $details) {
        $this->results[] = array(
            'name' => $name,
            'passed' => $passed,
            'details' => $details
        );
        
        $icon = $passed ? '✅' : '❌';
        $class = $passed ? 'success' : 'danger';
        
        echo '<div class="alert alert-' . $class . '">';
        echo '<strong>' . $icon . ' ' . $name . '</strong><br>';
        echo '<small>' . $details . '</small>';
        echo '</div>';
    }
    
    private function displaySummary() {
        $total_tests = count($this->results);
        $passed_tests = array_sum(array_column($this->results, 'passed'));
        $failed_tests = $total_tests - $passed_tests;
        
        echo '<hr>';
        echo '<h4>📊 Test Summary</h4>';
        echo '<div class="row text-center">';
        echo '<div class="col-md-4">';
        echo '<h3 class="text-primary">' . $total_tests . '</h3>';
        echo '<small>Total Tests</small>';
        echo '</div>';
        echo '<div class="col-md-4">';
        echo '<h3 class="text-success">' . $passed_tests . '</h3>';
        echo '<small>Passed</small>';
        echo '</div>';
        echo '<div class="col-md-4">';
        echo '<h3 class="text-danger">' . $failed_tests . '</h3>';
        echo '<small>Failed</small>';
        echo '</div>';
        echo '</div>';
        
        if ($failed_tests == 0) {
            echo '<div class="alert alert-success mt-3">';
            echo '<h5>🎉 All Tests Passed!</h5>';
            echo '<p>Your License Tracker system is ready for production deployment.</p>';
            echo '</div>';
        } else {
            echo '<div class="alert alert-danger mt-3">';
            echo '<h5>⚠️ Some Tests Failed</h5>';
            echo '<p>Please review and fix the failed tests before deploying to production.</p>';
            echo '</div>';
        }
    }
}

echo $OUTPUT->header();

// Run the tests
$testRunner = new LicenseTrackerTestRunner();
$testRunner->runAllTests();

// Action buttons
echo '<div class="mt-4 text-center">';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/system_status.php" class="btn btn-primary">System Status</a>';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/security_audit.php" class="btn btn-secondary ml-2">Security Audit</a>';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php" class="btn btn-info ml-2">Dashboard</a>';
echo '</div>';

echo $OUTPUT->footer();
