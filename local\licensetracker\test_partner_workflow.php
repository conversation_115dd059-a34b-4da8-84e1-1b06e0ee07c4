<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Partner registration workflow test for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once('../../config.php');
require_once($CFG->dirroot . '/local/licensetracker/lib.php');

require_login();
require_capability('local/licensetracker:manageallkeys', context_system::instance());

$PAGE->set_url('/local/licensetracker/test_partner_workflow.php');
$PAGE->set_context(context_system::instance());
$PAGE->set_title('Partner Registration Workflow Test');
$PAGE->set_heading('Partner Registration Workflow Test');

echo $OUTPUT->header();

echo '<div class="container-fluid">';
echo '<h2>🧪 Partner Registration Workflow Test</h2>';
echo '<p class="lead">This test verifies the complete partner registration workflow.</p>';

$tests = array();
$passed = 0;
$failed = 0;

// Test 1: Check if user profile field exists
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Test 1: User Profile Field</h4></div>';
echo '<div class="card-body">';

$field = $DB->get_record('user_info_field', array('shortname' => 'usertype'));
if ($field) {
    echo '<p class="text-success">✅ User type profile field exists</p>';
    echo '<ul>';
    echo '<li><strong>Name:</strong> ' . $field->name . '</li>';
    echo '<li><strong>Type:</strong> ' . $field->datatype . '</li>';
    echo '<li><strong>Options:</strong> ' . str_replace("\n", ", ", $field->param1) . '</li>';
    echo '<li><strong>Default:</strong> ' . $field->param2 . '</li>';
    echo '<li><strong>Locked:</strong> ' . ($field->locked ? 'Yes' : 'No') . '</li>';
    echo '</ul>';
    $passed++;
} else {
    echo '<p class="text-danger">❌ User type profile field missing</p>';
    $failed++;
}

echo '</div>';
echo '</div>';

// Test 2: Test user type functions
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Test 2: User Type Functions</h4></div>';
echo '<div class="card-body">';

try {
    // Test with admin user (should default to local_student)
    $usertype = local_licensetracker_get_user_type($USER->id);
    echo '<p class="text-success">✅ Get user type function works</p>';
    echo '<p>Current user type: <strong>' . $usertype . '</strong></p>';
    
    // Test setting user type
    local_licensetracker_set_user_type($USER->id, 'local_student');
    $newtype = local_licensetracker_get_user_type($USER->id);
    
    if ($newtype === 'local_student') {
        echo '<p class="text-success">✅ Set user type function works</p>';
        $passed++;
    } else {
        echo '<p class="text-danger">❌ Set user type function failed</p>';
        $failed++;
    }
} catch (Exception $e) {
    echo '<p class="text-danger">❌ User type functions error: ' . $e->getMessage() . '</p>';
    $failed++;
}

echo '</div>';
echo '</div>';

// Test 3: Partner stock validation
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Test 3: Partner Stock Validation</h4></div>';
echo '<div class="card-body">';

$partners = $DB->get_records('local_lt_partners', null, '', '*', 0, 1);
if ($partners) {
    $partner = reset($partners);
    echo '<p class="text-info">Testing with partner: <strong>' . $partner->name . '</strong></p>';
    
    try {
        $stock = local_licensetracker_get_partner_stock($partner->id);
        echo '<p class="text-success">✅ Partner stock function works</p>';
        echo '<p>Stock data retrieved for ' . count($stock) . ' course types</p>';
        
        foreach ($stock as $coursestock) {
            echo '<p>• ' . $coursestock->name . ' (' . $coursestock->licenseprefix . '): ' . $coursestock->available_count . ' keys</p>';
        }
        $passed++;
    } catch (Exception $e) {
        echo '<p class="text-danger">❌ Partner stock validation error: ' . $e->getMessage() . '</p>';
        $failed++;
    }
} else {
    echo '<p class="text-warning">⚠️ No partners found - create a partner to test this functionality</p>';
}

echo '</div>';
echo '</div>';

// Test 4: License key validation
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Test 4: License Key Validation</h4></div>';
echo '<div class="card-body">';

$keys = $DB->get_records('local_lt_keys', array('status' => 'available'), '', '*', 0, 1);
if ($keys) {
    $key = reset($keys);
    echo '<p class="text-info">Testing with key: <strong>' . $key->keystring . '</strong></p>';
    
    try {
        $validkey = local_licensetracker_validate_key($key->keystring);
        if ($validkey) {
            echo '<p class="text-success">✅ License key validation works</p>';
            echo '<p>Key details:</p>';
            echo '<ul>';
            echo '<li><strong>Status:</strong> ' . $validkey->status . '</li>';
            echo '<li><strong>Partner ID:</strong> ' . $validkey->partnerid . '</li>';
            echo '<li><strong>Course Type ID:</strong> ' . $validkey->coursetypeid . '</li>';
            echo '</ul>';
            $passed++;
        } else {
            echo '<p class="text-danger">❌ License key validation failed</p>';
            $failed++;
        }
    } catch (Exception $e) {
        echo '<p class="text-danger">❌ License key validation error: ' . $e->getMessage() . '</p>';
        $failed++;
    }
} else {
    echo '<p class="text-warning">⚠️ No available license keys found - generate keys to test this functionality</p>';
}

echo '</div>';
echo '</div>';

// Test 5: Partner key usage validation
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Test 5: Partner Key Usage Validation</h4></div>';
echo '<div class="card-body">';

if ($partners && $keys) {
    $partner = reset($partners);
    $key = reset($keys);
    
    try {
        $validation = local_licensetracker_validate_partner_key_usage($partner->id, $key->keystring);
        
        if ($validation['valid']) {
            echo '<p class="text-success">✅ Partner key usage validation works</p>';
            echo '<p>Validation successful for partner: <strong>' . $partner->name . '</strong></p>';
            $passed++;
        } else {
            echo '<p class="text-info">ℹ️ Partner key usage validation works (validation failed as expected)</p>';
            echo '<p>Validation message: ' . $validation['message'] . '</p>';
            $passed++;
        }
    } catch (Exception $e) {
        echo '<p class="text-danger">❌ Partner key usage validation error: ' . $e->getMessage() . '</p>';
        $failed++;
    }
} else {
    echo '<p class="text-warning">⚠️ Need both partners and keys to test this functionality</p>';
}

echo '</div>';
echo '</div>';

// Test Summary
echo '<div class="card mt-4">';
echo '<div class="card-header">';
echo '<h3>Test Summary</h3>';
echo '</div>';
echo '<div class="card-body">';

$total = $passed + $failed;
$percentage = $total > 0 ? round(($passed / $total) * 100, 1) : 0;

if ($failed == 0) {
    echo '<div class="alert alert-success">';
    echo '<h4><i class="fa fa-check-circle"></i> All Tests Passed!</h4>';
    echo '<p>Partner registration workflow is working correctly.</p>';
} else {
    echo '<div class="alert alert-' . ($passed > $failed ? 'warning' : 'danger') . '">';
    echo '<h4><i class="fa fa-exclamation-triangle"></i> Some Tests Failed</h4>';
    echo '<p>Please review the failed tests above.</p>';
}

echo '<p><strong>Results:</strong> ' . $passed . ' passed, ' . $failed . ' failed (' . $percentage . '% success rate)</p>';
echo '</div>';

echo '<div class="mt-3">';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/" class="btn btn-primary">Go to License Tracker</a> ';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/test_partner_workflow.php" class="btn btn-secondary">Re-run Tests</a>';
echo '</div>';

echo '</div>';
echo '</div>';

echo '</div>';

echo $OUTPUT->footer();
