# 🤝 **PARTNER USER MANUAL - FRANCHISE PARTNER DASHBOARD**

## 👋 **WELCOME FRANCHISE PARTNERS!**

This system allows you to:
- ✅ **Register Students** using your allocated license keys
- ✅ **Bulk Register** multiple students via CSV upload
- ✅ **Monitor Your Stock** of available license keys
- ✅ **View Statistics** of your student registrations
- ✅ **Track Usage** by course type

**IMPORTANT**: You can only register `partner_student` users with valid license keys from your allocation.

---

## 🔐 **ACCESSING YOUR DASHBOARD**

### **Login Process**
1. **Go to**: Your Moodle site login page
2. **Login** with your assigned partner account
3. **Navigate to**: `/local/licensetracker/`
4. **You'll see** your partner dashboard with available tabs

### **Required Permissions**
Your account must have the **System Partner** role with these capabilities:
- ✅ `local/licensetracker:viewpartnerkeys`
- ✅ `local/licensetracker:usekey`
- ✅ `local/licensetracker:viewpartnerstats`

---

## 📊 **ASSIGNED KEYS TAB**

### **Your License Key Inventory**
This tab shows all license keys allocated to your organization:

**Key Information Displayed:**
- **License Key** - The actual key string (e.g., CHCSS-1234-ABCD-5678)
- **Course Type** - Which course this key is for
- **Status** - Available, Used, Revoked, or Expired
- **Valid From/To** - Key validity period
- **Used By** - Student who used the key (if applicable)
- **Date Used** - When the key was consumed

### **Filtering Your Keys**
Use the filter options to find specific keys:
- **Course Type** - Filter by specific course
- **Status** - Show only available, used, etc.
- **Search** - Find specific key strings

### **Key Status Meanings**
- 🟢 **Available** - Ready to use for student registration
- 🔵 **Used** - Already consumed by a student
- 🔴 **Revoked** - Disabled by administrator
- ⚫ **Expired** - Past expiration date

---

## 📈 **STATISTICS TAB**

### **Your Performance Dashboard**
View comprehensive statistics about your license key usage:

**Overview Cards:**
- **Total Keys** - All keys allocated to you
- **Available Keys** - Keys ready for use
- **Used Keys** - Keys already consumed
- **Success Rate** - Percentage of keys used

### **Course Type Breakdown**
See your stock levels by course type:
- **Course Name** and license prefix
- **Total/Available/Used** keys per course
- **Stock Status** with visual indicators:
  - 🟢 **Good Stock** - 10+ keys available
  - 🟡 **Low Stock** - 5-10 keys available
  - 🔴 **Critical Stock** - Less than 5 keys available

### **Recent Activity**
Track your latest student registrations:
- **Student Name** and email
- **Course Type** registered for
- **Registration Date**
- **License Key** used

---

## 👤 **REGISTER NEW STUDENT TAB**

### **Individual Student Registration**
Register one student at a time using this form:

**Required Information:**
1. **Username** - Unique username (letters, numbers, dots, underscores, hyphens)
2. **First Name** - Student's first name
3. **Last Name** - Student's last name
4. **Email** - Valid email address (must be unique)
5. **Password** - Initial password (minimum 8 characters)
6. **License Key** - Valid key from your allocation

**Optional Information:**
- **City** - Student's city
- **Country** - Student's country

### **Registration Process**
1. **Fill out the form** with student information
2. **Enter a valid license key** from your available stock
3. **Click "Register User"**
4. **System validates** the key belongs to you and is available
5. **Student account created** and enrolled in the course
6. **License key marked as used**

### **Important Notes**
- ⚠️ **You can only use YOUR license keys**
- ⚠️ **Keys can only be used once**
- ⚠️ **Students are automatically enrolled** in the associated course
- ⚠️ **All students you register are marked as "partner_student" type**

---

## 📋 **BULK REGISTER STUDENTS TAB**

### **CSV Upload Registration**
Register multiple students at once using a CSV file:

**CSV File Requirements:**
Your CSV file must include these columns (in any order):
- **username** - Unique username for the student
- **firstname** - Student's first name
- **lastname** - Student's last name
- **email** - Student's email address (must be unique)
- **password** - Initial password for the student
- **licensekey** - Valid license key from your allocation
- **city** - Student's city (optional)
- **country** - Student's country (optional)

### **CSV Format Example**
```csv
username,firstname,lastname,email,password,licensekey,city,country
student1,John,Doe,<EMAIL>,password123,CHCSS-1234-ABCD,New York,US
student2,Jane,Smith,<EMAIL>,password456,CHCSS-5678-EFGH,London,GB
student3,Bob,Johnson,<EMAIL>,password789,AI-9876-IJKL,Toronto,CA
```

### **Bulk Registration Process**
1. **Prepare your CSV file** with student data
2. **Upload the file** using the file picker
3. **Configure processing options**:
   - **Continue on errors** - Keep processing even if some fail
   - **Maximum records** - Limit batch size (1-1000)
4. **Click "Process Bulk Registration"**
5. **Review results** showing success/failure for each student

### **Bulk Registration Results**
After processing, you'll see:
- **Success Count** - Students successfully registered
- **Error Count** - Failed registrations
- **Detailed Results** - Line-by-line success/failure messages

---

## 🚨 **STOCK ALERTS AND WARNINGS**

### **Stock Level Indicators**
The system provides visual warnings about your license key stock:

**Critical Stock Alert (Red):**
- **2 or fewer keys** remaining for individual registration
- **5 or fewer keys** remaining for bulk registration
- **Action Required**: Contact administrator immediately

**Low Stock Warning (Yellow):**
- **5 or fewer keys** remaining for individual registration
- **10 or fewer keys** remaining for bulk registration
- **Action Recommended**: Order more licenses soon

**Good Stock (Green):**
- **Sufficient keys** available for registration
- **No action needed**

### **Out of Stock**
When you have no available keys:
- ❌ **Registration is blocked**
- ❌ **Forms are disabled**
- 📞 **Contact your administrator** to purchase more licenses

---

## ❌ **ERROR HANDLING**

### **Common Registration Errors**
**"Username already exists"**
- Choose a different username
- Check if student is already registered

**"Email already exists"**
- Use a different email address
- Verify student isn't already in system

**"Invalid license key"**
- Check key spelling and format
- Ensure key is from your allocation
- Verify key hasn't been used already

**"You have no available license keys for this course type"**
- Check your stock levels
- Contact administrator for more keys

### **Bulk Registration Errors**
**"Invalid CSV format"**
- Check column headers match requirements
- Ensure all required columns are present
- Verify file is saved as CSV format

**"Missing required CSV columns"**
- Add missing columns to your CSV
- Check column name spelling

---

## 📞 **GETTING HELP**

### **Self-Service Resources**
- **Statistics Tab** - Monitor your usage and stock
- **Key Inventory** - Check available keys
- **Error Messages** - Detailed validation feedback

### **When to Contact Administrator**
- 🔴 **Out of stock** - Need more license keys
- 🔴 **Technical issues** - System not working properly
- 🔴 **Account problems** - Can't access dashboard
- 🔴 **Bulk registration failures** - Large-scale issues

### **What Information to Provide**
When contacting support, include:
- **Your partner organization name**
- **Specific error messages**
- **Student information** (if registration-related)
- **License keys involved** (if applicable)
- **Screenshots** of any errors

---

## 🎯 **BEST PRACTICES**

### **License Key Management**
- ✅ **Monitor stock levels** regularly
- ✅ **Order new keys** before running out
- ✅ **Keep track** of key usage
- ✅ **Don't share keys** with other partners

### **Student Registration**
- ✅ **Verify student information** before submitting
- ✅ **Use strong passwords** for initial setup
- ✅ **Test bulk uploads** with small batches first
- ✅ **Keep records** of registered students

### **CSV File Preparation**
- ✅ **Use proper CSV format** (comma-separated)
- ✅ **Include all required columns**
- ✅ **Check for duplicate usernames/emails**
- ✅ **Validate license keys** before upload

### **Security**
- ✅ **Keep login credentials secure**
- ✅ **Don't share your partner account**
- ✅ **Log out** when finished
- ✅ **Report suspicious activity**

---

## 🎉 **SUCCESS!**

You're now ready to use the franchise partner system to register students and manage your license key allocation. The system provides complete separation between partners and ensures only valid license keys can be used for student registration.

**Remember**: You can only register `partner_student` users with valid license keys from your allocation. The system prevents any unauthorized registrations and maintains complete data separation between partners.

**Happy registering!** 🚀
