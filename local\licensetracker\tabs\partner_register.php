<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Partner student registration tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

// CRITICAL: Verify partner permissions
$context = context_system::instance();
require_capability('local/licensetracker:usekey', $context);

// Ensure partner can only register with their own keys
if (!$partner) {
    throw new moodle_exception('nopermissions', 'error', '', 'Partner access required');
}

$form = new \local_licensetracker\form\student_registration_form();

if ($form->is_cancelled()) {
    redirect(new moodle_url('/local/licensetracker/index.php', array('tab' => 'register')));
} else if ($data = $form->get_data()) {
    
    // Validate license key and partner stock
    $validation = local_licensetracker_validate_partner_key_usage($partner->id, $data->licensekey);
    if (!$validation['valid']) {
        redirect($PAGE->url, $validation['message'], null, \core\output\notification::NOTIFY_ERROR);
    }

    $key = $validation['key'];
    
    // Get course type information
    $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $key->coursetypeid));
    if (!$coursetype) {
        redirect($PAGE->url, get_string('error:coursetypenotfound', 'local_licensetracker'), 
                null, \core\output\notification::NOTIFY_ERROR);
    }
    
    try {
        // Create user account - Partners can ONLY create partner_student users
        $newuser = new stdClass();
        $newuser->username = $data->username;
        $newuser->firstname = $data->firstname;
        $newuser->lastname = $data->lastname;
        $newuser->email = $data->email;
        $newuser->password = hash_internal_user_password($data->password);
        $newuser->city = $data->city;
        $newuser->country = $data->country;
        $newuser->confirmed = 1;
        $newuser->auth = 'manual';
        $newuser->mnethostid = $CFG->mnet_localhost_id;
        $newuser->timecreated = time();
        $newuser->timemodified = time();

        // Set language from partner if available
        if (!empty($partner->lang)) {
            $newuser->lang = $partner->lang;
        }

        $userid = user_create_user($newuser, false, false);

        // ENFORCE: Partners can only create partner_student users
        local_licensetracker_set_user_type($userid, 'partner_student');
        
        if (!$userid) {
            throw new Exception('Failed to create user account');
        }
        
        // Update license key status
        $ipaddress = getremoteaddr();
        $useragent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        local_licensetracker_update_key_status($key->id, 'used', $userid, $ipaddress, $useragent);
        
        // Enroll user in course
        $course = $DB->get_record('course', array('id' => $coursetype->moodlecourseid));
        if ($course) {
            $enrolmethod = $coursetype->enrolmethod ?: 'manual';
            $roleid = $coursetype->defaultroleid ?: 5; // Default to student role
            
            // Get enrollment plugin
            $enrol = enrol_get_plugin($enrolmethod);
            if (!$enrol) {
                $enrol = enrol_get_plugin('manual'); // Fallback to manual
                $enrolmethod = 'manual';
            }
            
            // Get or create enrollment instance
            $instance = $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => $enrolmethod));
            if (!$instance) {
                // Create new instance if it doesn't exist
                $instanceid = $enrol->add_instance($course);
                $instance = $DB->get_record('enrol', array('id' => $instanceid));
            }
            
            // Enroll the user
            $enrol->enrol_user($instance, $userid, $roleid, time(), 0, ENROL_USER_ACTIVE);
        }
        
        $message = get_string('studentregistered', 'local_licensetracker');
        if ($course) {
            $message .= ' ' . get_string('enrollmentsuccess', 'auth_licensetracker', format_string($course->fullname));
        }
        
        redirect($PAGE->url, $message, null, \core\output\notification::NOTIFY_SUCCESS);
        
    } catch (Exception $e) {
        // Log the error for debugging
        error_log('License Tracker Registration Error: ' . $e->getMessage() . ' - Partner ID: ' . $partner->id);

        // Provide user-friendly error message
        $errormsg = get_string('registrationfailed', 'local_licensetracker');
        if (strpos($e->getMessage(), 'username') !== false) {
            $errormsg = get_string('error:usernameexists', 'local_licensetracker');
        } else if (strpos($e->getMessage(), 'email') !== false) {
            $errormsg = get_string('error:emailexists', 'local_licensetracker');
        } else if (strpos($e->getMessage(), 'password') !== false) {
            $errormsg = get_string('error:invalidpassword', 'local_licensetracker');
        }

        redirect($PAGE->url, $errormsg, null, \core\output\notification::NOTIFY_ERROR);
    }
}

$output = '';
$output .= '<div class="card">';
$output .= '<div class="card-header">';
$output .= '<h3>' . get_string('registernewstudent', 'local_licensetracker') . '</h3>';
$output .= '</div>';
$output .= '<div class="card-body">';

// Get partner stock by course type
$stockbycourse = local_licensetracker_get_partner_stock($partner->id);
$totalavailable = 0;

foreach ($stockbycourse as $stock) {
    $totalavailable += $stock->available_count;
}

if ($totalavailable == 0) {
    $output .= '<div class="alert alert-danger">';
    $output .= '<h5><i class="fa fa-exclamation-triangle"></i> Out of Stock</h5>';
    $output .= get_string('noavailablekeys', 'local_licensetracker');
    $output .= '<p class="mt-2">Please contact your administrator to purchase more license keys.</p>';
    $output .= '</div>';
} else {
    // Check for low stock warnings
    $lowstockwarnings = array();
    $criticalstockwarnings = array();

    foreach ($stockbycourse as $stock) {
        if ($stock->available_count > 0) {
            if ($stock->available_count <= 2) {
                $criticalstockwarnings[] = $stock->name . ' (' . $stock->available_count . ' keys remaining)';
            } else if ($stock->available_count <= 5) {
                $lowstockwarnings[] = $stock->name . ' (' . $stock->available_count . ' keys remaining)';
            }
        }
    }

    // Show critical stock warnings
    if (!empty($criticalstockwarnings)) {
        $output .= '<div class="alert alert-danger">';
        $output .= '<h5><i class="fa fa-exclamation-triangle"></i> Critical Stock Alert</h5>';
        $output .= '<p>The following course types are critically low on license keys:</p>';
        $output .= '<ul>';
        foreach ($criticalstockwarnings as $warning) {
            $output .= '<li>' . $warning . '</li>';
        }
        $output .= '</ul>';
        $output .= '<p><strong>Please contact your administrator immediately to purchase more licenses.</strong></p>';
        $output .= '</div>';
    }

    // Show low stock warnings
    if (!empty($lowstockwarnings)) {
        $output .= '<div class="alert alert-warning">';
        $output .= '<h5><i class="fa fa-exclamation-circle"></i> Low Stock Warning</h5>';
        $output .= '<p>The following course types are running low on license keys:</p>';
        $output .= '<ul>';
        foreach ($lowstockwarnings as $warning) {
            $output .= '<li>' . $warning . '</li>';
        }
        $output .= '</ul>';
        $output .= '<p>Consider ordering more licenses soon.</p>';
        $output .= '</div>';
    }

    // Show stock breakdown by course type
    $output .= '<div class="alert alert-info">';
    $output .= '<h5>Available License Keys by Course Type:</h5>';
    $output .= '<div class="row">';

    foreach ($stockbycourse as $stock) {
        if ($stock->available_count > 0) {
            $cardclass = 'card';
            $textclass = 'text-success';

            if ($stock->available_count <= 2) {
                $cardclass = 'card border-danger';
                $textclass = 'text-danger';
            } else if ($stock->available_count <= 5) {
                $cardclass = 'card border-warning';
                $textclass = 'text-warning';
            }

            $output .= '<div class="col-md-4 mb-2">';
            $output .= '<div class="' . $cardclass . '">';
            $output .= '<div class="card-body text-center">';
            $output .= '<span class="badge badge-primary">' . $stock->licenseprefix . '</span>';
            $output .= '<h6 class="mt-2">' . $stock->name . '</h6>';
            $output .= '<h4 class="' . $textclass . '">' . $stock->available_count . '</h4>';
            $output .= '<small class="text-muted">available keys</small>';
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</div>';
        }
    }

    $output .= '</div>';
    $output .= '<p class="mt-2"><strong>Total Available:</strong> ' . $totalavailable . ' license keys</p>';
    $output .= '</div>';

    $output .= $form->render();
}

$output .= '</div>';
$output .= '</div>';

return $output;
