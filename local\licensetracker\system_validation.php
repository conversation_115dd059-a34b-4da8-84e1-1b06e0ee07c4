<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * System validation script for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once(__DIR__ . '/../../config.php');
require_once($CFG->libdir . '/adminlib.php');

// Security checks
require_login();
$context = context_system::instance();
require_capability('local/licensetracker:manageallkeys', $context);

// Set up page
$PAGE->set_url('/local/licensetracker/system_validation.php');
$PAGE->set_context($context);
$PAGE->set_title('License System Validation');
$PAGE->set_heading('License System Validation');

echo $OUTPUT->header();

echo '<div class="container-fluid">';
echo '<h2>License System Validation Results</h2>';

$errors = array();
$warnings = array();
$success = array();

// 1. Check database tables
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Database Schema Validation</h4></div>';
echo '<div class="card-body">';

$tables = array(
    'local_lt_keys',
    'local_lt_partners', 
    'local_lt_coursetypes',
    'local_lt_partner_coursetypes'
);

foreach ($tables as $table) {
    if ($DB->get_manager()->table_exists($table)) {
        echo '<p class="text-success">✓ Table ' . $table . ' exists</p>';
        $success[] = "Table $table exists";
    } else {
        echo '<p class="text-danger">✗ Table ' . $table . ' missing</p>';
        $errors[] = "Table $table is missing";
    }
}

// Check for licenseprefix field in coursetypes table
$dbman = $DB->get_manager();
$table = new xmldb_table('local_lt_coursetypes');
$field = new xmldb_field('licenseprefix');

if ($dbman->field_exists($table, $field)) {
    echo '<p class="text-success">✓ License prefix field exists in course types table</p>';
    $success[] = "License prefix field exists";
} else {
    echo '<p class="text-warning">⚠ License prefix field missing - run database upgrade</p>';
    $warnings[] = "License prefix field missing";
}

echo '</div>';
echo '</div>';

// 2. Check course types have license prefixes
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Course Type Configuration</h4></div>';
echo '<div class="card-body">';

$coursetypes = $DB->get_records('local_lt_coursetypes');
if ($coursetypes) {
    foreach ($coursetypes as $coursetype) {
        if (!empty($coursetype->licenseprefix)) {
            echo '<p class="text-success">✓ Course type "' . $coursetype->name . '" has prefix: ' . $coursetype->licenseprefix . '</p>';
            $success[] = "Course type {$coursetype->name} has prefix";
        } else {
            echo '<p class="text-warning">⚠ Course type "' . $coursetype->name . '" missing license prefix</p>';
            $warnings[] = "Course type {$coursetype->name} missing prefix";
        }
    }
} else {
    echo '<p class="text-warning">⚠ No course types configured</p>';
    $warnings[] = "No course types configured";
}

echo '</div>';
echo '</div>';

// 3. Check license key format validation
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>License Key Validation</h4></div>';
echo '<div class="card-body">';

// Test key generation if we have course types
if ($coursetypes) {
    $testcoursetype = reset($coursetypes);
    try {
        $testkey = local_licensetracker_generate_unique_key($testcoursetype->id);
        if (!empty($testcoursetype->licenseprefix) && strpos($testkey, $testcoursetype->licenseprefix . '-') === 0) {
            echo '<p class="text-success">✓ License key generation works with course prefix: ' . $testkey . '</p>';
            $success[] = "License key generation with prefix works";
            
            // Clean up test key
            $DB->delete_records('local_lt_keys', array('keystring' => $testkey));
        } else {
            echo '<p class="text-warning">⚠ License key generation may not include proper prefix</p>';
            $warnings[] = "License key prefix validation needed";
        }
    } catch (Exception $e) {
        echo '<p class="text-danger">✗ License key generation failed: ' . $e->getMessage() . '</p>';
        $errors[] = "License key generation failed";
    }
}

echo '</div>';
echo '</div>';

// 4. Check authentication plugin
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Authentication Plugin Status</h4></div>';
echo '<div class="card-body">';

$authplugins = get_enabled_auth_plugins();
if (in_array('licensetracker', $authplugins)) {
    echo '<p class="text-success">✓ License Tracker authentication plugin is enabled</p>';
    $success[] = "Auth plugin enabled";
} else {
    echo '<p class="text-warning">⚠ License Tracker authentication plugin is not enabled</p>';
    $warnings[] = "Auth plugin not enabled";
}

// Check if self-registration is disabled
$registerauth = get_config('core', 'registerauth');
if (empty($registerauth)) {
    echo '<p class="text-success">✓ Self-registration is disabled (staff-only registration)</p>';
    $success[] = "Self-registration disabled";
} else {
    echo '<p class="text-warning">⚠ Self-registration is enabled - should be disabled for staff-only registration</p>';
    $warnings[] = "Self-registration should be disabled";
}

echo '</div>';
echo '</div>';

// 5. Check capabilities
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Capability Configuration</h4></div>';
echo '<div class="card-body">';

$capabilities = array(
    'local/licensetracker:manageallkeys',
    'local/licensetracker:viewpartnerkeys',
    'local/licensetracker:usekey'
);

foreach ($capabilities as $capability) {
    if ($DB->record_exists('capabilities', array('name' => $capability))) {
        echo '<p class="text-success">✓ Capability ' . $capability . ' is defined</p>';
        $success[] = "Capability $capability defined";
    } else {
        echo '<p class="text-danger">✗ Capability ' . $capability . ' is missing</p>';
        $errors[] = "Capability $capability missing";
    }
}

echo '</div>';
echo '</div>';

// 6. Check partner separation and stock management
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Partner Data Separation & Stock Management</h4></div>';
echo '<div class="card-body">';

$partners = $DB->get_records('local_lt_partners');
if ($partners) {
    echo '<p class="text-success">✓ ' . count($partners) . ' partners configured</p>';
    $success[] = count($partners) . " partners configured";

    // Check if partners have separate key allocations with course-type breakdown
    foreach ($partners as $partner) {
        $stockbycourse = local_licensetracker_get_partner_stock($partner->id);
        $totalstock = 0;
        foreach ($stockbycourse as $stock) {
            $totalstock += $stock->available_count;
        }

        if ($totalstock > 0) {
            echo '<p class="text-success">✓ Partner "' . $partner->name . '" has ' . $totalstock . ' available license keys</p>';
            $success[] = "Partner {$partner->name} has available stock";

            // Show course type breakdown
            foreach ($stockbycourse as $stock) {
                if ($stock->available_count > 0) {
                    echo '<p class="text-info ml-3">• ' . $stock->name . ' (' . $stock->licenseprefix . '): ' . $stock->available_count . ' keys</p>';
                }
            }
        } else {
            echo '<p class="text-warning">⚠ Partner "' . $partner->name . '" has no available license keys</p>';
            $warnings[] = "Partner {$partner->name} has no available stock";
        }
    }
} else {
    echo '<p class="text-warning">⚠ No partners configured</p>';
    $warnings[] = "No partners configured";
}

echo '</div>';
echo '</div>';

// 7. Check user profile field implementation
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>User Profile Field Implementation</h4></div>';
echo '<div class="card-body">';

$usertype_field = $DB->get_record('user_info_field', array('shortname' => 'usertype'));
if ($usertype_field) {
    echo '<p class="text-success">✓ User type profile field exists</p>';
    $success[] = "User type profile field configured";

    // Check if any users have the field set
    $partner_students = $DB->count_records('user_info_data', array('fieldid' => $usertype_field->id, 'data' => 'partner_student'));
    $local_students = $DB->count_records('user_info_data', array('fieldid' => $usertype_field->id, 'data' => 'local_student'));

    echo '<p class="text-info">• Partner students: ' . $partner_students . '</p>';
    echo '<p class="text-info">• Local students: ' . $local_students . '</p>';
} else {
    echo '<p class="text-warning">⚠ User type profile field not found - will be created on first use</p>';
    $warnings[] = "User type profile field not yet created";
}

echo '</div>';
echo '</div>';

// 8. Check API functionality
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>API Functionality</h4></div>';
echo '<div class="card-body">';

// Check if web services are enabled
if (empty($CFG->enablewebservices)) {
    echo '<p class="text-warning">⚠ Web services are disabled - API functionality will not work</p>';
    $warnings[] = "Web services disabled";
} else {
    echo '<p class="text-success">✓ Web services are enabled</p>';
    $success[] = "Web services enabled";

    // Check if license tracker admin tool is installed
    if (file_exists($CFG->dirroot . '/admin/tool/licensetrackeradmin/version.php')) {
        echo '<p class="text-success">✓ License Tracker Admin Tool is installed</p>';
        $success[] = "Admin tool installed";
    } else {
        echo '<p class="text-danger">✗ License Tracker Admin Tool is missing</p>';
        $errors[] = "Admin tool not installed";
    }
}

echo '</div>';
echo '</div>';

// 9. Check partner-course type assignments
echo '<div class="card mb-3">';
echo '<div class="card-header"><h4>Partner-Course Type Assignments</h4></div>';
echo '<div class="card-body">';

$unassignedpartners = $DB->get_records_sql(
    "SELECT p.id, p.name
     FROM {local_lt_partners} p
     LEFT JOIN {local_lt_partner_coursetypes} pct ON pct.partnerid = p.id
     WHERE pct.id IS NULL"
);

if (empty($unassignedpartners)) {
    echo '<p class="text-success">✓ All partners are assigned to course types</p>';
    $success[] = "All partners have course type assignments";
} else {
    echo '<p class="text-warning">⚠ ' . count($unassignedpartners) . ' partners have no course type assignments:</p>';
    echo '<ul>';
    foreach ($unassignedpartners as $partner) {
        echo '<li>' . $partner->name . '</li>';
    }
    echo '</ul>';
    $warnings[] = count($unassignedpartners) . " partners without course type assignments";
}

echo '</div>';
echo '</div>';

// Summary
echo '<div class="card">';
echo '<div class="card-header"><h4>Validation Summary</h4></div>';
echo '<div class="card-body">';

echo '<div class="row">';
echo '<div class="col-md-4">';
echo '<div class="alert alert-success">';
echo '<h5>✓ Success (' . count($success) . ')</h5>';
foreach ($success as $item) {
    echo '<small>• ' . $item . '</small><br>';
}
echo '</div>';
echo '</div>';

echo '<div class="col-md-4">';
echo '<div class="alert alert-warning">';
echo '<h5>⚠ Warnings (' . count($warnings) . ')</h5>';
foreach ($warnings as $item) {
    echo '<small>• ' . $item . '</small><br>';
}
echo '</div>';
echo '</div>';

echo '<div class="col-md-4">';
echo '<div class="alert alert-danger">';
echo '<h5>✗ Errors (' . count($errors) . ')</h5>';
foreach ($errors as $item) {
    echo '<small>• ' . $item . '</small><br>';
}
echo '</div>';
echo '</div>';
echo '</div>';

if (count($errors) == 0 && count($warnings) == 0) {
    echo '<div class="alert alert-success">';
    echo '<h4>🎉 System Validation Passed!</h4>';
    echo '<p>Your license system is properly configured and ready for production use.</p>';
    echo '</div>';
} else if (count($errors) == 0) {
    echo '<div class="alert alert-warning">';
    echo '<h4>⚠ System Validation Completed with Warnings</h4>';
    echo '<p>Your system is functional but some optimizations are recommended.</p>';
    echo '</div>';
} else {
    echo '<div class="alert alert-danger">';
    echo '<h4>❌ System Validation Failed</h4>';
    echo '<p>Critical issues found that need to be resolved before production use.</p>';
    echo '</div>';
}

echo '</div>';
echo '</div>';

echo '<div class="mt-3">';
echo '<a href="' . $CFG->wwwroot . '/local/licensetracker/" class="btn btn-primary">Back to Dashboard</a>';
echo '</div>';

echo '</div>';

echo $OUTPUT->footer();
