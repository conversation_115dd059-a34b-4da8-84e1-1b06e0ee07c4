<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Activity logs tab for tool_licensetrackeradmin plugin.
 *
 * @package    tool_licensetrackeradmin
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

require_capability('tool/licensetrackeradmin:viewlogs', $context);

// Get filter parameters
$eventtype = optional_param('eventtype', '', PARAM_ALPHA);
$actor = optional_param('actor', 0, PARAM_INT);
$datefrom = optional_param('datefrom', 0, PARAM_INT);
$dateto = optional_param('dateto', 0, PARAM_INT);

// Build query for license key activity logs
$sql = "SELECT k.id, k.keystring, k.status, k.timecreated, k.timemodified, k.dateused,
               k.ipaddressused, k.useragent, k.usedbyuserid,
               p.partnername, ct.name as coursetypename,
               u.firstname, u.lastname, u.email
        FROM {local_lt_keys} k
        LEFT JOIN {local_lt_partners} p ON k.partnerid = p.id
        LEFT JOIN {local_lt_coursetypes} ct ON k.coursetypeid = ct.id
        LEFT JOIN {user} u ON k.usedbyuserid = u.id
        WHERE 1=1";

$params = array();

// Apply filters
if ($eventtype) {
    switch ($eventtype) {
        case 'used':
            $sql .= " AND k.status = 'used'";
            break;
        case 'revoked':
            $sql .= " AND k.status = 'revoked'";
            break;
        case 'created':
            // Show recently created keys
            $sql .= " AND k.timecreated > " . (time() - (30 * 24 * 60 * 60)); // Last 30 days
            break;
    }
}

if ($actor) {
    $sql .= " AND k.usedbyuserid = :actor";
    $params['actor'] = $actor;
}

if ($datefrom) {
    $sql .= " AND k.timemodified >= :datefrom";
    $params['datefrom'] = $datefrom;
}

if ($dateto) {
    $sql .= " AND k.timemodified <= :dateto";
    $params['dateto'] = $dateto;
}

$sql .= " ORDER BY k.timemodified DESC LIMIT 100";

$logs = $DB->get_records_sql($sql, $params);

echo '<div class="card">';
echo '<div class="card-header">';
echo '<h3>' . get_string('activitylogs', 'tool_licensetrackeradmin') . '</h3>';
echo '</div>';
echo '<div class="card-body">';

// Filter form
echo '<div class="card mb-3">';
echo '<div class="card-header">' . get_string('filters', 'core') . '</div>';
echo '<div class="card-body">';
echo '<form method="get" action="">';
echo '<input type="hidden" name="tab" value="activitylogs">';
echo '<div class="row">';

// Event type filter
echo '<div class="col-md-3">';
echo '<label for="eventtype">' . get_string('eventtype', 'tool_licensetrackeradmin') . '</label>';
echo '<select name="eventtype" id="eventtype" class="form-control">';
echo '<option value="">' . get_string('all') . '</option>';
echo '<option value="created"' . ($eventtype == 'created' ? ' selected' : '') . '>' . get_string('event_keycreated', 'tool_licensetrackeradmin') . '</option>';
echo '<option value="used"' . ($eventtype == 'used' ? ' selected' : '') . '>' . get_string('event_keyused', 'tool_licensetrackeradmin') . '</option>';
echo '<option value="revoked"' . ($eventtype == 'revoked' ? ' selected' : '') . '>' . get_string('event_keyrevoked', 'tool_licensetrackeradmin') . '</option>';
echo '</select>';
echo '</div>';

// Date filters
echo '<div class="col-md-3">';
echo '<label for="datefrom">' . get_string('datefrom', 'tool_licensetrackeradmin') . '</label>';
echo '<input type="date" name="datefrom" id="datefrom" class="form-control" value="' . ($datefrom ? date('Y-m-d', $datefrom) : '') . '">';
echo '</div>';

echo '<div class="col-md-3">';
echo '<label for="dateto">' . get_string('dateto', 'tool_licensetrackeradmin') . '</label>';
echo '<input type="date" name="dateto" id="dateto" class="form-control" value="' . ($dateto ? date('Y-m-d', $dateto) : '') . '">';
echo '</div>';

echo '<div class="col-md-3 d-flex align-items-end">';
echo '<button type="submit" class="btn btn-primary mr-2">' . get_string('filter', 'core') . '</button>';
echo '<a href="' . $CFG->wwwroot . '/admin/tool/licensetrackeradmin/index.php?tab=activitylogs" class="btn btn-secondary">' . get_string('clear', 'core') . '</a>';
echo '</div>';

echo '</div>';
echo '</form>';
echo '</div>';
echo '</div>';

// Logs table
if (!empty($logs)) {
    echo '<div class="table-responsive">';
    echo '<table class="table table-striped table-hover">';
    echo '<thead class="thead-dark">';
    echo '<tr>';
    echo '<th>' . get_string('timestamp', 'tool_licensetrackeradmin') . '</th>';
    echo '<th>' . get_string('eventtype', 'tool_licensetrackeradmin') . '</th>';
    echo '<th>' . get_string('keystring', 'local_licensetracker') . '</th>';
    echo '<th>' . get_string('partner', 'local_licensetracker') . '</th>';
    echo '<th>' . get_string('coursetype', 'local_licensetracker') . '</th>';
    echo '<th>' . get_string('actor', 'tool_licensetrackeradmin') . '</th>';
    echo '<th>' . get_string('ipaddress', 'tool_licensetrackeradmin') . '</th>';
    echo '<th>' . get_string('details', 'tool_licensetrackeradmin') . '</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';
    
    foreach ($logs as $log) {
        echo '<tr>';
        echo '<td>' . userdate($log->timemodified) . '</td>';
        
        // Determine event type
        $event = 'Unknown';
        if ($log->status == 'used' && $log->dateused) {
            $event = get_string('event_keyused', 'tool_licensetrackeradmin');
        } else if ($log->status == 'revoked') {
            $event = get_string('event_keyrevoked', 'tool_licensetrackeradmin');
        } else if ($log->timecreated == $log->timemodified) {
            $event = get_string('event_keycreated', 'tool_licensetrackeradmin');
        }
        
        echo '<td>' . $event . '</td>';
        echo '<td><code>' . $log->keystring . '</code></td>';
        echo '<td>' . $log->partnername . '</td>';
        echo '<td>' . $log->coursetypename . '</td>';
        echo '<td>' . ($log->firstname ? $log->firstname . ' ' . $log->lastname : 'System') . '</td>';
        echo '<td>' . ($log->ipaddressused ?: 'N/A') . '</td>';
        echo '<td>';
        if ($log->useragent) {
            echo '<small>' . substr($log->useragent, 0, 50) . '...</small>';
        } else {
            echo 'N/A';
        }
        echo '</td>';
        echo '</tr>';
    }
    
    echo '</tbody>';
    echo '</table>';
    echo '</div>';
} else {
    echo '<div class="alert alert-info">';
    echo 'No activity logs found for the selected criteria.';
    echo '</div>';
}

echo '</div>';
echo '</div>';
