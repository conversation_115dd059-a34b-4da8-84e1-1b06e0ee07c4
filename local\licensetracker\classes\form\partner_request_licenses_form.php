<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Partner request more licenses form for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\form;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/formslib.php');

/**
 * Form for partners to request more license keys.
 */
class partner_request_licenses_form extends \moodleform {

    /**
     * Define the form.
     */
    public function definition() {
        global $DB;

        $mform = $this->_form;
        $partner = $this->_customdata['partner'];

        // Get available course types for this partner
        $sql = "SELECT ct.id, ct.name, ct.description, ct.defaultprice,
                       COALESCE(pct.price, ct.defaultprice) as partnerprice
                FROM {local_lt_coursetypes} ct
                LEFT JOIN {local_lt_partner_coursetypes} pct ON ct.id = pct.coursetypeid AND pct.partnerid = ?
                WHERE ct.id IN (
                    SELECT DISTINCT pct2.coursetypeid 
                    FROM {local_lt_partner_coursetypes} pct2 
                    WHERE pct2.partnerid = ?
                )
                ORDER BY ct.name";
        $coursetypes = $DB->get_records_sql($sql, array($partner->id, $partner->id));

        if (empty($coursetypes)) {
            $mform->addElement('static', 'nocoursetypes', '', 
                get_string('nocoursetypesassigned', 'local_licensetracker'));
            return;
        }

        // Header
        $mform->addElement('header', 'requestheader', get_string('requestmorelicenses', 'local_licensetracker'));
        
        // Partner information (read-only)
        $mform->addElement('static', 'partnername', get_string('partnername', 'local_licensetracker'), 
                          $partner->partnername);
        $mform->addElement('static', 'contactemail', get_string('contactemail', 'local_licensetracker'), 
                          $partner->contactemail);

        // Course type selection
        $coursetypeoptions = array();
        foreach ($coursetypes as $ct) {
            $price = number_format($ct->partnerprice, 2);
            $coursetypeoptions[$ct->id] = $ct->name . ' (' . get_string('price', 'local_licensetracker') . ': $' . $price . ')';
        }
        
        $mform->addElement('select', 'coursetypeid', get_string('coursetype', 'local_licensetracker'), 
                          $coursetypeoptions);
        $mform->addRule('coursetypeid', null, 'required', null, 'client');
        $mform->addHelpButton('coursetypeid', 'coursetype_request', 'local_licensetracker');

        // Quantity requested
        $mform->addElement('text', 'quantity', get_string('quantityrequested', 'local_licensetracker'));
        $mform->setType('quantity', PARAM_INT);
        $mform->addRule('quantity', null, 'required', null, 'client');
        $mform->addRule('quantity', null, 'numeric', null, 'client');
        $mform->setDefault('quantity', 10);
        $mform->addHelpButton('quantity', 'quantityrequested', 'local_licensetracker');

        // Urgency level
        $urgencyoptions = array(
            'normal' => get_string('urgency_normal', 'local_licensetracker'),
            'high' => get_string('urgency_high', 'local_licensetracker'),
            'urgent' => get_string('urgency_urgent', 'local_licensetracker')
        );
        $mform->addElement('select', 'urgency', get_string('urgency', 'local_licensetracker'), $urgencyoptions);
        $mform->setDefault('urgency', 'normal');
        $mform->addHelpButton('urgency', 'urgency', 'local_licensetracker');

        // Additional notes
        $mform->addElement('textarea', 'notes', get_string('additionalnotes', 'local_licensetracker'), 
                          array('rows' => 4, 'cols' => 50));
        $mform->setType('notes', PARAM_TEXT);
        $mform->addHelpButton('notes', 'additionalnotes', 'local_licensetracker');

        // Hidden partner ID
        $mform->addElement('hidden', 'partnerid', $partner->id);
        $mform->setType('partnerid', PARAM_INT);

        // Action buttons
        $this->add_action_buttons(true, get_string('submitrequest', 'local_licensetracker'));
    }

    /**
     * Validate the form data.
     *
     * @param array $data Form data
     * @param array $files Form files
     * @return array Validation errors
     */
    public function validation($data, $files) {
        $errors = parent::validation($data, $files);

        // Validate quantity
        if (!empty($data['quantity'])) {
            if ($data['quantity'] < 1) {
                $errors['quantity'] = get_string('error:quantitytoosmall', 'local_licensetracker');
            } else if ($data['quantity'] > 10000) {
                $errors['quantity'] = get_string('error:quantitytoolarge', 'local_licensetracker');
            }
        }

        // Validate notes length
        if (!empty($data['notes']) && strlen($data['notes']) > 1000) {
            $errors['notes'] = get_string('error:notestoolong', 'local_licensetracker');
        }

        return $errors;
    }
}
