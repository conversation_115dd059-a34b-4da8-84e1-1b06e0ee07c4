<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Language strings for auth_licensetracker plugin.
 *
 * @package    auth_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['pluginname'] = 'License Tracker Authentication';
$string['auth_licensetrackerDescription'] = 'Authentication method that requires valid license keys for registration';

// Settings
$string['enabled'] = 'Enable license key authentication';
$string['enabled_desc'] = 'Enable license key validation during user registration';
$string['requirekeyforall'] = 'Require key for all students';
$string['requirekeyforall_desc'] = 'If enabled, all students must provide a valid license key during registration';
$string['localcountries'] = 'Local countries';
$string['localcountries_desc'] = 'Comma-separated list of country codes that do not require license keys (if "Require key for all students" is disabled)';

// Form fields
$string['licensekey'] = 'License Key';
$string['licensekey_help'] = 'Enter your license key to complete registration';
$string['licensekey_required'] = 'License key is required for registration';

// Validation messages
$string['invalidkey'] = 'Invalid license key provided';
$string['keynotfound'] = 'License key not found';
$string['keyalreadyused'] = 'This license key has already been used';
$string['keyexpired'] = 'This license key has expired';
$string['keynotactive'] = 'This license key is not yet active';
$string['keyrevoked'] = 'This license key has been revoked';
$string['registrationrequireskey'] = 'Registration requires a valid license key';

// Success messages
$string['registrationsuccess'] = 'Registration successful! You have been enrolled in the course.';
$string['enrollmentsuccess'] = 'You have been successfully enrolled in: {$a}';

// Errors
$string['error:coursenotfound'] = 'Course not found for this license key';
$string['error:enrollmentfailed'] = 'Failed to enroll user in course';
$string['error:invalidkeyformat'] = 'License key format is invalid';
$string['error:partnerlanguagefailed'] = 'Failed to set partner language preference';

// Additional validation messages
$string['licensekey_required'] = 'License key is required for registration';
$string['globalstudentregistration'] = 'Global student registration requires a license key';
